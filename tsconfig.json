{"compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "node", "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "noImplicitAny": false, "sourceMap": true, "baseUrl": ".", "outDir": "dist", "resolveJsonModule": true, "forceConsistentCasingInFileNames": true, "strict": false, "paths": {"*": ["src/types/*"], "@main/*": ["src/main/*"], "@types": ["src/renderer/types/index.ts"], "@preload/*": ["src/preload/*"], "@renderer/*": ["src/renderer/*"], "@shared/*": ["src/shared/*"]}, "typeRoots": ["node_modules/@types", "src/types", "src/renderer/types"], "lib": ["ESNext", "DOM", "DOM.Iterable"], "jsx": "react-jsx", "types": ["node", "electron"]}, "include": ["src/**/*", "src/**/*.tsx", "src/**/*.ts"], "exclude": ["node_modules"]}