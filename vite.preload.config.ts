import { defineConfig } from 'vite';
import path from 'path';

// https://vitejs.dev/config
export default defineConfig(({ mode }) => ({
  build: {
    outDir: '.vite/build',
    minify: mode === 'production',
    sourcemap: mode === 'development' ? 'inline' : false,
    lib: {
      entry: 'src/preload/index.ts',
      formats: ['cjs'],
      fileName: () => 'preload.js',
    },
    rollupOptions: {
      external: ['electron'],
    },
  },
  resolve: {
    alias: {
      '@src': path.resolve(__dirname, 'src')
    }
  },
}));
