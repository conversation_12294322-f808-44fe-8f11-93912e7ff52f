import { defineConfig } from 'vite';
import path from 'path';
import react from '@vitejs/plugin-react';

// https://vitejs.dev/config
export default defineConfig(({ mode }) => ({
  plugins: [
    react(),
  ],
  resolve: {
    alias: {
      '@src': path.resolve(__dirname, 'src')
    }
  },
  optimizeDeps: {
    // 强制预构建某些依赖
    include: [
      'react',
      'react-dom',
      'antd',
      '@ant-design/x',
      'axios',
      'zustand'
    ],
    // 排除一些可能有问题的依赖
    exclude: [
      'electron',
      'better-sqlite3',
      'canvas',
      'jsdom'
    ]
  },
  build: {
    rollupOptions: {
      input: {
        index: path.join(__dirname, 'src/renderer/index.html'),
      },
    },
    outDir: path.resolve(__dirname, '.vite/renderer/main_window'),
    emptyOutDir: false,
    // 在开发模式下强制使用 inline sourcemap 以获得更好的调试体验
    sourcemap: mode === 'development' ? 'inline' : false,
    minify: mode === 'production',
  },
  root: path.join(__dirname, 'src/renderer'),
  publicDir: path.join(__dirname, 'src/renderer/public'),
  server: {
    port: 5173,
    sourcemapIgnoreList: (sourcePath: string) => {
      // sourcePath is the absolute path to the source file
      // e.g., /path/to/project/node_modules/lucide-react/dist/esm/icons/index.js
      return sourcePath.includes('node_modules/lucide-react/');
    },
  },
}));
