# WebSocket 调试信息

## 问题现象
用户报告收到的数据：
```json
{"type": "tts", "state": "sentence_end", "session_id": "8038d05c-6d2a-4542-b494-b66b3180b29e", "text": "竹蜻蜓好像很有精神的样子呢！"}
{"type": "tts", "state": "sentence_start", "session_id": "8038d05c-6d2a-4542-b494-b66b3180b29e"}
二进制消息 65.3 kB
{"type": "tts", "state": "sentence_end", "session_id": "8038d05c-6d2a-4542-b494-b66b3180b29e"}
{"type": "tts", "state": "sentence_start", "session_id": "8038d05c-6d2a-4542-b494-b66b3180b29e"}
{"type": "tts", "state": "sentence_end", "session_id": "8038d05c-6d2a-4542-b494-b66b3180b29e"}
二进制消息 28.8 kB
{"type": "tts", "state": "stop", "session_id": "8038d05c-6d2a-4542-b494-b66b3180b29e"}
```

渲染的DOM:
```html
<div class="markdown-content">
  <p>😍😲🤔🤔😆😲😲😲😲🤔🤔🤔😲😆</p>
</div>
```

## 数据流分析

### 期望行为
1. 收到TTS消息带text字段 → 显示文本内容
2. 收到TTS消息不带text字段 → 不显示内容
3. 收到二进制音频数据 → 传递给音频播放器
4. 收到"stop"状态 → 标记对话完成

### 实际问题
显示了大量表情符号，而不是TTS文本内容

### 可能原因
1. TTS文本被错误处理
2. 情感状态被当作文本内容
3. 某个地方在累加表情符号
4. 消息ID管理错误导致重复处理

### 修复策略
1. ✅ 修改convertServerResponse：只有llm类型消息才设置content
2. ✅ 修改读取器：TTS文本单独处理并作为内容显示
3. ✅ 添加情感状态的单独处理分支
4. 🔄 添加更详细的调试日志

### 需要测试的场景
1. 纯TTS文本消息
2. 纯情感状态消息
3. 混合消息类型
4. 音频数据处理 