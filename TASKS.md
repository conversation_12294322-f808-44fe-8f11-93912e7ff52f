# Aido 快速启动器软件开发计划

基于PRD的需求分析，我们将开发一款集成AI和MCP功能的快速启动器软件。

## 需求概述

Aido (MCP)功能，具备以下特点：
- 无边框搜索栏界面，支持快速搜索应用、工具和文件
- AI对话功能，可通过LLM进行智能交互
- MCP工具集成，允许AI调用各种外部工具
- 插件系统，支持安装和管理各种MCP Server
- 多种激活方式，包括全局热键、鼠标和系统托盘

## 开发任务清单

### 基础架构

- [x] 需求分析和任务拆解
- [x] 设计技术栈和架构
- [x] 规划文件结构
- [x] 创建Electron项目架构
  - [x] 初始化Electron项目
  - [x] 配置构建系统(Vite)
  - [x] 设置开发环境(热重载等)
  - [x] 配置打包和分发脚本
- [x] 配置React和TypeScript环境
  - [x] 设置TypeScript配置
  - [x] 集成React框架
  - [x] 配置ESLint和Prettier
  - [ ] 设置单元测试框架
- [x] 集成部分UI组件库
  - [x] 集成shadcn/ui作为主界面组件库
  - [x] 配置TailwindCSS
  - [ ] 集成Ant Design X作为AI交互组件库
  - [ ] 创建全局样式系统，以便用户可以设置皮肤，一键换肤
  - [ ] 可将样式应用于所有组件上
  - [ ] 构建基础UI组件
- [ ] 设计全局异常处理机制
  - [ ] 区分系统异常和用户异常
  - [ ] 实现全局异常捕获和处理
  - [ ] 设计用户友好的错误提示机制
  - [ ] 创建日志记录系统
- [x] 实现国际化框架
  - [x] 集成i18next国际化库
  - [x] 创建中文和英文语言包
  - [x] 实现动态语言切换功能
  - [x] 设计多语言内容管理系统
- [x] 实现主题切换
  - [x] 设计亮色和暗色主题
  - [x] 实现主题切换逻辑
  - [x] 跟随系统主题设置
  - [x] 保存用户主题偏好

### 核心功能开发

- [x] 开发无边框主界面
  - [x] 实现无边框窗口
  - [ ] 添加窗口拖拽功能
  - [ ] 设计窗口显示/隐藏动画
  - [ ] 实现窗口位置记忆功能
- [x] 开发搜索栏
  - [x] 设计搜索栏UI
  - [x] 实现全局热键激活(opt+space)
  - [ ] 实现搜索栏焦点和键盘导航
  - [ ] 添加搜索历史功能
  - [ ] 集成右侧功能图标(工具使用、网络访问、深度思考、语音对话)
- [ ] 实现模糊搜索算法
  - [ ] 开发前缀匹配优先策略
  - [ ] 实现英文搜索功能
  - [ ] 实现中文搜索功能
  - [ ] 集成中文拼音搜索功能
  - [ ] 优化搜索性能
- [ ] 开发应用程序搜索功能
  - [ ] 实现系统应用程序扫描
  - [ ] 创建应用程序索引
  - [ ] 添加应用图标和信息提取
  - [ ] 实现最近使用排序
- [ ] 开发工具(Tool)搜索
  - [ ] 实现已安装Tool的扫描
  - [ ] 创建Tool索引和metadata提取
  - [ ] 设计Tool搜索结果UI
  - [ ] 实现Tool快速执行
- [ ] 开发文件检索功能
  - [ ] 开发文件系统索引功能
  - [ ] 实现文件搜索算法
  - [ ] 创建文件预览系统
  - [ ] 支持不同类型文件的预览
- [ ] 完善全局热键系统
  - [x] 实现全局热键监听
  - [ ] 支持自定义全局热键
  - [ ] 添加鼠标中键激活功能
  - [ ] 创建热键冲突检测和解决方案
- [ ] 实现系统托盘功能
  - [ ] 设计系统托盘图标
  - [ ] 实现托盘菜单
  - [ ] 添加托盘通知功能
  - [ ] 支持托盘快捷操作
- [ ] 开发快捷键管理
  - [ ] 实现应用/Tool快捷键设置UI
  - [ ] 创建快捷键存储系统
  - [ ] 实现tag显示快捷键信息
  - [ ] 添加快捷键修改功能

### AI和MCP集成

- [ ] 集成AI对话功能
  - [ ] 实现LLM API连接
  - [ ] 开发对话上下文管理
  - [ ] 设计AI对话界面
  - [ ] 添加流式响应功能
- [ ] 完善LLM对话功能
  - [ ] 优化对话上下文管理
  - [ ] 实现响应渲染和格式化
  - [ ] 添加对话历史管理
- [ ] 完善AI对话界面
  - [ ] 创建聊天历史记录组件
  - [ ] 开发对话输入组件
  - [ ] 实现消息气泡和样式
  - [ ] 添加消息状态和加载动画
- [ ] 集成MCP Client功能
  - [ ] 实现MCP Client接口
  - [ ] 开发MCP消息处理系统
  - [ ] 创建Tool调用代理
  - [ ] 实现调用结果处理
- [ ] 开发Tool执行引擎
  - [ ] 实现Tool调用框架
  - [ ] 创建参数验证系统
  - [ ] 开发执行结果处理
  - [ ] 添加执行状态跟踪
- [ ] 实现MCP Server管理
  - [ ] 开发MCP Server注册系统
  - [ ] 实现Server生命周期管理
  - [ ] 创建Server状态监控
  - [ ] 添加服务器诊断工具
- [ ] 实现工具调用权限管理
  - [ ] 开发工具使用开关
  - [ ] 实现网络访问控制
  - [ ] 添加深度思考模式
  - [ ] 集成语音对话功能

### 插件系统

- [ ] 设计MCP Server插件架构
  - [ ] 定义插件规范和接口
  - [ ] 创建插件加载机制
  - [ ] 实现插件间依赖管理
  - [ ] 开发插件沙箱环境
- [ ] 开发插件管理界面
  - [ ] 设计插件列表和详情UI
  - [ ] 实现插件激活/禁用功能
  - [ ] 添加插件设置界面
  - [ ] 创建插件卸载流程
- [ ] 实现插件商城功能
  - [ ] 设计插件商城UI
  - [ ] 开发插件搜索和过滤
  - [ ] 实现插件下载和安装
  - [ ] 添加插件更新机制
- [ ] 开发本地协议URL处理
  - [ ] 实现自定义协议注册
  - [ ] 创建URL解析系统
  - [ ] 开发一键安装流程
  - [ ] 添加安装确认和权限控制
- [ ] 设计Tool动态UI生成器
  - [ ] 基于inputSchema创建UI组件
  - [ ] 实现表单验证
  - [ ] 开发动态布局系统
  - [ ] 支持不同类型的输入控件

### 运行时环境

- [ ] 集成bun运行环境
  - [ ] 内置bun运行时
  - [ ] 创建bun CLI封装
  - [ ] 实现npm包形式MCP Server执行
  - [ ] 开发依赖管理系统
- [ ] 集成portable Python
  - [ ] 内置Python解释器
  - [ ] 创建Python环境管理
  - [ ] 实现Python MCP Server执行
  - [ ] 开发包管理系统
- [ ] 集成uv/uvx
  - [ ] 内置uv/uvx工具
  - [ ] 创建虚拟环境管理
  - [ ] 实现Python包安装
  - [ ] 开发依赖解析系统
- [ ] 开发环境自动配置
  - [ ] 实现环境检测
  - [ ] 创建自动配置流程
  - [ ] 开发环境隔离机制
  - [ ] 添加环境诊断工具

### 状态条和优化测试

- [ ] 实现状态条
  - [ ] 设计状态条UI
  - [ ] 实现错误信息显示
  - [ ] 开发右侧快捷操作
  - [ ] 为不同选中对象提供个性化操作
- [ ] 性能优化
  - [ ] 优化启动时间
  - [ ] 改进搜索性能
  - [ ] 优化内存使用
  - [ ] 减少UI渲染开销
- [ ] 自动化测试
  - [ ] 创建单元测试
  - [ ] 实现集成测试
  - [ ] 开发端到端测试
  - [ ] 设置持续集成
- [ ] 用户体验测试
  - [ ] 进行可用性测试
  - [ ] 收集用户反馈
  - [ ] 优化交互流程
  - [ ] 改进错误处理
- [ ] 发布准备
  - [ ] 完成应用打包
  - [ ] 实现自动更新
  - [ ] 创建安装程序
  - [ ] 准备文档和帮助

## 实施计划

### 技术架构

- 前端：Electron + React + TypeScript
- UI组件：shadcn/ui (主界面) + Ant Design X (AI交互)
- 后端服务：Node.js + MCP Server架构
- 内置运行时：bun, portable Python, uv/uvx

### 关键文件结构

- `/src` - 源代码目录
  - `/main` - Electron主进程代码
    - `/services` - 主进程服务层代码
    - `/utils` - 主进程工具函数
  - `/renderer` - 渲染进程代码
    - `/components` - React组件
    - `/hooks` - 自定义React Hooks
    - `/utils` - 渲染进程工具函数
    - `/services` - 渲染进程服务层代码
    - `/stores` - 状态管理
    - `/i18n` - 国际化资源
    - `/styles` - 样式文件
    - `/pages` - 页面组件
    - `/assets` - 静态资源（图标、图片等）
  - `/preload` - 预加载脚本（连接主进程和渲染进程）
  - `/common` - 主进程和渲染进程共享的代码
- `/public` - 打包时直接复制的静态资源
- `/scripts` - 构建和开发脚本
- `/runtime` - 内置运行时环境
- `/plugins` - 插件系统相关代码

### 开发流程

1. 搭建基础项目架构
2. 实现无边框窗口和搜索栏
3. 开发搜索和匹配算法
4. 实现应用程序/文件检索功能
5. 集成MCP客户端和AI对话功能
6. 开发插件系统和工具调用功能
7. 优化UI/UX和性能
8. 测试和发布
