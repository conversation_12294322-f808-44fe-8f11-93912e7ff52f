开发一款快速启动器（quick launcher）软件，区别与传统快速启动器，最大的特色是集成了AI和MCP功能，既具备传统launcher软件的功能，还可直接进行AI对话，同时AI可借助MCP来调用已经安装的各种工具，快速执行任务（注意：MCP是指modelcontextprotocol，用来供大模型自主决策调用什么外部工具和服务的协议，如果你不清楚是什么，可参考README.md和@https://modelcontextprotocol.io/introduction ）。
一个MCP Server中有若干个Tool，每个Tool在本软件中都是一个可以独立执行的工具。
本软件自身就是一个MCP Client，可以调用大语言模型，根据模型返回的调用指令来调用对应的Tool。
  
软件具备以下特点：  
# 核心功能与交互逻辑  
- 主界面是一个搜索栏，无窗体，无标题栏；  
- 搜索栏中可以输入文字，输入后会搜索本机的应用程序或者已安装的Tool；  
- 搜索结果在搜索栏下方展开，以列表方式呈现，一行一个，默认选中第一个，可通过键盘上下键快速导航，回车执行，也可鼠标点选某行执行；  
- 如果用户输入的文字匹配不到精准的本机应用程序或已安装的Tool，则搜索结果的第一项为“Rapid AI”（中文“快速AI”），回车后调用LLM进行对话，窗口切换为AI对话状态，窗口左侧是历史聊天记录，右侧是对话区；  
- 如果在搜索栏中输入时，第一个字输入的是空格，则自动进入搜索本机文件的状态，窗口切换为文件检索状态，左侧显示文件搜索结果，右侧显示当前选中文件的预览；  
- 搜索栏的右侧有4个图标，分别是工具使用（默认激活），网络访问（默认关闭），深度思考（默认关闭），语音对话（点击后激活）；  
- 搜索结果列表中，每一行的左侧是应用程序或者Tool的名称，右侧是设置快捷键的图标，点击可设置快捷键，已经设置过快捷键的应用则不显示快捷键图标，而是用tag显示出快捷键是什么，点击tag后可以修改快捷键；
  
# 软件主界面激活方式
- 全局热键激活（如alt/opt+space或双击ctrl/cmd）  
- 鼠标中键激活  
- 系统托盘图标点击激活  
  
# 搜索与匹配算法
- 模糊搜索算法，可搜索出包含输入内容的应用程序或Tool，但是采用前缀匹配优先策略
- 最近使用过的排序优先，但是优先级不如前缀匹配
- 需要支持英文、中文及中文拼音的模糊搜索

# 插件管理
- 可通过软件内置的应用商城安装插件（插件即MCP Server），安装好后其包含的Tool便可以被搜索到
- 另外在一些MCP Server的聚合网站上可通过本地协议的url拉起本软件，一键安装MCP Server
- 软件可以管理已安装的插件（插件即MCP Server），可激活、临时禁用或卸载插件

# 多种使用工具的方法
- AI对话时如果开启了工具调用，则激活所有已经安装的MCP Server，根据模型返回的调用指令来调用对应的Tool
- 每个Tool在搜索结果列表中点击时，可以直接执行，执行时需要根据这个Tool的inputSchema来动态生成界面中的交互组件

# 状态条
- 本软件界面的最下方有一个状态条，状态条的左侧显示软件小logo，执行当前应用程序或Tool时，如发生用户异常，则在logo右侧显示错误信息给用户；
- 本软件需要设计统一的全局异常机制，包含系统异常和用户异常等（系统异常不需要展示给用户，用户异常是需要展示给用户的）
- 状态条的右侧是快捷操作，可以根据当前选中的应用程序或文件给出个性化操作，比如当前选中了一个应用程序，则快捷操作为打开、结束运行、打开所在目录、卸载等，如当前选中文件则为打开、打开所在目录、重命名、删除等；

# 开发注意事项
- 本软件使用Electron开发
- 需要自带bun的cli，以便能在用户电脑没有安装nodejs时，顺利执行以npm包形式发布的mcp server
- 需要自带portable python和uv、uvx的cli，以便能在用户电脑没有安装python时，顺利执行python开发的mcp server
- 本软件前端使用React开发
- 本软件前端UI库使用shadcn/ui
- 本软件前端开发AI交互时，使用Ant Design X作为组件库 @https://x.ant.design/components/overview-cn
- 本软件需要实现国际化
- 本软件需要支持亮色暗色模式切换