# WebSocket 聊天功能实现

## 概述

这个实现将原有的HTTP API聊天方式改为WebSocket方式，连接到指定的WebSocket服务器 `ws://**************:8010/`。模型选择由后端处理，前端不再需要传递模型信息。

## 核心组件

### 1. WebSocketChatService (src/renderer/llm/services/WebSocketChatService.ts)

主要的WebSocket聊天服务类，继承自 `NextChatService`：

- **WebSocketManager**: 管理WebSocket连接，包含重连机制、消息队列、心跳检测
- **WebSocketChatReader**: 模拟流式读取器接口，处理WebSocket消息
- **WebSocketChatService**: 主要服务类，实现聊天逻辑

#### 主要特性：
- ✅ 自动重连机制（最多5次重连）
- ✅ 消息队列管理（连接断开时缓存消息）
- ✅ 流式内容处理
- ✅ Reasoning内容支持（深度思考）
- ✅ 工具调用支持
- ✅ 错误处理和恢复

### 2. 修改的ChatServiceFactory

在 `src/renderer/llm/services/ChatServiceFactory.ts` 中添加了WebSocket支持：

```typescript
case 'websocket':
case 'WebSocket':
  return WebSocketChatService.getInstance(context);

default:
  // 默认使用WebSocket服务
  return WebSocketChatService.getInstance(context);
```

### 3. 更新的AI聊天组件

在 `src/renderer/components/ai-chat.tsx` 中强制使用WebSocket模式：

- 移除了复杂的provider和model选择逻辑
- 直接使用WebSocket配置
- 保持所有现有功能（reasoning、工具调用、文件路径处理等）

## WebSocket 消息协议

### 请求消息格式
```typescript
{
  type: 'listen',
  id: string,
  state: 'detect',
  text: string,              // 用户输入的文本
  source: 'text'             // 输入源类型
}
```

### 响应消息格式

#### LLM文本响应
```typescript
{
  type: 'llm',
  text: string,              // AI生成的文本内容（表情符号）
  emotion: 'surprised' | 'thinking' | 'happy' | 'sad' | 'angry' | 'neutral',
  session_id: string
}
```

#### TTS语音响应
```typescript
{
  type: 'tts',
  state: 'sentence_start' | 'sentence_end' | 'stop',
  session_id: string,
  text?: string              // 要朗读的文本（可选）
}
```

#### 二进制音频数据
```
Binary Message (ArrayBuffer) - 音频数据片段
```

## 配置说明

### WebSocket连接地址
```
ws://**************:8010/
```

### Provider类型
新增了 `WebSocket` 类型到 `ProviderType`：

```typescript
export type ProviderType = 
  | 'OpenAI'
  | 'Google'
  // ... 其他类型
  | 'WebSocket';
```

## 功能特性

### ✅ 已实现的功能
1. **基础聊天**: 支持基本的问答对话
2. **流式响应**: 实时显示生成的内容
3. **多模态支持**: 
   - **情感表达**: 实时显示AI的情感状态（惊讶、思考、开心等）
   - **语音合成**: 支持TTS语音输出
   - **音频播放**: 内置音频播放器，支持播放、暂停、进度控制
4. **深度思考**: 支持reasoning内容的显示和管理
5. **工具调用**: 支持MCP工具的调用和结果显示
6. **文件路径处理**: 自动识别和处理文件路径
7. **代码高亮**: 支持代码块高亮和复制
8. **Mermaid图表**: 支持Mermaid图表渲染
9. **连接管理**: 自动重连、心跳检测
10. **错误处理**: 完善的错误处理和用户反馈

### 🔄 保留的原有功能
- 消息历史管理
- 工具调用历史展示
- Reasoning内容展开/收起
- 文件路径按钮交互
- 主题切换支持
- 国际化支持

## 使用方法

### 1. 直接使用AI聊天组件
```tsx
import { AiChat } from '../components/ai-chat';

<AiChat
  query=""
  onComplete={() => console.log('完成')}
/>
```

### 2. 使用测试页面
访问 `WebSocketChatTest` 组件（位于 `src/renderer/pages/websocket-chat-test.tsx`）：

```tsx
import WebSocketChatTest from '../pages/websocket-chat-test';

// 提供了完整的聊天界面和快捷操作按钮
```

## 🎭 多模态功能说明

### 情感表达系统
AI会根据对话内容和语境显示相应的表情：
- 😲 **惊讶** (`surprised`) - 遇到意外信息时
- 🤔 **思考** (`thinking`) - 正在分析问题时  
- 😊 **开心** (`happy`) - 积极回应或帮助用户时
- 😢 **难过** (`sad`) - 遇到不好的消息时
- 😠 **生气** (`angry`) - 遇到问题或错误时
- 😐 **中性** (`neutral`) - 默认状态

### 语音合成和播放
1. **TTS文本处理**: 服务器返回的文本会自动转换为语音
2. **音频流播放**: 支持实时音频流播放
3. **播放控制**: 
   - ▶️ 播放/⏸️ 暂停切换
   - 进度条显示和拖拽控制
   - 音频时长和当前时间显示
   - 音量指示器

### 实时交互特性
- **情感同步**: 表情状态随AI回复实时更新
- **语音同步**: 文本生成的同时开始语音合成
- **多片段音频**: 支持长对话的分段音频播放
- **状态管理**: 完整的播放状态管理和错误处理

## 测试功能

创建了专门的测试页面 `WebSocketChatTest`，包含：

- 完整的聊天界面
- 消息输入框
- 快捷操作按钮（问候、文件操作、天气查询、代码生成等）
- 连接状态显示

## 技术细节

### WebSocket连接管理
- 使用单例模式管理全局WebSocket连接
- 实现指数退避重连策略
- 支持消息队列缓存
- 心跳检测机制

### 消息流处理
- 支持增量内容更新
- 分离reasoning和普通内容
- 工具调用状态实时更新
- 完成状态检测

### 错误处理
- 连接错误自动重连
- 消息解析错误处理
- 用户友好的错误提示
- 服务降级机制

## 部署注意事项

1. **WebSocket服务器**: 确保 `ws://**************:8010/` 可访问
2. **防火墙**: 确保8010端口开放
3. **协议支持**: 确保服务器支持定义的消息协议
4. **CORS**: 确保WebSocket服务器允许跨域连接

## 后续优化建议

1. **配置化**: 将WebSocket地址配置化，支持用户自定义
2. **连接状态**: 添加更详细的连接状态指示器
3. **重连策略**: 支持手动重连和连接状态重置
4. **消息持久化**: 支持离线消息缓存和恢复
5. **性能优化**: 大消息分片处理和压缩支持

## 兼容性

- ✅ 保持与原有AI聊天组件的完全兼容
- ✅ 支持所有现有功能（reasoning、工具调用等）
- ✅ 无需修改现有调用代码
- ✅ 向前兼容，可随时切换回HTTP模式 