import { registerAppHandlers } from './handlers/app-handler';
import { registerFileHandlers } from './handlers/file-handler';
import { registerWindowHandlers } from './handlers/window-handler';
import { registerDBHandlers } from './handlers/db-handler';
import { registerMcpHandlers } from './handlers/mcp-handler';
import { registerStoreHandlers } from './handlers/store-handler';
import { registerClipboardHandlers } from './handlers/clipboard-handler';
import { registerPetHandlers } from './handlers/pet-handler';
import { registerProxyHandlers } from './handlers/proxy-handler';
import { registerShortcutHandlers } from './handlers/shortcut-handler';
import { registerSystemHandlers } from './handlers/system-handler';
import { registerAuthHandlers } from './handlers/auth-handler';
import { registerSettingsHandlers } from './handlers/settings-handler';
import { registerCrossWindowHandlers } from './handlers/cross-window-handler';
import { registerScreenCaptureHandlers } from './handlers/screen-capture-handler';

import { registerAppEmitters } from './emitters/app-emitter';
import { registerClipboardEmitters } from './emitters/clipboard-emitter';

// 全局标志，用于防止重复注册
let isIpcHandlersRegistered = false;

/**
 * 注册所有IPC处理程序和事件发射器
 */
export function registerAllIpcHandlers(): void {
  // 如果已经注册过，则直接返回
  if (isIpcHandlersRegistered) {
    console.log('IPC处理程序已注册，跳过重复注册');
    return;
  }

  try {
    // 注册各种处理程序
    const handlers = [
      registerAppHandlers,
      registerFileHandlers,
      registerWindowHandlers,
      registerDBHandlers,
      registerMcpHandlers,
      registerStoreHandlers,
      registerAuthHandlers,
      registerClipboardHandlers,
      registerPetHandlers,
      registerShortcutHandlers,
      registerProxyHandlers,
      registerSystemHandlers,
      registerSettingsHandlers,
      registerCrossWindowHandlers,
      registerScreenCaptureHandlers,
    ];
    
    // 注册发射器
    const emitters = [
      registerAppEmitters,
      registerClipboardEmitters,
    ];
    
    // 执行所有处理程序注册
    handlers.forEach(handler => handler());
    
    // 执行所有发射器注册
    emitters.forEach(emitter => emitter());

    // 标记为已注册
    isIpcHandlersRegistered = true;
    console.log('所有IPC处理程序和事件发射器注册成功');
  } catch (error) {
    console.error('注册IPC模块失败:', error);
  }
}