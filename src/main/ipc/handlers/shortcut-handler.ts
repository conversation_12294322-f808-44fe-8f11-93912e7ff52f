import { ipcMain, globalShortcut } from 'electron';
import { SHORTCUT } from '../../../shared/ipc';
import { dbService } from '../../services/dbService';
import { appService } from '../../services/appService';
import { WindowManager } from '../../windows/window-manager';
import { settingsService } from '../../services/settingsService';
import { systemShortcutChecker } from '../../../shared/system-shortcuts';
import { clipboardService } from '../../services/clipboardService';
import * as log from '../../logging';

// 存储应用快捷键的映射
const appShortcuts = new Map<string, {
  id: string;
  shortcut: string;
  path: string;
  name: string;
}>();

// 存储内置功能快捷键的映射
const builtinShortcuts = new Map<string, {
  id: string;
  shortcut: string;
  name: string;
  action: () => Promise<void>;
}>();

// 存储被禁用的快捷键，用于恢复
const disabledShortcuts = new Map<string, {
  id: string;
  shortcut: string;
  path: string;
  name: string;
}>();

// 存储被禁用的内置功能快捷键，用于恢复
const disabledBuiltinShortcuts = new Map<string, {
  id: string;
  shortcut: string;
  name: string;
  action: () => Promise<void>;
}>();

/**
 * 将用户输入的快捷键格式转换为系统可识别的格式（仅用于注册）
 * @param shortcut 用户输入的快捷键字符串
 * @returns 系统可识别的快捷键字符串，如果无效则返回null
 */
function convertForSystemRegistration(shortcut: string): string | null {
  try {
    // 如果为空或不是字符串，返回null
    if (!shortcut || typeof shortcut !== 'string') {
      log.debug('快捷键为空或不是字符串');
      return null;
    }
    
    log.debug('开始转换快捷键用于系统注册:', shortcut);
    
    // 转换为系统可识别的格式
    const converted = shortcut
      .replace(/\s+/g, '') // 移除空格
      .replace(/Cmd/g, 'Command') // Cmd -> Command
      .replace(/Option/g, 'Alt') // Option -> Alt（Mac特有，转换为系统标准）
      .replace(/Ctrl/g, 'Control'); // Ctrl -> Control
    
    log.debug('转换后的系统格式:', converted);
    
    // 定义修饰键列表
    const modifiers = ['Command', 'Control', 'Alt', 'Shift', 'Super'];
    const parts = converted.split('+');
    
    log.debug('快捷键部分:', parts);
    
    // 处理双击情况 (例如: "Command+Command")
    if (parts.length === 2 && parts[0] === parts[1] && modifiers.includes(parts[0])) {
      log.debug('检测到修饰键双击:', converted);
      return converted;
    }
    
    // 检查是否有非修饰键
    const lastPart = parts[parts.length - 1];
    const hasNonModifier = !modifiers.includes(lastPart);
    
    // 检查是否有修饰键
    const hasModifier = parts.slice(0, -1).some(part => modifiers.includes(part));
    
    log.debug('有修饰键:', hasModifier, '有非修饰键:', hasNonModifier);
    
    // 验证组合：至少有一个修饰键和一个非修饰键
    if (!hasModifier || !hasNonModifier) {
      // 特殊情况：单个功能键可以不需要修饰键
      const functionKeys = ['F1', 'F2', 'F3', 'F4', 'F5', 'F6', 'F7', 'F8', 'F9', 'F10', 'F11', 'F12'];
      if (parts.length === 1 && functionKeys.includes(parts[0])) {
        log.debug('单个功能键快捷键有效:', converted);
        return converted;
      }
      
      log.warn('快捷键格式无效:', shortcut);
      return null;
    }
    
    log.debug('快捷键转换成功:', shortcut, '->', converted);
    return converted;
  } catch (error) {
    log.error('转换快捷键格式时出错:', error);
    return null;
  }
}

/**
 * 注册快捷键相关的 IPC 处理程序
 */
export function registerShortcutHandlers(): void {
  
  // 检查快捷键是否可用
  ipcMain.handle(SHORTCUT.CHECK_AVAILABLE, async (_, shortcut: string): Promise<boolean> => {
    try {
      // 转换为系统可识别的格式
      const systemShortcut = convertForSystemRegistration(shortcut);
      if (!systemShortcut) {
        log.debug('快捷键格式无效:', shortcut);
        return false;
      }

      // 检查内部冲突（应用快捷键、内置功能快捷键、剪贴板快捷键、系统快捷键）
      const conflict = await checkShortcutConflictAsync(shortcut);
      if (conflict) {
        const conflictTypeMap = {
          'app': '应用',
          'builtin': '内置功能',
          'clipboard': '剪贴板',
          'system': '系统'
        };
        const conflictTypeName = conflictTypeMap[conflict.type as keyof typeof conflictTypeMap] || conflict.type;
        log.debug(`快捷键与${conflictTypeName}冲突:`, shortcut, '名称:', conflict.name);
        return false;
      }

      // 检查是否已被全局注册
      const isRegistered = globalShortcut.isRegistered(systemShortcut);
      if (isRegistered) {
        log.debug('快捷键已被注册:', systemShortcut);
        return false;
      }

      // 尝试临时注册以检查是否可用
      const canRegister = globalShortcut.register(systemShortcut, () => {
        // 临时空回调
      });
      if (canRegister) {
        // 立即注销临时注册
        globalShortcut.unregister(systemShortcut);
        log.debug('快捷键可用:', systemShortcut);
        return true;
      } else {
        log.debug('快捷键不可用:', systemShortcut);
        return false;
      }
    } catch (error) {
      log.error('检查快捷键可用性失败:', error);
      return false;
    }
  });

  // 注册应用快捷键
  ipcMain.handle(SHORTCUT.REGISTER_APP, async (_, data: {
    id: string;
    shortcut: string;
    path: string;
    name: string;
  }): Promise<boolean> => {
    try {
      const { id, shortcut, path, name } = data;
      
      // 转换为系统可识别的格式（仅用于注册）
      const systemShortcut = convertForSystemRegistration(shortcut);
      if (!systemShortcut) {
        log.error('快捷键格式无效:', shortcut);
        return false;
      }
      
      // 检查快捷键冲突
      const conflict = await checkShortcutConflictAsync(shortcut, id, 'app');
      if (conflict) {
        const conflictTypeMap = {
          'app': '应用',
          'builtin': '内置功能',
          'clipboard': '剪贴板',
          'system': '系统'
        };
        const conflictTypeName = conflictTypeMap[conflict.type as keyof typeof conflictTypeMap] || conflict.type;
        log.error(`快捷键已被${conflictTypeName}使用:`, shortcut, '名称:', conflict.name);
        return false;
      }
      
      // 如果该应用已有快捷键，先注销
      const existingShortcut = appShortcuts.get(id);
      if (existingShortcut) {
        const existingSystemShortcut = convertForSystemRegistration(existingShortcut.shortcut);
        if (existingSystemShortcut) {
          globalShortcut.unregister(existingSystemShortcut);
          await new Promise(resolve => setTimeout(resolve, 50));
        }
      }

      // 检查快捷键是否已被全局注册
      if (globalShortcut.isRegistered(systemShortcut)) {
        log.error('快捷键已被系统或其他软件占用:', systemShortcut);
        return false;
      }

      // 注册新快捷键（使用系统格式）
      const registered = globalShortcut.register(systemShortcut, async () => {
        log.info('快捷键触发:', systemShortcut, '启动应用:', name);
        try {
          const success = await appService.launchApp(path);
          if (!success) {
            log.error('应用启动失败:', name);
          }
        } catch (error) {
          log.error('通过快捷键启动应用失败:', error);
        }
      });

      if (registered) {
        // 保存到内存（使用原始格式）
        appShortcuts.set(id, { id, shortcut, path, name });
        
        try {
          // 保存到数据库（使用原始格式）
          await saveShortcutToDatabase(id, shortcut, path, name);
          log.info('快捷键注册成功:', shortcut, '用于应用:', name);
          return true;
        } catch (dbError) {
          // 数据库保存失败，回滚快捷键注册
          globalShortcut.unregister(systemShortcut);
          appShortcuts.delete(id);
          log.error('保存快捷键到数据库失败:', dbError);
          return false;
        }
      } else {
        log.error('快捷键注册失败:', systemShortcut);
        return false;
      }
    } catch (error) {
      log.error('注册应用快捷键失败:', error);
      return false;
    }
  });

  // 注销应用快捷键
  ipcMain.handle(SHORTCUT.UNREGISTER_APP, async (_, id: string): Promise<boolean> => {
    try {
      const shortcutData = appShortcuts.get(id);
      if (!shortcutData) {
        log.warn('未找到应用快捷键:', id);
        return false;
      }

      // 转换为系统格式进行注销
      const systemShortcut = convertForSystemRegistration(shortcutData.shortcut);
      if (systemShortcut) {
        globalShortcut.unregister(systemShortcut);
      }
      
      // 从内存中移除
      appShortcuts.delete(id);
      
      // 从数据库中删除
      await removeShortcutFromDatabase(id);
      
      log.info('快捷键注销成功:', shortcutData.shortcut);
      return true;
    } catch (error) {
      log.error('注销应用快捷键失败:', error);
      return false;
    }
  });

  // 获取所有应用快捷键
  ipcMain.handle(SHORTCUT.GET_APP_SHORTCUTS, async (): Promise<Array<{
    id: string;
    shortcut: string;
    path: string;
    name: string;
  }>> => {
    try {
      return Array.from(appShortcuts.values());
    } catch (error) {
      log.error('获取应用快捷键失败:', error);
      return [];
    }
  });

  // 注册内置功能快捷键
  ipcMain.handle(SHORTCUT.REGISTER_BUILTIN, async (_, data: {
    id: string;
    shortcut: string;
    name: string;
  }): Promise<boolean> => {
    try {
      const { id, shortcut, name } = data;

      // 转换为系统可识别的格式（仅用于注册）
      const systemShortcut = convertForSystemRegistration(shortcut);
      if (!systemShortcut) {
        log.error('内置功能快捷键格式无效:', shortcut);
        return false;
      }

      // 检查快捷键冲突
      const conflict = await checkShortcutConflictAsync(shortcut, id, 'builtin');
      if (conflict) {
        const conflictTypeMap = {
          'app': '应用',
          'builtin': '内置功能',
          'clipboard': '剪贴板',
          'system': '系统'
        };
        const conflictTypeName = conflictTypeMap[conflict.type as keyof typeof conflictTypeMap] || conflict.type;
        log.error(`快捷键已被${conflictTypeName}使用:`, shortcut, '名称:', conflict.name);
        return false;
      }

      // 如果该内置功能已有快捷键，先注销
      const existingShortcut = builtinShortcuts.get(id);
      if (existingShortcut) {
        const existingSystemShortcut = convertForSystemRegistration(existingShortcut.shortcut);
        if (existingSystemShortcut) {
          globalShortcut.unregister(existingSystemShortcut);
          await new Promise(resolve => setTimeout(resolve, 50));
        }
      }

      // 检查快捷键是否已被全局注册
      if (globalShortcut.isRegistered(systemShortcut)) {
        log.error('快捷键已被系统或其他软件占用:', systemShortcut);
        return false;
      }

      // 创建内置功能的执行动作
      const action = createBuiltinAction(id);
      if (!action) {
        log.error('未知的内置功能ID:', id);
        return false;
      }

      // 注册新快捷键（使用系统格式）
      const registered = globalShortcut.register(systemShortcut, async () => {
        log.info('内置功能快捷键触发:', systemShortcut, '功能:', name);
        try {
          await action();
        } catch (error) {
          log.error('通过快捷键执行内置功能失败:', error);
        }
      });

      if (registered) {
        // 保存到内存（使用原始格式）
        builtinShortcuts.set(id, { id, shortcut, name, action });

        try {
          // 保存到数据库（使用原始格式）
          await saveBuiltinShortcutToDatabase(id, shortcut, name);
          log.info('内置功能快捷键注册成功:', shortcut, '功能:', name);
          return true;
        } catch (dbError) {
          // 数据库保存失败，回滚快捷键注册
          globalShortcut.unregister(systemShortcut);
          builtinShortcuts.delete(id);
          log.error('保存内置功能快捷键到数据库失败:', dbError);
          return false;
        }
      } else {
        log.error('内置功能快捷键注册失败:', systemShortcut);
        return false;
      }
    } catch (error) {
      log.error('注册内置功能快捷键失败:', error);
      return false;
    }
  });

  // 注销内置功能快捷键
  ipcMain.handle(SHORTCUT.UNREGISTER_BUILTIN, async (_, id: string): Promise<boolean> => {
    try {
      const shortcutData = builtinShortcuts.get(id);
      if (!shortcutData) {
        log.warn('未找到内置功能快捷键:', id);
        return false;
      }

      // 转换为系统格式进行注销
      const systemShortcut = convertForSystemRegistration(shortcutData.shortcut);
      if (systemShortcut) {
        globalShortcut.unregister(systemShortcut);
      }

      // 从内存中移除
      builtinShortcuts.delete(id);

      // 从数据库中删除
      await removeBuiltinShortcutFromDatabase(id);

      log.info('内置功能快捷键注销成功:', shortcutData.shortcut);
      return true;
    } catch (error) {
      log.error('注销内置功能快捷键失败:', error);
      return false;
    }
  });

  // 获取所有内置功能快捷键
  ipcMain.handle(SHORTCUT.GET_BUILTIN_SHORTCUTS, async (): Promise<Array<{
    id: string;
    shortcut: string;
    name: string;
  }>> => {
    try {
      return Array.from(builtinShortcuts.values()).map(({ id, shortcut, name }) => ({
        id, shortcut, name
      }));
    } catch (error) {
      log.error('获取内置功能快捷键失败:', error);
      return [];
    }
  });

  // 禁用所有快捷键（设置快捷键时用）
  ipcMain.handle(SHORTCUT.DISABLE_ALL, async (): Promise<boolean> => {
    try {
      // 保存当前应用快捷键状态到禁用列表
      disabledShortcuts.clear();
      for (const [id, shortcutData] of appShortcuts) {
        disabledShortcuts.set(id, { ...shortcutData });
      }

      // 保存当前内置功能快捷键状态到禁用列表
      disabledBuiltinShortcuts.clear();
      for (const [id, shortcutData] of builtinShortcuts) {
        disabledBuiltinShortcuts.set(id, { ...shortcutData });
      }

      // 注销应用快捷键
      for (const [id, shortcutData] of appShortcuts) {
        const systemShortcut = convertForSystemRegistration(shortcutData.shortcut);
        if (systemShortcut) {
          globalShortcut.unregister(systemShortcut);
        }
      }

      // 注销内置功能快捷键
      for (const [id, shortcutData] of builtinShortcuts) {
        const systemShortcut = convertForSystemRegistration(shortcutData.shortcut);
        if (systemShortcut) {
          globalShortcut.unregister(systemShortcut);
        }
      }

      log.debug('已禁用快捷键，应用:', disabledShortcuts.size, '内置功能:', disabledBuiltinShortcuts.size);
      return true;
    } catch (error) {
      log.error('禁用快捷键失败:', error);
      return false;
    }
  });

  // 恢复所有快捷键
  ipcMain.handle(SHORTCUT.RESTORE_ALL, async (): Promise<boolean> => {
    try {
      // 延迟确保之前的注销完成
      await new Promise(resolve => setTimeout(resolve, 100));

      let appSuccessCount = 0;
      let builtinSuccessCount = 0;

      // 重新注册所有应用快捷键
      for (const [id, shortcutData] of appShortcuts) {
        const { shortcut, path, name } = shortcutData;

        // 转换为系统可识别的格式
        const systemShortcut = convertForSystemRegistration(shortcut);
        if (!systemShortcut) {
          log.warn('应用快捷键格式无效，跳过恢复:', shortcut);
          continue;
        }

        // 先检查是否已注册，如果是则注销
        if (globalShortcut.isRegistered(systemShortcut)) {
          globalShortcut.unregister(systemShortcut);
          await new Promise(resolve => setTimeout(resolve, 50));
        }

        // 重新注册快捷键
        const registered = globalShortcut.register(systemShortcut, async () => {
          log.info('快捷键触发:', systemShortcut, '启动应用:', name);
          try {
            const success = await appService.launchApp(path);
            if (!success) {
              log.error('应用启动失败:', name);
            }
          } catch (error) {
            log.error('通过快捷键启动应用失败:', error);
          }
        });

        if (registered) {
          appSuccessCount++;
        } else {
          log.error('应用快捷键恢复失败:', systemShortcut);
        }
      }

      // 重新注册所有内置功能快捷键
      for (const [id, shortcutData] of builtinShortcuts) {
        const { shortcut, name, action } = shortcutData;

        // 转换为系统可识别的格式
        const systemShortcut = convertForSystemRegistration(shortcut);
        if (!systemShortcut) {
          log.warn('内置功能快捷键格式无效，跳过恢复:', shortcut);
          continue;
        }

        // 先检查是否已注册，如果是则注销
        if (globalShortcut.isRegistered(systemShortcut)) {
          globalShortcut.unregister(systemShortcut);
          await new Promise(resolve => setTimeout(resolve, 50));
        }

        // 重新注册快捷键
        const registered = globalShortcut.register(systemShortcut, async () => {
          log.info('内置功能快捷键触发:', systemShortcut, '功能:', name);
          try {
            await action();
          } catch (error) {
            log.error('通过快捷键执行内置功能失败:', error);
          }
        });

        if (registered) {
          builtinSuccessCount++;
        } else {
          log.error('内置功能快捷键恢复失败:', systemShortcut);
        }
      }

      disabledShortcuts.clear();
      disabledBuiltinShortcuts.clear();
      log.debug('快捷键恢复完成，应用成功:', appSuccessCount, '/', appShortcuts.size, '内置功能成功:', builtinSuccessCount, '/', builtinShortcuts.size);
      return (appSuccessCount > 0 || appShortcuts.size === 0) && (builtinSuccessCount > 0 || builtinShortcuts.size === 0);
    } catch (error) {
      log.error('恢复快捷键失败:', error);
      return false;
    }
  });
}

/**
 * 保存快捷键到数据库
 */
async function saveShortcutToDatabase(id: string, shortcut: string, path: string, name: string): Promise<void> {
  const sql = `
    INSERT OR REPLACE INTO app_shortcuts (id, shortcut, path, name, created_at, updated_at) 
    VALUES (?, ?, ?, ?, ?, ?)
  `;
  const now = Date.now();
  await dbService.getDbInstance().prepare(sql).run(id, shortcut, path, name, now, now);
}

/**
 * 从数据库中删除快捷键
 */
async function removeShortcutFromDatabase(id: string): Promise<void> {
  const sql = `DELETE FROM app_shortcuts WHERE id = ?`;
  await dbService.getDbInstance().prepare(sql).run(id);
}

/**
 * 从数据库加载快捷键
 */
export async function loadShortcutsFromDatabase(): Promise<void> {
  try {
    // 确保表存在
    await createShortcutsTable();
    
    const sql = `SELECT id, shortcut, path, name FROM app_shortcuts`;
    const shortcuts = dbService.getDbInstance().prepare(sql).all() as Array<{
      id: string;
      shortcut: string;
      path: string;
      name: string;
    }>;

    for (const shortcutData of shortcuts) {
      const { id, shortcut, path, name } = shortcutData;
      
      // 转换为系统可识别的格式（仅用于注册）
      const systemShortcut = convertForSystemRegistration(shortcut);
      if (!systemShortcut) {
        log.warn('数据库中快捷键格式无效，跳过:', shortcut);
        continue;
      }
      
      // 检查快捷键是否可用
      if (!globalShortcut.isRegistered(systemShortcut)) {
        try {
          const registered = globalShortcut.register(systemShortcut, async () => {
            log.debug('触发应用快捷键:', systemShortcut, '启动:', name);
            try {
              await appService.launchApp(path);
            } catch (error) {
              log.error('通过快捷键启动应用失败:', error);
            }
          });

          if (registered) {
            // 保存到内存（使用原始格式）
            appShortcuts.set(id, { id, shortcut, path, name });
            log.debug('从数据库恢复快捷键:', shortcut, '用于应用:', name);
          } else {
            log.warn('快捷键恢复失败，可能已被占用:', shortcut);
            // 可以选择从数据库中删除这个无效的快捷键
            await removeShortcutFromDatabase(id);
          }
        } catch (error) {
          log.error('注册快捷键失败:', shortcut, error);
        }
      } else {
        log.warn('快捷键已被占用，跳过:', shortcut);
      }
    }

    log.info('应用快捷键数据库加载完成，恢复了', appShortcuts.size, '个快捷键');

    // 同时加载内置功能快捷键
    await loadBuiltinShortcutsFromDatabase();
  } catch (error) {
    log.error('从数据库加载快捷键失败:', error);
  }
}

/**
 * 创建快捷键表
 */
async function createShortcutsTable(): Promise<void> {
  const sql = `
    CREATE TABLE IF NOT EXISTS app_shortcuts (
      id TEXT PRIMARY KEY,
      shortcut TEXT NOT NULL,
      path TEXT NOT NULL,
      name TEXT NOT NULL,
      created_at INTEGER NOT NULL,
      updated_at INTEGER NOT NULL
    )
  `;
  await dbService.getDbInstance().prepare(sql).run();
}

/**
 * 清理所有应用快捷键
 */
export function cleanupAppShortcuts(): void {
  for (const [id, shortcutData] of appShortcuts) {
    try {
      const systemShortcut = convertForSystemRegistration(shortcutData.shortcut);
      if (systemShortcut) {
        globalShortcut.unregister(systemShortcut);
        log.debug('清理快捷键:', shortcutData.shortcut);
      }
    } catch (error) {
      log.error('清理快捷键失败:', shortcutData.shortcut, error);
    }
  }
  appShortcuts.clear();
  log.info('应用快捷键清理完成');
}

/**
 * 获取所有应用快捷键（用于内部调用）
 */
export function getAppShortcuts(): Array<{
  id: string;
  shortcut: string;
  path: string;
  name: string;
}> {
  return Array.from(appShortcuts.values());
}

/**
 * 检查快捷键冲突的统一函数
 * @param shortcut 要检查的快捷键（原始格式）
 * @param excludeId 要排除的ID（用于更新现有快捷键时）
 * @param type 快捷键类型：'app' | 'builtin'
 * @returns 冲突信息，如果没有冲突返回null
 */
function checkShortcutConflict(
  shortcut: string,
  excludeId?: string,
  type?: 'app' | 'builtin'
): { type: string; name: string; id: string } | null {

  // 1. 检查与应用快捷键的冲突
  for (const [existingId, existingData] of appShortcuts) {
    if (existingId !== excludeId && existingData.shortcut === shortcut) {
      return {
        type: 'app',
        name: existingData.name,
        id: existingId
      };
    }
  }

  // 2. 检查与内置功能快捷键的冲突
  for (const [existingId, existingData] of builtinShortcuts) {
    if (existingId !== excludeId && existingData.shortcut === shortcut) {
      return {
        type: 'builtin',
        name: existingData.name,
        id: existingId
      };
    }
  }

  // 3. 检查与剪贴板快捷键的冲突（从设置中获取）
  // 注意：这里需要异步获取设置，但为了保持函数同步，我们先跳过
  // 在调用此函数之前应该先检查剪贴板快捷键

  return null;
}

/**
 * 检查快捷键是否与系统或其他软件冲突
 * @param shortcut 快捷键（系统格式）
 * @returns 是否冲突
 */
function checkSystemShortcutConflict(shortcut: string): boolean {
  return globalShortcut.isRegistered(shortcut);
}

/**
 * 异步检查快捷键冲突（包括剪贴板快捷键和系统快捷键）
 * @param shortcut 要检查的快捷键（原始格式）
 * @param excludeId 要排除的ID
 * @param type 快捷键类型
 * @returns 冲突信息
 */
async function checkShortcutConflictAsync(
  shortcut: string,
  excludeId?: string,
  type?: 'app' | 'builtin'
): Promise<{ type: string; name: string; id: string } | null> {

  // 检查是否为系统快捷键
  const isSystem = systemShortcutChecker.isSystemShortcut(shortcut);
  log.debug('检查系统快捷键:', shortcut, '结果:', isSystem, '平台:', systemShortcutChecker.getPlatform());
  if (isSystem) {
    return {
      type: 'system',
      name: '系统快捷键',
      id: 'system'
    };
  }

  // 先检查基本冲突
  const basicConflict = checkShortcutConflict(shortcut, excludeId, type);
  if (basicConflict) {
    return basicConflict;
  }

  // 检查与剪贴板快捷键的冲突
  try {
    const settings = settingsService.getSettings();
    const clipboardHotkey = settings.clipboard?.hotkey;

    if (clipboardHotkey && clipboardHotkey === shortcut) {
      // 如果是剪贴板功能本身在设置快捷键，则不算冲突
      if (excludeId !== 'clipboard-history-mode') {
        return {
          type: 'clipboard',
          name: '剪贴板历史',
          id: 'clipboard-history-mode'
        };
      }
    }
  } catch (error) {
    log.error('检查剪贴板快捷键冲突失败:', error);
  }

  return null;
}

/**
 * 创建内置功能的执行动作
 */
function createBuiltinAction(id: string): (() => Promise<void>) | null {
  switch (id) {
    case 'clipboard-history-mode':
      return async () => {
        await WindowManager.toggleClipboardHistory();
      };
    case 'translate-tool':
      return async () => {
        // 尝试获取选中的文字
        const selectedText = await clipboardService.getSelectedText();

        await WindowManager.showMainWindow();
        const mainWindow = WindowManager.getMainWindow();
        if (mainWindow) {
          // 发送消息到渲染进程切换到翻译模式
          mainWindow.getWindow()?.webContents.send('switch-to-translator');

          // 如果有选中的文字，发送到翻译组件
          if (selectedText) {
            // 等待翻译模式切换完成
            setTimeout(() => {
              mainWindow.getWindow()?.webContents.executeJavaScript(`
                window.dispatchEvent(new CustomEvent('electron-message', {
                  detail: { type: 'set-translator-text', text: ${JSON.stringify(selectedText)} }
                }));
              `);
            }, 200);
          } else {
            // 如果没有获取到选中文字，显示提示
            setTimeout(() => {
              mainWindow.getWindow()?.webContents.executeJavaScript(`
                window.dispatchEvent(new CustomEvent('electron-message', {
                  detail: { type: 'show-copy-hint' }
                }));
              `);
            }, 200);
          }
        }
      };
    case 'ai-chat-mode':
      return async () => {
        await WindowManager.showMainWindow();
        // 发送消息到渲染进程切换到AI对话模式
        const mainWindow = WindowManager.getMainWindow();
        if (mainWindow) {
          mainWindow.getWindow()?.webContents.send('switch-to-ai-chat');
        }
      };
    case 'file-search-mode':
      return async () => {
        await WindowManager.showMainWindow();
        // 发送消息到渲染进程切换到文件搜索模式
        const mainWindow = WindowManager.getMainWindow();
        if (mainWindow) {
          mainWindow.getWindow()?.webContents.send('switch-to-file-search');
        }
      };
    case 'screenshot-tool':
      return async () => {
        // 调用截图功能
        const mainWindow = WindowManager.getMainWindow();
        if (mainWindow) {
          mainWindow.getWindow()?.webContents.send('trigger-screenshot');
        }
      };
    default:
      return null;
  }
}

/**
 * 保存内置功能快捷键到数据库
 */
async function saveBuiltinShortcutToDatabase(id: string, shortcut: string, name: string): Promise<void> {
  try {
    const db = dbService.getDbInstance();
    const stmt = db.prepare('INSERT OR REPLACE INTO builtin_shortcuts (id, shortcut, name) VALUES (?, ?, ?)');
    stmt.run(id, shortcut, name);
  } catch (error) {
    log.error('保存内置功能快捷键到数据库失败:', error);
    throw error;
  }
}

/**
 * 从数据库删除内置功能快捷键
 */
async function removeBuiltinShortcutFromDatabase(id: string): Promise<void> {
  try {
    const db = dbService.getDbInstance();
    const stmt = db.prepare('DELETE FROM builtin_shortcuts WHERE id = ?');
    stmt.run(id);
  } catch (error) {
    log.error('从数据库删除内置功能快捷键失败:', error);
    throw error;
  }
}

/**
 * 从数据库加载内置功能快捷键
 */
export async function loadBuiltinShortcutsFromDatabase(): Promise<void> {
  try {
    const db = dbService.getDbInstance();

    // 确保表存在
    const createTableStmt = db.prepare(`
      CREATE TABLE IF NOT EXISTS builtin_shortcuts (
        id TEXT PRIMARY KEY,
        shortcut TEXT NOT NULL,
        name TEXT NOT NULL
      )
    `);
    createTableStmt.run();

    const shortcuts = db.prepare('SELECT * FROM builtin_shortcuts').all() as Array<{
      id: string;
      shortcut: string;
      name: string;
    }>;

    for (const shortcut of shortcuts) {
      const { id, shortcut: shortcutKey, name } = shortcut;

      // 转换为系统可识别的格式
      const systemShortcut = convertForSystemRegistration(shortcutKey);
      if (!systemShortcut) {
        log.warn('内置功能快捷键格式无效，跳过:', shortcutKey);
        continue;
      }

      // 检查快捷键是否可用
      if (!globalShortcut.isRegistered(systemShortcut)) {
        try {
          const action = createBuiltinAction(id);
          if (!action) {
            log.warn('未知的内置功能ID，跳过:', id);
            continue;
          }

          const registered = globalShortcut.register(systemShortcut, async () => {
            log.debug('触发内置功能快捷键:', systemShortcut, '功能:', name);
            try {
              await action();
            } catch (error) {
              log.error('通过快捷键执行内置功能失败:', error);
            }
          });

          if (registered) {
            // 保存到内存（使用原始格式）
            builtinShortcuts.set(id, { id, shortcut: shortcutKey, name, action });
            log.debug('从数据库恢复内置功能快捷键:', shortcutKey, '功能:', name);
          } else {
            log.warn('内置功能快捷键恢复失败，可能已被占用:', shortcutKey);
            // 可以选择从数据库中删除这个无效的快捷键
            await removeBuiltinShortcutFromDatabase(id);
          }
        } catch (error) {
          log.error('注册内置功能快捷键失败:', shortcutKey, error);
        }
      } else {
        log.warn('内置功能快捷键已被占用，跳过:', shortcutKey);
      }
    }

    log.info('内置功能快捷键数据库加载完成，恢复了', builtinShortcuts.size, '个快捷键');
  } catch (error) {
    log.error('从数据库加载内置功能快捷键失败:', error);
  }
}