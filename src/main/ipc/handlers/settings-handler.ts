import { ipcMain } from 'electron';
import { SETTINGS } from '../../../shared/ipc';
import { settingsService } from '../../services/settingsService';

/**
 * 注册设置相关的IPC处理程序
 * 处理从渲染进程到主进程的请求
 */
export function registerSettingsHandlers(): void {  
  // 获取所有设置
  ipcMain.handle(SETTINGS.GET, async (_event) => {
    try {
      return settingsService.loadSettings();
    } catch (error) {
      console.error('获取设置失败:', error);
      throw error;
    }
  });
  
  // 更新设置
  ipcMain.handle(SETTINGS.UPDATE, async (_event, newConfig) => {
    try {
      // 使用settingsService更新配置
      settingsService.updateSettings(newConfig);
      return true;
    } catch (error) {
      console.error('更新配置失败:', error);
      return false;
    }
  });
}