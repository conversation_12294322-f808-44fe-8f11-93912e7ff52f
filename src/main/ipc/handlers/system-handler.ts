import { ipcMain, app } from 'electron';
import { SYSTEM } from '../../../shared/ipc';
import { systemService } from '../../services/systemService';
import * as log from '../../logging';

/**
 * 注册系统信息相关的IPC处理程序
 */
export function registerSystemHandlers(): void {
  // 获取完整系统信息
  ipcMain.handle(SYSTEM.GET_INFO, async () => {
    try {
      const systemInfo = await systemService.getSystemInfo();
      log.info('获取系统信息请求处理成功');
      return systemInfo;
    } catch (error) {
      log.error('获取系统信息失败:', error);
      throw error;
    }
  });

  // 获取电脑ID
  ipcMain.handle(SYSTEM.GET_COMPUTER_ID, async () => {
    try {
      const computerId = await systemService.getComputerId();
      log.info('获取电脑ID请求处理成功');
      return computerId;
    } catch (error) {
      log.error('获取电脑ID失败:', error);
      throw error;
    }
  });

  // 获取主目录路径
  ipcMain.handle(SYSTEM.GET_HOME_PATH, async () => {
    try {
      const homePath = systemService.getHomePath();
      log.info('获取主目录路径请求处理成功:', homePath);
      return homePath;
    } catch (error) {
      log.error('获取主目录路径失败:', error);
      throw error;
    }
  });

  log.info('系统信息IPC处理程序注册完成');
} 