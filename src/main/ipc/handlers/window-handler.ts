import { ipcMain } from 'electron';
import { WindowManager } from '../../windows/window-manager';
import { WINDOW } from '../../../shared/ipc';

/**
 * 注册窗口相关的 IPC 处理程序
 */
export function registerWindowHandlers(): void {
  // 主窗口控制
  ipcMain.on(WINDOW.HIDE, () => {
    WindowManager.hideMainWindow();
  });
  
  ipcMain.on(WINDOW.SHOW, () => {
    WindowManager.showMainWindow();
  });

    // 移动窗口
  ipcMain.on(WINDOW.MOVE_WINDOW, async (_, mouseData: { mouseX: number, mouseY: number, width: number, height: number }) => {
    WindowManager.moveWindow(mouseData);
  });
  
  ipcMain.on(WINDOW.TOGGLE, () => {
    WindowManager.toggleMainWindow();
  });
  
  // 开发者工具
  ipcMain.on(WINDOW.DEV_TOOLS, () => {
    WindowManager.toggleDevTools();
  });

  // 设置窗口是否可调整大小
  ipcMain.on(WINDOW.SET_RESIZABLE, (_, resizable: boolean) => {
    return WindowManager.setPetResizable(resizable);
  });
  
  // 获取窗口是否可调整大小
  ipcMain.handle(WINDOW.IS_RESIZABLE, () => {
    return WindowManager.isPetResizable();
  });
  
  // 设置窗口是否忽略鼠标事件
  ipcMain.on(WINDOW.SET_IGNORE_MOUSE, (_, ignore: boolean) => {
    return WindowManager.setPetIgnoreMouseEvents(ignore);
  });
  
  // 获取配置信息
  ipcMain.handle(WINDOW.GET_CONFIG, () => {
    return WindowManager.getConfig();
  });
  
  // 设置钉住状态
  ipcMain.on(WINDOW.SET_PIN, (_, pinned: boolean) => {
    return WindowManager.setPinned(pinned);
  });
  
  // 获取钉住状态
  ipcMain.handle(WINDOW.IS_PINNED, () => {
    return WindowManager.isPinned();
  });

  // 设置主窗口紧凑模式
  ipcMain.on(WINDOW.SET_COMPACT_MODE, (_, compact: boolean) => {
    return WindowManager.setMainWindowCompactMode(compact);
  });

  // 设置窗口相关
  ipcMain.on(WINDOW.SHOW_SETTINGS, () => {
    WindowManager.showSettingsWindow();
  });

  ipcMain.on(WINDOW.HIDE_SETTINGS, () => {
    WindowManager.hideSettingsWindow();
  });

  ipcMain.on(WINDOW.TOGGLE_SETTINGS, () => {
    WindowManager.toggleSettingsWindow();
  });
} 