import { ipc<PERSON>ain } from 'electron';
import { MCP, WINDOW, WindowType } from '../../../shared/ipc';
import { mcpService } from '../../services/mcpService';
import { dbService } from '../../services/dbService';
import * as logging from '../../logging';
import { mainCrossWindow, getCrossWindowService } from '../../services/cross-window-communication';
import { findProjectByUUID } from '../../services/apiService';
import { WindowManager } from '../../windows/window-manager';
import { addServerFromProject, reactivateLocalServer } from '../../services/aidoProtocolService';



export function registerMcpHandlers(): void {
    ipcMain.handle(MCP.ADD_SERVER, async (_, { key, server }) => {
        return await mcpService.addServer(key, server);
    });
    ipcMain.handle(MCP.UPDATE_SERVER, async (_, { key, server }) => {
        return await mcpService.updateServer(key, server);
    });
    ipcMain.handle(MCP.ACTIVATE, async (_, { key, server, command, args, env }) => {
        // 如果传入了 command、args、env，则使用它们覆盖 server 中的值
        const serverToActivate = {
            ...server,
            ...(command && { command }),
            ...(args && { args }),
            ...(env && { env })
        };
        return await mcpService.activate(key, serverToActivate);
    });
    ipcMain.handle(MCP.DEACTIVATED, async (_, clientName: string) => {
        return await mcpService.deactivate(clientName);
    });
    ipcMain.handle(MCP.LIST_TOOLS, async (_, key?: string) => {
        try {
            const tools = await mcpService.listTools(key);
            return { tools };
        } catch (error: any) {
            logging.error('Error listing MCP tools:', error);
            return { error: error.message || 'Failed to list tools' };
        }
    });
    ipcMain.handle(MCP.GET_ALL_TOOLS, async () => {
        try {
            const tools = dbService.getAllMcpServerTools();
            console.log(`📊 从数据库获取到 ${tools.length} 个MCP技能`);
            return { tools };
        } catch (error: any) {
            logging.error('Error getting all MCP tools from DB:', error);
            return { error: error.message || 'Failed to get all tools' };
        }
    });
    ipcMain.handle(MCP.GET_MCP_TOOL_BY_ID, async (_, id: string) => {
        try {
            const tools = mcpService.getMcpToolById(id);
            console.log(`🔍 根据ID获取MCP工具: ${id}, 找到 ${tools.length} 个工具`);
            return { tools };
        } catch (error: any) {
            logging.error('Error getting MCP tool by ID:', error);
            return { error: error.message || 'Failed to get tool by ID' };
        }
    });
    ipcMain.handle(
        MCP.CALL_TOOL,
        async (_, args: { client: string; name: string; args: any }) => {
            try {
                const result = await mcpService.callTool(args);
                return { result };
            } catch (error: any) {
                logging.error('Error calling MCP tool:', error);
                return { error: error.message || 'Failed to call tool' };
            }
        },
    );
    ipcMain.handle(MCP.GET_CONFIG, async () => {
        try {
            const config = await mcpService.getConfig();
            return { config };
        } catch (error: any) {
            logging.error('Error getting MCP config:', error);
            return { error: error.message || 'Failed to get config' };
        }
    });

    ipcMain.handle(MCP.PUT_CONFIG, async (_, config) => {
        try {
            const success = await mcpService.putConfig(config);
            return { success };
        } catch (error: any) {
            logging.error('Error saving MCP config:', error);
            return { error: error.message || 'Failed to save config' };
        }
    });
    ipcMain.handle(MCP.GET_ACTIVE_SERVERS, () => {
        return mcpService.getClientNames();
    });

    // 设置MCP技能下载相关的事件处理器
    setupMcpToolDownloadHandlers();
}

/**
 * 设置MCP技能下载相关的事件处理器
 */
function setupMcpToolDownloadHandlers() {
    // 监听MCP技能下载请求事件
    mainCrossWindow.on('mcp:tool-download-request', async (data: {
        toolName: string;
        toolArgs: any;
        callbackId: string;
        projectUUId?: string;
    }) => {
        logging.info('收到MCP技能下载请求:', data);

        if (data.projectUUId) {
            // 优先从本地数据库查找MCP服务器信息
            try {
                logging.info(`尝试从本地数据库获取MCP服务器信息: ${data.projectUUId}`);

                // 先检查数据库中所有的MCP服务器
                const allServers = dbService.getAllMcpServers();
                console.log(`🗄️ 数据库中所有MCP服务器:`, Object.keys(allServers).map(id => ({ id, name: allServers[id].name })));

                const localServerInfo = dbService.getMcpServer(data.projectUUId);
                console.log(`🔍 查询结果 (${data.projectUUId}):`, localServerInfo);

                if (localServerInfo) {
                    logging.info(`从本地数据库找到MCP服务器信息:`, localServerInfo);

                    // 简化逻辑：如果服务器已经在数据库中且标记为活跃，直接尝试执行
                    console.log(`🔍 服务器 ${data.projectUUId} 在数据库中且标记为活跃，直接尝试执行技能`);
                    logging.info(`服务器 ${data.projectUUId} 已经安装，直接尝试执行技能`);

                    // 直接发送完成事件，让技能执行逻辑处理
                    mainCrossWindow.emit('mcp:tool-download-completed', {
                        success: true,
                        callbackId: data.callbackId,
                        originalToolName: data.toolName,
                        serverInfo: localServerInfo,
                        skipActivation: true
                    });
                    return;
                }

                // 本地没有找到，尝试从API获取服务器信息
                logging.info(`本地未找到，尝试从API获取MCP服务器信息: ${data.projectUUId}`);
                const projectInfo = await findProjectByUUID(data.projectUUId);

                if (projectInfo && projectInfo.data) {
                    const project = projectInfo.data;
                    logging.info(`从API获取到MCP服务器信息:`);//, project

                    // 显示下载对话
                    showDownloadDialog(data, project.name, {
                        projectUUId: data.projectUUId,
                        projectData: project
                    }, `需要下载并安装 "${project.name}" MCP服务器`);
                    return;
                }
            } catch (error) {
                logging.error(`获取MCP服务器信息失败: ${data.projectUUId}`, error);
            }
        }

        // 如果无法获取服务器信息，显示通用下载请求
        logging.warn(`无法获取技能 ${data.toolName} 对应的MCP服务器信息`);
        showDownloadDialog(data, '未知MCP服务器', undefined, '需要下载并安装MCP服务器');
    });

    // 监听MCP服务器下载完成事件
    mainCrossWindow.on('mcp:server-download-completed', async (data: {
        success: boolean;
        callbackId: string;
        originalToolName?: string;
        serverInfo?: {
            packageName?: string;
            serverKey?: string;
            serverName?: string;
            projectUUId?: string;
            projectData?: any;
            localServerData?: any;
        };
    }) => {
        logging.info('收到MCP服务器下载完成事件:');

        if (data.success && data.serverInfo) {
            try {
                // 使用跨窗口通信服务

                let result: {success: boolean, message?: string};

                if (data.serverInfo.localServerData) {
                    // 本地服务器激活流程
                    const projectId = data.serverInfo.projectUUId!;

                    // 发送激活开始事件
                    logging.info(`📤 发送状态更新事件: activating, callbackId: ${data.callbackId}`);
                    getCrossWindowService().emitFromMain('mcp:server-status-update', {
                        callbackId: data.callbackId,
                        status: 'activating',
                        message: '正在激活MCP服务器...'
                    }, WindowType.MAIN);

                    result = await reactivateLocalServer(projectId, data.serverInfo.localServerData);
                } else if (data.serverInfo.projectData) {
                    // 远程服务器下载安装流程

                    // 发送下载开始事件
                    logging.info(`📤 发送状态更新事件: downloading, callbackId: ${data.callbackId}`);
                    getCrossWindowService().emitFromMain('mcp:server-status-update', {
                        callbackId: data.callbackId,
                        status: 'downloading',
                        message: '正在下载MCP服务器...'
                    }, WindowType.MAIN);

                    // 短暂延迟让UI更新
                    await new Promise(resolve => setTimeout(resolve, 500));

                    // 发送安装开始事件
                    logging.info(`📤 发送状态更新事件: installing, callbackId: ${data.callbackId}`);
                    getCrossWindowService().emitFromMain('mcp:server-status-update', {
                        callbackId: data.callbackId,
                        status: 'installing',
                        message: '正在安装MCP服务器...'
                    }, WindowType.MAIN);

                    // 执行实际的安装过程
                    result = await addServerFromProject(data.serverInfo.projectData);

                    if (result.success) {
                        // 发送激活开始事件
                        logging.info(`📤 发送状态更新事件: activating, callbackId: ${data.callbackId}`);
                        getCrossWindowService().emitFromMain('mcp:server-status-update', {
                            callbackId: data.callbackId,
                            status: 'activating',
                            message: '正在激活MCP服务器...'
                        }, WindowType.MAIN);

                        // 短暂延迟让UI更新
                        await new Promise(resolve => setTimeout(resolve, 500));
                    }
                } else {
                    throw new Error('缺少服务器信息');
                }

                if (result.success) {
                    logging.info(`MCP服务器操作成功: ${result.message}`);

                    // 发送完成事件
                    logging.info(`📤 发送状态更新事件: completed, callbackId: ${data.callbackId}`);
                    getCrossWindowService().emitFromMain('mcp:server-status-update', {
                        callbackId: data.callbackId,
                        status: 'completed',
                        message: 'MCP服务器安装激活完成'
                    }, WindowType.MAIN);

                    // 发送最终成功事件
                    console.log('🎉 MCP服务器安装成功，发送技能下载完成事件');
                    console.log('📤 事件数据:', {
                        toolName: data.originalToolName,
                        success: true,
                        callbackId: data.callbackId
                    });

                    mainCrossWindow.emit('mcp:tool-download-completed', {
                        toolName: data.originalToolName || data.callbackId,
                        success: true,
                        callbackId: data.callbackId
                    });
                } else {
                    throw new Error(result.message || '操作失败');
                }
            } catch (error) {
                logging.error('MCP服务器操作失败:', error);

                // 发送失败状态更新
                mainCrossWindow.emit('mcp:server-status-update', {
                    callbackId: data.callbackId,
                    status: 'failed',
                    message: 'MCP服务器安装失败'
                });

                // 发送失败事件
                mainCrossWindow.emit('mcp:tool-download-completed', {
                    toolName: data.originalToolName || data.callbackId,
                    success: false,
                    callbackId: data.callbackId
                });
            }
        } else {
            // 用户取消或失败
            logging.info('用户取消MCP服务器下载或操作失败');

            // 发送失败状态更新
            logging.info(`📤 发送状态更新事件: failed, callbackId: ${data.callbackId}`);
            try {
                getCrossWindowService().emitFromMain('mcp:server-status-update', {
                    callbackId: data.callbackId,
                    status: 'failed',
                    message: '用户取消MCP服务器下载'
                }, WindowType.MAIN);
            } catch (error) {
                logging.error('发送失败状态更新失败:', error);
            }

            // 发送取消事件
            mainCrossWindow.emit('mcp:tool-download-completed', {
                toolName: data.originalToolName || data.callbackId,
                success: false,
                callbackId: data.callbackId
            });
        }
    });
}

/**
 * 显示下载对话的辅助函数
 */
function showDownloadDialog(data: any, serverName: string, serverInfo?: {
    packageName?: string;
    serverKey?: string;
    serverName?: string;
    projectUUId?: string;
    projectData?: any;
    localServerData?: any;
}, customMessage?: string) {
    // 确保主窗口显示并切换到AI对话模式
    WindowManager.showMainWindow();

    const mainWindow = WindowManager.getMainWindow();
    if (mainWindow) {
        const browserWindow = mainWindow.getWindow();
        if (browserWindow && !browserWindow.isDestroyed()) {
            // 先切换到AI对话模式
            browserWindow.webContents.send(WINDOW.SWITCH_TO_AI_CHAT);

            // 延迟一点时间确保AI对话组件已加载，然后发送下载请求
            setTimeout(() => {
                const enhancedData = {
                    ...data,
                    serverInfo,
                    message: customMessage || `需要下载并安装 "${serverName}" MCP服务器`,
                    originalToolName: data.toolName // 确保传递原始技能名称
                };
                browserWindow.webContents.send(MCP.TOOL_DOWNLOAD_REQUEST, enhancedData);
            }, 500);
        }
    }
}