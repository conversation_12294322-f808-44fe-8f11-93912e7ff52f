import { ipcMain, BrowserWindow } from 'electron';
import { appService } from '../../services/appService';
import { APP, WINDOW } from '../../../shared/ipc';

/**
 * 注册应用程序相关的IPC处理程序
 */
export function registerAppHandlers(): void {
  
  // 启动应用程序
  ipcMain.handle(APP.LAUNCH, async (_, appPath: string) => {
    return await appService.launchApp(appPath);
  });



  // 获取应用列表
  ipcMain.handle(APP.GET_APP_LIST, async () => {
    try {
      return await appService.getAppList();
    } catch (error) {
      console.error('IPC 获取应用列表失败:', error);
      return [];
    }
  });
  
  // 强制刷新应用列表
  ipcMain.handle(APP.REFRESH_APP_LIST, async () => {
    try {
      return await appService.refreshAppList();
    } catch (error) {
      console.error('IPC 刷新应用列表失败:', error);
      return [];
    }
  });
  
  // 监听应用图标更新事件，并广播到所有渲染进程
  ipcMain.on(APP.ICON_UPDATED, (_, data) => {
    // 获取所有窗口
    const windows = BrowserWindow.getAllWindows();
    
    // 向所有窗口广播图标更新事件
    windows.forEach(window => {
      if (!window.isDestroyed()) {
        window.webContents.send(APP.ICON_UPDATED, data);
      }
    });
    
    // 同时使用 crossWindow 广播
    try {
      // 如果有 crossWindow 模块，使用它广播事件
      const { crossWindowService } = require('../../services/cross-window-service');
      if (crossWindowService) {
        crossWindowService.broadcast(APP.ICON_UPDATED, data);
      }
    } catch (err) {
      // 忽略错误，继续使用直接发送
    }
    
    console.log('广播应用图标更新事件:', data.appId);
  });
}
