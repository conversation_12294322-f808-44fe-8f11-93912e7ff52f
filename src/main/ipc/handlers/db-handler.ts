import { ipc<PERSON>ain } from 'electron';
import { DB } from '../../../shared/ipc';
import * as logging from '../../logging';
import { dbService } from '../../services/dbService';
import { Statement } from 'better-sqlite3';
import { isOneDimensionalArray } from '../../utils/util';
export function registerDBHandlers(): void {
    ipcMain.handle(DB.ALL, (event, data) => {
        const { sql, params } = data;
        logging.debug(DB.ALL, sql, params);
        try {
            return dbService.getDbInstance().prepare(sql).all(params);
        } catch (err: any) {
            logging.captureException(err);
            return [];
        }
    });

    ipcMain.handle(DB.RUN, (_, data) => {
        const { sql, params } = data;
        logging.debug(DB.RUN, sql, params);
        try {
            dbService.getDbInstance().prepare(sql).run(params);
            return true;
        } catch (err: any) {
            logging.captureException(err);
            return false;
        }
    });

    ipcMain.handle(DB.TRANSACTION, (_, data: any[]) => {
        logging.debug(DB.TRANSACTION, JSON.stringify(data, null, 2));
        const tasks: { statement: Statement; params: any[] }[] = [];
        data.forEach(({ sql, params }) => {
            tasks.push({
                statement: dbService.getDbInstance().prepare(sql),
                params,
            });
        });
        return new Promise((resolve) => {
            try {
                dbService.getDbInstance().transaction(() => {
                    tasks.forEach(({ statement, params }) => {
                        if (isOneDimensionalArray(params)) {
                            statement.run(params);
                        } else {
                            params.forEach((param: any) => {
                                statement.run(param);
                            });
                        }
                    });
                })();
                resolve(true);
            } catch (err: any) {
                logging.captureException(err);
                resolve(false);
            }
        });
    });

    ipcMain.handle(DB.GET, (_, data) => {
        const { sql, id } = data;
        logging.debug(DB.GET, sql, id);
        try {
            return dbService.getDbInstance().prepare(sql).get(id);
        } catch (err: any) {
            logging.captureException(err);
            return null;
        }
    });
}