import { ipcMain } from 'electron';
import { clipboardService } from '../../services/clipboardService';
import { CLIPBOARD, CLIPBOARD_HISTORY } from '../../../shared/ipc';
import { WindowManager } from '../../windows/window-manager';
import { FocusManager } from '../../utils/focusManager';
import { settingsService } from '../../services/settingsService';
import { dbService } from '../../services/dbService';
import type { ClipboardItem } from '../../../shared/types/clipboard';

/**
 * 注册剪贴板相关的IPC处理程序
 * 处理从渲染进程到主进程的请求
 */
export function registerClipboardHandlers(): void {
  // 获取剪贴板历史记录
  ipcMain.handle(CLIPBOARD.GET_HISTORY, () => {
    return clipboardService.getHistory();
  });

  // 清空剪贴板历史记录
  ipcMain.handle(CLIPBOARD.CLEAR_HISTORY, () => {
    clipboardService.clearHistory();
  });

  // 获取当前剪贴板内容
  ipcMain.handle(CLIPBOARD.GET_CURRENT, () => {
    return clipboardService.getCurrent();
  });

  // 剪贴板历史窗口控制功能
  ipcMain.handle(CLIPBOARD_HISTORY.SHOW, async () => {
    await WindowManager.showClipboardHistory();
    return true;
  });

  ipcMain.handle(CLIPBOARD_HISTORY.HIDE, async () => {
    WindowManager.hideClipboardHistory();
    return true;
  });

  ipcMain.handle(CLIPBOARD_HISTORY.TOGGLE, async () => {
    await WindowManager.toggleClipboardHistory();
    return true;
  });

  ipcMain.handle(CLIPBOARD_HISTORY.GET_HISTORY, async () => {
    try {
    return clipboardService.getHistory();
    } catch (error) {
      console.error('获取剪贴板历史失败:', error);
      return [];
    }
  });

  ipcMain.handle(CLIPBOARD_HISTORY.CLEAR_HISTORY, async () => {
    try {
    clipboardService.clearHistory();
    return true;
    } catch (error) {
      console.error('清空剪贴板历史失败:', error);
      return false;
    }
  });

  ipcMain.handle(CLIPBOARD_HISTORY.SELECT_ITEM, async (event, item: ClipboardItem) => {
    try {
      // 立即隐藏窗口，提供即时的视觉反馈
      WindowManager.hideClipboardHistory();
      
      // 立即写入剪贴板
      const writeSuccess = await clipboardService.writeItemToClipboard(item);
      if (!writeSuccess) {
        return false;
      }
      
      // 焦点恢复逻辑
      const clipboardWindow = WindowManager.getClipboardHistoryWindow();
      console.log('焦点恢复逻辑检查 - clipboardWindow存在:', !!clipboardWindow);

      if (clipboardWindow) {
        // 独立剪贴板窗口模式：检查是否已有记录的焦点，如果没有则使用统一管理器
        const previousApp = clipboardWindow.getPreviousFocusedApp();
        const unifiedApp = FocusManager.getPreviousFocusedApp();
        console.log('独立窗口模式 - 窗口记录的焦点:', previousApp, '统一管理器记录的焦点:', unifiedApp);

        if (!previousApp) {
          // 如果独立窗口没有记录焦点，使用统一管理器的记录
          if (unifiedApp) {
            console.log('独立窗口无焦点记录，使用统一管理器的记录:', unifiedApp);
            clipboardWindow.setPreviousFocusedApp(unifiedApp);
          }
        }
        await clipboardWindow.restorePreviousFocusFast();
      } else {
        // 主窗口模式：直接使用统一的焦点管理器
        const unifiedApp = FocusManager.getPreviousFocusedApp();
        console.log('主窗口模式，使用统一焦点管理器，记录的焦点:', unifiedApp);
        await FocusManager.restorePreviousFocus();
      }
      
      // 短暂延迟确保焦点切换完成，然后执行粘贴
      setTimeout(async () => {
        const pasteSuccess = await clipboardService.performInstantPaste();
        if (!pasteSuccess) {
          console.warn('粘贴操作失败，但内容已复制到剪切板');
        }
      }, 50); // 确保焦点切换完成
      
      return true;
    } catch (error) {
      console.error('选择剪贴板项目失败:', error);
      return false;
    }
  });

  ipcMain.handle(CLIPBOARD_HISTORY.UPDATE_STYLE, async (_event, style) => {
    try {
      // 直接更新窗口样式，不修改配置
      WindowManager.updateClipboardHistoryStyle(style);
      return true;
    } catch (error) {
      console.error('更新剪贴板窗口样式失败:', error);
      return false;
    }
  });

  ipcMain.handle(CLIPBOARD_HISTORY.GET_CURRENT_STYLE, async () => {
    try {
      const clipboardWindow = WindowManager.getClipboardHistoryWindow();
      if (clipboardWindow) {
        const currentStyle = clipboardWindow.getCurrentStyle();
        return currentStyle;
      }
      
      // 如果窗口不存在，返回配置中的样式
      return settingsService.getSetting('clipboard').clipboardWindowStyle;
    } catch (error) {
      console.error('获取当前窗口样式失败:', error);
      return 'bottom-bar'; // 默认值
    }
  });

  // 新增数据库功能的IPC处理方法

  // 获取分页的剪贴板历史记录
  ipcMain.handle(CLIPBOARD_HISTORY.GET_HISTORY_PAGINATED, async (event, options) => {
    try {
      return clipboardService.getHistoryPaginated(options);
    } catch (error) {
      console.error('获取分页剪贴板历史失败:', error);
      return {
        items: [],
        total: 0,
        page: 1,
        limit: 50,
        totalPages: 0
      };
    }
  });

  // 删除单个剪贴板记录
  ipcMain.handle(CLIPBOARD_HISTORY.DELETE_ITEM, async (event, id: string) => {
    try {
      return clipboardService.deleteHistoryItem(id);
    } catch (error) {
      console.error('删除剪贴板记录失败:', error);
      return false;
    }
  });

  // 批量删除剪贴板记录
  ipcMain.handle(CLIPBOARD_HISTORY.DELETE_ITEMS, async (event, ids: string[]) => {
    try {
      return dbService.deleteClipboardItems(ids);
    } catch (error) {
      console.error('批量删除剪贴板记录失败:', error);
      return 0;
    }
  });

  // 设置收藏状态
  ipcMain.handle(CLIPBOARD_HISTORY.SET_FAVORITE, async (event, id: string, favorite: boolean) => {
    try {
      return clipboardService.setHistoryItemFavorite(id, favorite);
    } catch (error) {
      console.error('设置剪贴板收藏状态失败:', error);
      return false;
    }
  });

  // 更新备注
  ipcMain.handle(CLIPBOARD_HISTORY.UPDATE_MEMO, async (event, id: string, memo: string) => {
    try {
      return clipboardService.updateHistoryItemMemo(id, memo);
    } catch (error) {
      console.error('更新剪贴板备注失败:', error);
      return false;
    }
  });

  // 更新标签
  ipcMain.handle(CLIPBOARD_HISTORY.UPDATE_TAGS, async (event, id: string, tags: string[]) => {
    try {
      return dbService.updateClipboardTags(id, tags);
    } catch (error) {
      console.error('更新剪贴板标签失败:', error);
      return false;
    }
  });

  // 清理已删除的记录
  ipcMain.handle(CLIPBOARD_HISTORY.PURGE_DELETED, async (event, olderThanDays: number = 30) => {
    try {
      return dbService.purgeDeletedClipboardItems(olderThanDays);
    } catch (error) {
      console.error('清理已删除的剪贴板记录失败:', error);
      return 0;
    }
  });
}

