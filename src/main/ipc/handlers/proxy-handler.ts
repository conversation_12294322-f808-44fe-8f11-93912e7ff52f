import { ipcMain } from 'electron';
import * as log from '../../logging';
import { 
  configureProxy, 
  getCurrentProxyConfig, 
  saveProxyConfig, 
  testProxyConnection,
  resetProxyToSystem,
  setDirectConnection,
  setManualProxy,
  setPacProxy,
  ProxyConfig
} from '../../utils/proxy';

/**
 * 注册代理相关的IPC处理程序
 */
export function registerProxyHandlers(): void {
  // 获取当前代理配置
  ipcMain.handle('proxy:get-config', async () => {
    try {
      return await getCurrentProxyConfig();
    } catch (error) {
      log.error('获取代理配置失败:', error);
      throw error;
    }
  });

  // 设置代理配置
  ipcMain.handle('proxy:set-config', async (event, config: ProxyConfig) => {
    try {
      await configureProxy(config);
      await saveProxyConfig(config);
      return { success: true };
    } catch (error) {
      log.error('设置代理配置失败:', error);
      throw error;
    }
  });

  // 测试代理连接
  ipcMain.handle('proxy:test-connection', async (event, testUrl?: string) => {
    try {
      return await testProxyConnection(testUrl);
    } catch (error) {
      log.error('测试代理连接失败:', error);
      throw error;
    }
  });

  // 重置为系统代理
  ipcMain.handle('proxy:reset-to-system', async () => {
    try {
      await resetProxyToSystem();
      return { success: true };
    } catch (error) {
      log.error('重置系统代理失败:', error);
      throw error;
    }
  });

  // 设置直连模式
  ipcMain.handle('proxy:set-direct', async () => {
    try {
      await setDirectConnection();
      return { success: true };
    } catch (error) {
      log.error('设置直连模式失败:', error);
      throw error;
    }
  });

  // 设置手动代理
  ipcMain.handle('proxy:set-manual', async (event, proxyRules: string, bypassRules?: string) => {
    try {
      await setManualProxy(proxyRules, bypassRules);
      return { success: true };
    } catch (error) {
      log.error('设置手动代理失败:', error);
      throw error;
    }
  });

  // 设置PAC脚本代理
  ipcMain.handle('proxy:set-pac', async (event, pacScript: string) => {
    try {
      await setPacProxy(pacScript);
      return { success: true };
    } catch (error) {
      log.error('设置PAC脚本代理失败:', error);
      throw error;
    }
  });

  log.info('代理IPC处理程序已注册');
} 