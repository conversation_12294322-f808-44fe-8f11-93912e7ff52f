import { ipcMain } from 'electron';
import { getCrossWindowService } from '../../services/cross-window-communication';
import { CROSS_WINDOW, WindowType } from '../../../shared/ipc';
import * as log from '../../logging';

/**
 * 注册跨窗口通信相关的 IPC 处理程序
 */
export function registerCrossWindowHandlers(): void {
  try {
    // 先尝试移除已存在的处理器，防止重复注册
    try {
      ipcMain.removeHandler('cross-window-get-current-type');
      ipcMain.removeHandler('cross-window-get-active-windows');
      ipcMain.removeAllListeners('cross-window-update-window-map');
      ipcMain.removeAllListeners('ai-chat:tool-call-start');
      ipcMain.removeAllListeners('ai-chat:tool-call-complete');
      ipcMain.removeAllListeners('ai-chat:tool-call-error');
      ipcMain.removeAllListeners('check:ai-chat-mode');
    } catch (error) {
      // 如果处理器不存在，忽略错误
    }

    // 获取当前窗口类型
    ipcMain.handle('cross-window-get-current-type', (event) => {
      // 这里需要根据 webContents 确定窗口类型
      // 由于我们在服务中已经有了这个逻辑，这里可以调用服务的方法
      const windowType = getWindowTypeByWebContents(event.sender);
      return windowType || WindowType.MAIN; // 默认返回主窗口类型
    });

    // 获取所有活跃窗口
    ipcMain.handle('cross-window-get-active-windows', () => {
      return getCrossWindowService().getActiveWindows();
    });

    // 更新窗口映射（当有新窗口创建或销毁时调用）
    ipcMain.on('cross-window-update-window-map', () => {
      getCrossWindowService().updateWindowMap();
    });

    // 处理MCP工具调用开始事件
    ipcMain.on('ai-chat:tool-call-start', (event, data) => {
      log.info('收到MCP工具调用开始事件，转发到渲染进程');
      // 转发到主窗口
      getCrossWindowService().emitFromMain('ai-chat:tool-call-start', data, [WindowType.MAIN]);
    });

    // 处理MCP工具调用完成事件
    ipcMain.on('ai-chat:tool-call-complete', (event, data) => {
      log.info('收到MCP工具调用完成事件，转发到渲染进程');
      // 转发到主窗口
      getCrossWindowService().emitFromMain('ai-chat:tool-call-complete', data, [WindowType.MAIN]);
    });

    // 处理MCP工具调用错误事件
    ipcMain.on('ai-chat:tool-call-error', (event, data) => {
      log.info('收到MCP工具调用错误事件，转发到渲染进程');
      // 转发到主窗口
      getCrossWindowService().emitFromMain('ai-chat:tool-call-error', data, [WindowType.MAIN]);
    });

    // 处理AI对话模式检查事件
    ipcMain.on('check:ai-chat-mode', (event, data) => {
      log.info('收到AI对话模式检查事件');
      // 这里可以添加检查当前是否在AI对话模式的逻辑
      // 暂时返回true，表示当前在AI对话模式
      // 实际实现中需要根据应用状态来判断
      const isInAiChatMode = true; // 这里需要根据实际情况判断
      log.info(`当前AI对话模式状态: ${isInAiChatMode}`);
    });

    log.info('跨窗口通信 IPC 处理程序注册成功');
  } catch (error) {
    log.error('注册跨窗口通信 IPC 处理程序失败:', error);
  }
}

/**
 * 根据 WebContents 获取窗口类型
 * 这是一个辅助函数，用于确定发送请求的窗口类型
 */
function getWindowTypeByWebContents(webContents: Electron.WebContents): WindowType | null {
  // 这里需要根据窗口的特征来判断窗口类型
  // 可以通过窗口的 URL、标题或其他属性来判断
  
  const url = webContents.getURL();
  
  // 根据 URL 路径判断窗口类型
  if (url.includes('index.html') || url.includes('main')) {
    return WindowType.MAIN;
  } else if (url.includes('settings')) {
    return WindowType.SETTINGS;
  } else if (url.includes('clipboard-history')) {
    return WindowType.CLIPBOARD_HISTORY;
  } else if (url.includes('floating-ball')) {
    return WindowType.FLOATING_BALL;
  } else if (url.includes('screen-capture')) {
    return WindowType.SCREEN_CAPTURE;
  } else if (url.includes('pet')) {
    return WindowType.PET;
  }
  
  // 如果无法通过 URL 判断，尝试通过其他方式
  // 比如检查窗口的标题或其他属性
  const title = webContents.getTitle();
  if (title.includes('Settings') || title.includes('设置')) {
    return WindowType.SETTINGS;
  } else if (title.includes('Clipboard') || title.includes('剪贴板')) {
    return WindowType.CLIPBOARD_HISTORY;
  }
  
  // 默认返回主窗口类型
  return WindowType.MAIN;
}

/**
 * 清理跨窗口通信处理程序
 */
export function cleanupCrossWindowHandlers(): void {
  try {
    // 移除所有相关的 IPC 监听器
    ipcMain.removeAllListeners('cross-window-get-current-type');
    ipcMain.removeAllListeners('cross-window-get-active-windows');
    ipcMain.removeAllListeners('cross-window-update-window-map');
    
    log.info('跨窗口通信 IPC 处理程序已清理');
  } catch (error) {
    log.error('清理跨窗口通信 IPC 处理程序失败:', error);
  }
}
