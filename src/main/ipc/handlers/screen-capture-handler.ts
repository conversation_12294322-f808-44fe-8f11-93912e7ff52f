import { ipcMain } from 'electron';
import { SCREEN_CAPTURE } from '../../../shared/ipc';
import { multiScreenDetector } from '../../utils/multi-screen-detector';
import { WindowManager } from '../../windows/window-manager';
import * as log from '../../logging';

/**
 * 注册屏幕截图相关的IPC处理器
 */
export function registerScreenCaptureHandlers(): void {
  try {
    // 获取所有显示器信息
    ipcMain.handle(SCREEN_CAPTURE.GET_DISPLAYS, async () => {
      try {
        if (!multiScreenDetector.getIsInitialized()) {
          log.warn('多屏检测器未初始化，返回空列表');
          return [];
        }
        
        const displays = multiScreenDetector.getAllDisplays();
        log.info(`获取显示器信息成功，共 ${displays.length} 个显示器`);
        return displays;
      } catch (error) {
        log.error('获取显示器信息失败:', error);
        return [];
      }
    });

    // 启动多屏截图
    ipcMain.on(SCREEN_CAPTURE.START_MULTI, async () => {
      const ipcStart = Date.now();
      try {
        log.info(`[性能] 收到启动多屏截图请求，时间: ${ipcStart}`);

        const getManagerStart = Date.now();
        const manager = WindowManager.getMultiScreenCaptureManager();
        const getManagerEnd = Date.now();
        log.info(`[性能] 获取多屏截图管理器耗时: ${getManagerEnd - getManagerStart}ms`);

        const startCaptureStart = Date.now();
        await manager.startMultiScreenCapture();
        const startCaptureEnd = Date.now();
        log.info(`[性能] manager.startMultiScreenCapture耗时: ${startCaptureEnd - startCaptureStart}ms`);

        const ipcEnd = Date.now();
        log.info(`[性能] IPC处理总耗时: ${ipcEnd - ipcStart}ms`);
      } catch (error) {
        const ipcError = Date.now();
        log.error(`[性能] 启动多屏截图失败，耗时: ${ipcError - ipcStart}ms，错误:`, error);
      }
    });

    // 选择特定显示器进行截图
    ipcMain.on(SCREEN_CAPTURE.SELECT_DISPLAY, async (_, displayId: string) => {
      try {
        log.info(`收到选择显示器截图请求: ${displayId}`);
        const manager = WindowManager.getMultiScreenCaptureManager();
        await manager.startSingleDisplayCapture(displayId);
      } catch (error) {
        log.error(`选择显示器截图失败 (${displayId}):`, error);
      }
    });

    log.info('屏幕截图IPC处理器注册成功');
  } catch (error) {
    log.error('注册屏幕截图IPC处理器失败:', error);
  }
}
