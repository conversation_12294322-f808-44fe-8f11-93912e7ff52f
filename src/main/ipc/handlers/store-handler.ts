import { ipcMain } from 'electron';
import { STORE } from '../../../shared/ipc';
import { storeService } from '../../services/storeService';


export function registerStoreHandlers(): void {
    ipcMain.on(STORE.GET, (evt, args) => {
        const { key, defaultValue } = args;
        evt.returnValue = storeService.get(key, defaultValue);
    });

    ipcMain.on(STORE.SET, (evt, args) => {
        const { key, val } = args;
        storeService.set(key, val);
        evt.returnValue = val;
    });
}