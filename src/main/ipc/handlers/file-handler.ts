import { ipcMain, dialog } from 'electron';
import { fileService } from '../../services/fileService';
import { FILE } from '../../../shared/ipc';

/**
 * 注册文件相关的 IPC 处理程序
 */
export function registerFileHandlers(): void {
  // 获取最近文件
  ipcMain.handle(FILE.GET_RECENT, async () => {
    try {
      return await fileService.getRecentFiles();
    } catch (error) {
      console.error('获取最近文件出错:', error);
      return [];
    }
  });

  // 搜索文件
  ipcMain.handle(FILE.SEARCH, async (_, query: string) => {
    try {
      return await fileService.searchFiles(query);
    } catch (error) {
      console.error('搜索文件出错:', error);
      return [];
    }
  });

  // 打开文件
  ipcMain.handle(FILE.OPEN, async (_, path: string) => {
    try {
      return await fileService.openFile(path);
    } catch (error) {
      console.error('打开文件出错:', error);
      return false;
    }
  });

  // 打开文件所在目录
  ipcMain.handle(FILE.OPEN_LOCATION, async (_, path: string) => {
    try {
      const result = await fileService.openFileLocation(path);
      return result;
    } catch (error) {
      console.error('打开文件所在目录出错:', error);
      return false;
    }
  });

  // 读取文件预览内容
  ipcMain.handle(FILE.READ_PREVIEW, async (_, path: string) => {
    try {
      return await fileService.readFilePreview(path);
    } catch (error) {
      console.error('读取文件预览出错:', error);
      return '无法读取文件内容';
    }
  });

  // 检查文件是否存在
  ipcMain.handle(FILE.CHECK_EXISTS, async (_, path: string) => {
    try {
      return await fileService.checkFileExists(path);
    } catch (error) {
      console.error('检查文件存在性出错:', error);
      return false;
    }
  });

  // 选择文件夹
  ipcMain.handle(FILE.SELECT_FOLDER, async () => {
    try {
      const result = await dialog.showOpenDialog({
        properties: ['openDirectory'],
        title: '选择文件夹'
      });
      
      if (!result.canceled && result.filePaths.length > 0) {
        return result.filePaths[0];
      }
      return null;
    } catch (error) {
      console.error('选择文件夹出错:', error);
      return null;
    }
  });
} 