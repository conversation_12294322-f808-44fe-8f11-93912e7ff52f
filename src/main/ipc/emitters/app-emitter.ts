import { appService } from '../../services/appService';
import { APP } from '../../../shared/ipc';
import { WindowManager } from '../../windows/window-manager';

/**
 * 通知渲染进程应用列表已更新
 * @param appList 应用列表
 * @param updateInfo 更新信息
 */
function notifyRenderer(appList: any[], updateInfo: { count: number; timestamp: number }): void {
  try {
    const mainWindow = WindowManager.getMainWindow();
    
    // 通知主窗口
    if (mainWindow) {
      const browserWindow = mainWindow.getWindow();
      if (browserWindow && !browserWindow.isDestroyed()) {
        browserWindow.webContents.send(APP.LIST_UPDATED, {
          apps: appList,
          ...updateInfo
        });
      }
    }
    
    // 如果有其他需要通知的窗口，可以在这里添加
    console.log(`已通知渲染进程应用列表更新: ${updateInfo.count} 个应用`);
  } catch (error) {
    console.error('通知渲染进程应用列表更新失败:', error);
  }
}

/**
 * 注册应用列表变化事件监听器
 */
export function registerAppEmitters(): void {
  // 监听应用服务的变化事件，并通知渲染进程
  appService.on('listUpdated', (appList, updateInfo) => {
    notifyRenderer(appList, updateInfo);
  });
  
  console.log('应用列表emitter已注册');
}