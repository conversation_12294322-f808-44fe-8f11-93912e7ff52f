import { clipboardService } from '../../services/clipboardService';
import { ClipboardItem } from '../../../shared/types/clipboard';
import { CLIPBOARD } from '../../../shared/ipc';
import { WindowManager } from '../../windows/window-manager';

/**
 * 通知渲染进程剪贴板历史已更新
 * @param history 剪贴板历史记录
 */
function notifyRenderer(history: ClipboardItem[]): void {
  try {
    const mainWindow = WindowManager.getMainWindow();
    const clipboardHistoryWindow = WindowManager.getClipboardHistoryWindow();
    
    // 通知主窗口
    if (mainWindow) {
      const browserWindow = mainWindow.getWindow();
      if (browserWindow && !browserWindow.isDestroyed()) {
        browserWindow.webContents.send(CLIPBOARD.ON_CHANGE, history);
      }
    }
    
    // 通知剪贴板历史窗口
    if (clipboardHistoryWindow && !clipboardHistoryWindow.isDestroyed()) {
      const browserWindow = clipboardHistoryWindow.getWindow();
      if (browserWindow && !browserWindow.isDestroyed()) {
        browserWindow.webContents.send(CLIPBOARD.ON_CHANGE, history);
      }
    }
  } catch (error) {
    console.error('通知渲染进程失败:', error);
  }
}

/**
 * 注册剪贴板变化事件监听器
 * 主要任务是将主进程中的剪贴板变化事件转发到渲染进程，并添加到历史记录
 */
export function registerClipboardEmitters(): void {
  // 监听剪贴板服务的变化事件，并通知渲染进程
  clipboardService.on('changed', (history) => {
    // 直接通知渲染进程，不再重复处理历史记录
    // clipboardService 已经维护了自己的历史记录
    notifyRenderer(history);
  });
  
  // 始终发送初始剪贴板历史，确保渲染进程启动后能立即获取当前状态
  setTimeout(() => {
    const currentHistory = clipboardService.getHistory();
    console.log(`发送初始剪贴板历史到渲染进程: ${currentHistory.length} 项`);
    notifyRenderer(currentHistory);
  }, 500); // 减少延迟到500ms
} 