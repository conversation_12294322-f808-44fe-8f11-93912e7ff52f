import fs from 'node:fs'
import path from 'node:path'

import { APP_PATH, USER_DATA_PATH } from '@main/common'


export function getResourcePath() {
  return path.join(APP_PATH, 'resources')
}

export function getDataPath() {
  const dataPath = path.join(USER_DATA_PATH, 'Data')
  if (!fs.existsSync(dataPath)) {
    fs.mkdirSync(dataPath, { recursive: true })
  }
  return dataPath
}

export function getInstanceName(baseURL: string) {
  try {
    return new URL(baseURL).host.split('.')[0]
  } catch (error) {
    return ''
  }
}

export function debounce(func: (...args: any[]) => void, wait: number, immediate = false) {
  let timeout: NodeJS.Timeout | null = null
  return function (...args: any[]) {
    if (timeout) clearTimeout(timeout)
    if (immediate) {
      func(...args)
    } else {
      timeout = setTimeout(() => func(...args), wait)
    }
  }
}

export function dumpPersistState() {
  const persistState = JSON.parse(localStorage.getItem('persist:cherry-studio') || '{}')
  for (const key in persistState) {
    persistState[key] = JSON.parse(persistState[key])
  }
  return JSON.stringify(persistState)
}

export const runAsyncFunction = async (fn: () => void) => {
  await fn()
}

export function makeSureDirExists(dir: string) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true })
  }
}
