import fs from 'node:fs'
import path from 'node:path'
import { app } from 'electron'
import { promisify } from 'node:util'
import { pipeline } from 'node:stream'
import * as log from '../logging';
import AdmZip from 'adm-zip'
import { isLinux, isMac, isWin } from '../common'
import { APP_PATH, BIN_DIR } from '../common/paths'
import { makeSureDirExists } from './index'
import { settingsService } from '@main/services/settingsService'
const pipelineAsync = promisify(pipeline)

// 获取应用版本
function getAppVersion(): string {
  return app.getVersion()
}

function getResourcePath(): string {
  let resourcePath;
  if (app.isPackaged) {
    if (isWin) {
      // Windows: 资源在可执行文件同级的resources目录
      resourcePath = path.join(path.dirname(app.getPath('exe')), 'resources');
    } else if (isMac) {
      // macOS: 资源在.app包内的Contents/Resources目录
      resourcePath = path.join(path.dirname(app.getPath('exe')), '../Resources');
    } else if (isLinux) {
      // Linux: 资源通常在可执行文件同级的resources目录
      resourcePath = path.join(path.dirname(app.getPath('exe')), 'resources');
    } else {
      // 默认
      resourcePath = path.join(APP_PATH, 'resources');
    }
  } else {
    // 开发环境: 项目根目录下的resources文件夹
    resourcePath = path.join(APP_PATH, 'build/runtime');
  }
  return resourcePath
}

/**
 * 根据操作系统获取正确的bun压缩包路径
 */
export function getBunZipPath(): string {
  let zipFileName = ''
  if (isWin) {
    zipFileName = 'bun-windows-x64.zip'
  } else if (isMac) {
    zipFileName = process.arch === 'arm64' ? 'bun-darwin-aarch64.zip' : 'bun-darwin-x64.zip'
  } else if (isLinux) {
    zipFileName = process.arch === 'arm64' ? 'bun-linux-aarch64.zip' : 'bun-linux-x64.zip'
  }

  return path.join(getResourcePath(), zipFileName)
}

/**
 * 根据操作系统获取正确的Python压缩包路径
 */
export function getPythonZipPath(): string | null {
  // Python目前只在Windows上内置
  if (isWin) {
    const zipFileName = 'python-windows-x64.zip';
    const zipPath = path.join(getResourcePath(), zipFileName);
    if (fs.existsSync(zipPath)) {
      return zipPath;
    }
    log.warn(`[ExtractTools] Python压缩包未找到: ${zipPath}`);
  }
  return null;
}

export function getUvZipPath(): string {
  let zipFileName = ''
  if (isWin) {
    zipFileName = 'uv-windows-x64.zip'
  } else if (isMac) {
    zipFileName = process.arch === 'arm64' ? 'uv-darwin-aarch64.zip' : 'uv-darwin-x64.zip'
  } else if (isLinux) {
    zipFileName = process.arch === 'arm64' ? 'uv-linux-aarch64.zip' : 'uv-linux-x64.zip'
  }
  return path.join(getResourcePath(), zipFileName)
}

/**
 * 根据操作系统获取正确的FFmpeg压缩包路径
 */
export function getFfmpegZipPath(): string | null {
  let zipFileName = ''
  if (isWin) {
    zipFileName = 'ffmpeg-windows-x64.zip'
  } else if (isMac) {
    zipFileName = 'ffmpeg-darwin.zip'
  } else {
    // Linux 暂时不支持 FFmpeg
    return null
  }
  
  const zipPath = path.join(getResourcePath(), zipFileName)
  if (fs.existsSync(zipPath)) {
    return zipPath
  }
  log.warn(`[ExtractTools] FFmpeg压缩包未找到: ${zipPath}`)
  return null
}

/**
 * 确保bin目录存在
 */
export function ensureBinDir(): string {
  makeSureDirExists(BIN_DIR)
  return BIN_DIR
}

/**
 * 获取Bun可执行文件名称
 */
export function getBunName(): string {
  return isWin ? 'bun.exe' : 'bun'
}

export function getUvName(): string {
  return isWin ? 'uv.exe' : 'uv'
}

export function getUvxName(): string {
  return isWin ? 'uvx.exe' : 'uvx'
}

/**
 * 获取FFmpeg可执行文件名称
 */
export function getFfmpegName(): string {
  return isWin ? 'ffmpeg.exe' : 'ffmpeg'
}

/**
 * 获取版本文件路径
 */
function getVersionFilePath(): string {
  return path.join(BIN_DIR, 'version.txt');
}

/**
 * 写入版本文件
 */
function writeVersionFile(): void {
  const versionFilePath = getVersionFilePath();
  const currentVersion = getAppVersion();
  fs.writeFileSync(versionFilePath, currentVersion);
  log.debug(`[ExtractTools] 写入版本文件: ${versionFilePath}, 版本: ${currentVersion}`);
}

/** 写入bun镜像配置文件 */
function writeBunMirrorConfigFile(): void {
  const bunMirrorConfigPath = path.join(BIN_DIR, 'bunfig.toml');
  fs.writeFileSync(bunMirrorConfigPath, `[install]
registry = "https://registry.npmmirror.com/"
`); 
  log.debug(`[ExtractTools] 写入bun镜像配置文件: ${bunMirrorConfigPath}`);
}

/**
 * 检查是否需要解压
 * 通过检查版本文件判断是否需要重新解压
 */
function needsExtraction(): boolean {
  const bunName = getBunName();
  const uvName = getUvxName();
  const bunEPath = path.join(BIN_DIR, bunName);
  const uvPath = path.join(BIN_DIR, uvName);
  const versionFilePath = getVersionFilePath();
  
  // 如果可执行文件不存在，则需要解压
  if (!fs.existsSync(bunEPath)) {
    return true;
  }
  if (!fs.existsSync(uvPath)) {
    return true;
  }

  // 新增：检查Python目录是否存在（仅Windows）
  if (isWin) {
    const pythonDir = path.join(BIN_DIR, 'python');
    if (!fs.existsSync(pythonDir)) {
      log.debug('[ExtractTools] Python目录不存在，需要解压。');
      return true;
    }
  }
  
  // 检查版本文件是否存在
  if (!fs.existsSync(versionFilePath)) {
    return true;
  }
  
  // 读取版本文件中的版本并比较
  try {
    const savedVersion = fs.readFileSync(versionFilePath, 'utf-8').trim();
    const currentVersion = getAppVersion();
    return savedVersion !== currentVersion;
  } catch (error) {
    // 如果读取失败，则需要解压
    return true;
  }
}

/**
 * 执行解压操作
 */
async function performExtraction(): Promise<void> {
  try {
    const binDir = ensureBinDir();
    const bunZipPath = getBunZipPath();
    const uvZipPath = getUvZipPath();
    const bunName = getBunName();
    const uvName = getUvName();
    const uvxName = getUvxName();
    const bunPath = path.join(binDir, bunName);
    const uvPath = path.join(binDir, uvName);
    const uvxPath = path.join(binDir, uvxName);

    log.debug(`[ExtractTools] 开始解压Bun: ${bunZipPath} 到 ${binDir}`);
    log.debug(`[ExtractTools] 开始解压Uv: ${uvZipPath} 到 ${binDir}`);

    if (!fs.existsSync(bunZipPath)) {
      throw new Error(`Bun压缩包不存在: ${bunZipPath}`);
    }
    if (!fs.existsSync(uvZipPath)) {
      throw new Error(`Uv压缩包不存在: ${uvZipPath}`);
    }

    // 使用AdmZip解压
    const bunZip = new AdmZip(bunZipPath);
    bunZip.extractAllTo(binDir, true);
    const uvZip = new AdmZip(uvZipPath);
    uvZip.extractAllTo(binDir, true);

    // 新增：解压FFmpeg（Windows和macOS）
    if (isWin || isMac) {
      const ffmpegZipPath = getFfmpegZipPath();
      if (ffmpegZipPath) {
        log.debug(`[ExtractTools] 开始解压FFmpeg: ${ffmpegZipPath} 到 ${binDir}`);
        const ffmpegZip = new AdmZip(ffmpegZipPath);
        ffmpegZip.extractAllTo(binDir, true);
        log.debug(`[ExtractTools] FFmpeg解压完成到: ${binDir}`);
      }
    }

    // 新增：解压Python（仅Windows）
    if (isWin) {
      const pythonZipPath = getPythonZipPath();
      if (pythonZipPath) {
        log.debug(`[ExtractTools] 开始解压Python: ${pythonZipPath} 到 ${binDir}`);
        const pythonZip = new AdmZip(pythonZipPath);
        pythonZip.extractAllTo(binDir, true);
        log.debug(`[ExtractTools] Python解压完成到: ${binDir}`);
      }
    }

    // 对于非Windows系统，确保可执行权限
    if (!isWin) {
      log.debug(`[ExtractTools] 设置Bun可执行权限: ${bunPath}`);
      fs.chmodSync(bunPath, 0o755);
    }
    if (!isWin) {
      log.debug(`[ExtractTools] 设置Uv可执行权限: ${uvPath}`);
      fs.chmodSync(uvPath, 0o755);
    }
    if (!isWin) {
      log.debug(`[ExtractTools] 设置Uvx可执行权限: ${uvxPath}`);
      fs.chmodSync(uvxPath, 0o755);
    }
    
    // 设置FFmpeg可执行权限（macOS）
    if (!isWin) {
      const ffmpegName = getFfmpegName();
      const ffmpegPath = path.join(binDir, ffmpegName);
      if (fs.existsSync(ffmpegPath)) {
        log.debug(`[ExtractTools] 设置FFmpeg可执行权限: ${ffmpegPath}`);
        fs.chmodSync(ffmpegPath, 0o755);
      }
    }

    // 写入版本文件，记录当前版本
    writeVersionFile();
    writeBunMirrorConfigFile();

    log.debug(`[ExtractTools] Bun解压完成: ${bunPath}`);
    log.debug(`[ExtractTools] UV解压完成: ${uvPath}`);
    log.debug(`[ExtractTools] Uvx解压完成: ${uvxPath}`);
  } catch (error) {
    log.error(`[ExtractTools] 解压Bun/UV/UVX/Python失败:`, error);
    throw error;
  }
}

/**
 * 解压Bun压缩包到bin目录
 * 只在必要时解压，避免每次启动都执行解压操作
 */
export async function extractBunToBindir(): Promise<void> {
  // 异步执行解压操作，避免阻塞主进程
  setTimeout(async () => {
    try {
      // 检查是否需要解压
      if (!needsExtraction()) {
        log.debug(`[ExtractTools] Bun和Uvx已经存在且版本匹配，跳过解压`);
        return;
      }
      
      await performExtraction();
    } catch (error) {
      log.error(`[ExtractTools] 解压操作失败:`, error);
    }
  }, 1000); // 延迟1秒执行，不阻塞主进程启动
  
  // 立即返回，不等待解压完成
  return Promise.resolve();
} 