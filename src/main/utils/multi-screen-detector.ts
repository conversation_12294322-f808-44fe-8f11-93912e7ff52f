import { screen, Display } from 'electron';
import * as log from '../logging';

/**
 * 显示器信息接口
 * 扩展Electron的Display接口，添加一些额外信息
 */
export interface DisplayInfo extends Omit<Display, 'id'> {
  id: string;       // 显示器唯一标识符（字符串格式）
  electronId: number; // 原始的Electron显示器ID
  isPrimary: boolean; // 是否为主显示器
  name: string;     // 显示器名称（如果可用）
}

/**
 * 多屏幕检测器
 * 负责检测和管理多个显示器
 */
export class MultiScreenDetector {
  private displays: DisplayInfo[] = [];
  private displayChangeListeners: Array<(displays: DisplayInfo[]) => void> = [];
  private isInitialized: boolean = false;

  constructor() {
    // 不在构造函数中初始化，等待应用准备就绪
    log.info('多屏幕检测器已创建，等待应用准备就绪');
  }

  /**
   * 初始化检测器（在应用准备就绪后调用）
   */
  public initialize(): void {
    if (this.isInitialized) {
      return;
    }

    try {
      // 先设置初始化标志，然后更新显示器列表
      this.isInitialized = true;
      this.updateDisplays();

      // 监听显示器变化事件
      screen.on('display-added', () => this.handleDisplayChange());
      screen.on('display-removed', () => this.handleDisplayChange());
      screen.on('display-metrics-changed', () => this.handleDisplayChange());

      log.info('多屏幕检测器已初始化');
    } catch (error) {
      log.error('多屏幕检测器初始化失败:', error);
      this.isInitialized = false;
    }
  }

  /**
   * 处理显示器变化事件
   */
  private handleDisplayChange(): void {
    log.info('检测到显示器配置变化');
    this.updateDisplays();
    
    // 通知所有监听器
    this.displayChangeListeners.forEach(listener => {
      try {
        listener(this.displays);
      } catch (error) {
        log.error('显示器变化监听器执行出错', error);
      }
    });
  }

  /**
   * 更新显示器列表
   */
  private updateDisplays(): void {
    if (!this.isInitialized) {
      log.warn('尝试在初始化前更新显示器列表');
      return;
    }

    try {
      const primaryDisplay = screen.getPrimaryDisplay();
      const allDisplays = screen.getAllDisplays();

      log.info(`检测到 ${allDisplays.length} 个显示器，主显示器ID: ${primaryDisplay.id}`);

      this.displays = allDisplays.map(display => {
        const isPrimary = display.id === primaryDisplay.id;

        log.info(`显示器 ${display.id}: ${display.size.width}x${display.size.height} at (${display.bounds.x}, ${display.bounds.y}) ${isPrimary ? '(主显示器)' : ''}`);

        return {
          ...display,
          id: `display-${display.id}`,
          electronId: display.id,
          isPrimary,
          name: isPrimary ? '主显示器' : `显示器 ${display.id}`
        };
      });

      log.info(`已更新显示器列表，共 ${this.displays.length} 个显示器`);
    } catch (error) {
      log.error('更新显示器列表失败:', error);
      this.displays = [];
    }
  }

  /**
   * 获取所有显示器信息
   */
  public getAllDisplays(): DisplayInfo[] {
    if (!this.isInitialized) {
      log.warn('检测器未初始化，返回空显示器列表');
      return [];
    }
    return [...this.displays];
  }

  /**
   * 获取主显示器信息
   */
  public getPrimaryDisplay(): DisplayInfo | null {
    if (!this.isInitialized || this.displays.length === 0) {
      return null;
    }
    return this.displays.find(display => display.isPrimary) || this.displays[0];
  }

  /**
   * 根据ID获取显示器信息
   * @param id 显示器ID
   */
  public getDisplayById(id: string): DisplayInfo | undefined {
    return this.displays.find(display => display.id === id);
  }

  /**
   * 添加显示器变化监听器
   * @param listener 监听器函数
   * @returns 移除监听器的函数
   */
  public onDisplayChange(listener: (displays: DisplayInfo[]) => void): () => void {
    this.displayChangeListeners.push(listener);
    
    return () => {
      const index = this.displayChangeListeners.indexOf(listener);
      if (index !== -1) {
        this.displayChangeListeners.splice(index, 1);
      }
    };
  }

  /**
   * 获取显示器数量
   */
  public getDisplayCount(): number {
    return this.displays.length;
  }

  /**
   * 判断是否为多显示器环境
   */
  public isMultiDisplayEnvironment(): boolean {
    return this.isInitialized && this.displays.length > 1;
  }

  /**
   * 检查是否已初始化
   */
  public getIsInitialized(): boolean {
    return this.isInitialized;
  }
}

// 导出单例实例
export const multiScreenDetector = new MultiScreenDetector();
