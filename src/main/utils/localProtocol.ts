import { protocol, net } from 'electron'
import { handleAidoProtocol } from '../services/aidoProtocolService.js'

export function registerLocalProtocol(): void {
    // protocol.handle('file', (request) => {
    //     const url = request.url.replace('file://', '')
    //     const decodedUrl = decodeURI(url)
    //     try {
    //       return net.fetch(new URL(decodedUrl).toString())
    //     } catch (error) {
    //       console.error('Could not get file path:', error)
    //       // 返回一个表示错误的 Response 对象
    //       return new Response(null, { status: 404 })
    //     }
    // })
    
    // 注册 aido:// 协议处理器
    protocol.handle('aido', async (request) => {
      try {
        console.log('Protocol.handle Aido 协议被调用:', request.url)
        const response = await handleAidoProtocol(request.url)
        return new Response(JSON.stringify(response), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        })
      } catch (error) {
        console.error('Protocol.handle 处理 Aido 协议失败:', error)
        return new Response(JSON.stringify({
          success: false,
          error: error.message
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        })
      }
    })

    protocol.handle('image', (request) => {
      const url = request.url.replace('image://', '')
      const decodedUrl = decodeURI(url)
      try {
        return net.fetch(`file://${decodedUrl}`)
      } catch (error) {
        console.error('加载图片失败:', error)
        return new Response(null, { status: 404 })
      }
    });

    protocol.handle('live2d', (request) => {
        const url = request.url.replace('live2d://', '')
        const decodedUrl = decodeURI(url)
        try {
            return net.fetch(`file://${decodedUrl}`)
        } catch (error) {
            console.error('加载Live2D模型失败:', error)
            return new Response(null, { status: 404 })
        }
    })
}