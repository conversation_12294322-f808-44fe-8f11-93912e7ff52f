import { app, session } from 'electron';
import * as log from '../logging';
import { storeService } from '../services/storeService';

export interface ProxyConfig {
  mode: 'direct' | 'system' | 'fixed_servers' | 'pac_script';
  proxyRules?: string;
  proxyBypassRules?: string;
  pacScript?: string;
}

/**
 * 配置应用代理设置
 */
export async function configureProxy(config?: ProxyConfig): Promise<void> {
  try {
    const defaultSession = session.defaultSession;
    
    // 如果没有提供配置，从存储中读取或使用默认配置
    if (!config) {
      const savedConfig = await storeService.get('proxy-config') as ProxyConfig | null;
      config = savedConfig || { mode: 'system' };
    }
    
    // 根据配置模式设置代理
    await defaultSession.setProxy(config);
    
    log.info(`✅ 代理配置已设置: ${config.mode}`);
    
    // 记录当前代理状态
    if (config.mode !== 'direct') {
      try {
        const proxyUrl = await defaultSession.resolveProxy('https://www.google.com');
        log.debug(`当前代理解析结果: ${proxyUrl}`);
      } catch (error) {
        log.debug('代理解析测试失败:', error);
      }
    }
    
  } catch (error) {
    log.error('❌ 配置代理失败:', error);
    throw error;
  }
}

/**
 * 获取当前代理配置
 */
export async function getCurrentProxyConfig(): Promise<ProxyConfig | null> {
  try {
    return await storeService.get('proxy-config');
  } catch (error) {
    log.error('获取代理配置失败:', error);
    return null;
  }
}

/**
 * 保存代理配置
 */
export async function saveProxyConfig(config: ProxyConfig): Promise<void> {
  try {
    await storeService.set('proxy-config', config);
    log.info('代理配置已保存');
  } catch (error) {
    log.error('保存代理配置失败:', error);
    throw error;
  }
}

/**
 * 测试代理连接
 */
export async function testProxyConnection(testUrl: string = 'https://www.google.com'): Promise<boolean> {
  try {
    const defaultSession = session.defaultSession;
    const proxyUrl = await defaultSession.resolveProxy(testUrl);
    
    // 如果返回 'DIRECT'，表示直连
    // 如果返回 'PROXY xxx:xxx'，表示使用代理
    log.info(`代理测试结果: ${proxyUrl}`);
    
    return true;
  } catch (error) {
    log.error('代理连接测试失败:', error);
    return false;
  }
}

/**
 * 重置代理配置为系统默认
 */
export async function resetProxyToSystem(): Promise<void> {
  const systemConfig: ProxyConfig = { mode: 'system' };
  await configureProxy(systemConfig);
  await saveProxyConfig(systemConfig);
}

/**
 * 设置直连模式（不使用代理）
 */
export async function setDirectConnection(): Promise<void> {
  const directConfig: ProxyConfig = { mode: 'direct' };
  await configureProxy(directConfig);
  await saveProxyConfig(directConfig);
}

/**
 * 设置手动代理
 */
export async function setManualProxy(
  proxyRules: string,
  bypassRules?: string
): Promise<void> {
  const manualConfig: ProxyConfig = {
    mode: 'fixed_servers',
    proxyRules,
    proxyBypassRules: bypassRules
  };
  await configureProxy(manualConfig);
  await saveProxyConfig(manualConfig);
}

/**
 * 设置PAC脚本代理
 */
export async function setPacProxy(pacScript: string): Promise<void> {
  const pacConfig: ProxyConfig = {
    mode: 'pac_script',
    pacScript
  };
  await configureProxy(pacConfig);
  await saveProxyConfig(pacConfig);
} 