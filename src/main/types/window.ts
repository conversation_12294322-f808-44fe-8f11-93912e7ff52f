/**
 * 主窗口接口
 * 定义主窗口应该具有的方法
 */
export interface IMainWindow {
  show(): void;
  hide(): void;
  isVisible(): boolean;
  isDestroyed(): boolean;
  resize(width: number, height: number): void;
  toggleDevTools(): void;
  getWindow(): any; // 保留 any，因为我们可能不想暴露 BrowserWindow 的完整 API
  setBounds?(bounds: { x: number, y: number, width: number, height: number }): void;
  setAlwaysOnTop(alwaysOnTop: boolean): void;
  setCompactMode(compact: boolean): void; // 新增紧凑模式方法
  setPinned(pinned: boolean): void;
  isPinned(): boolean;
}

/**
 * 窗口管理器接口
 * 定义窗口管理器应该具有的所有方法
 */
export interface IWindowManager {
  // 窗口控制
  showMainWindow(): void;
  hideMainWindow(): void;
  toggleMainWindow(): void;
  // 剪贴板历史窗口控制
  showClipboardHistory(): void;
  hideClipboardHistory(): void;
  toggleClipboardHistory(): void;
  updateClipboardHistoryStyle(): void;

  // 开发者工具
  toggleDevTools(): void;

  // 其他方法
  getMainWindow(): IMainWindow | null;
}

/**
 * 静态窗口管理器类型，用于WindowManager静态类
 */
export interface IWindowManagerStatic {
  showMainWindow: () => void;
  hideMainWindow: () => void;
  toggleMainWindow: () => void;
  showClipboardHistory: () => Promise<void>;
  hideClipboardHistory: () => void;
  toggleClipboardHistory: () => Promise<void>;
  updateClipboardHistoryStyle: () => void;
  toggleDevTools: () => void;
  getMainWindow: () => IMainWindow | null;
  getClipboardHistoryWindow: () => any | null;
  getIsToggling: () => boolean;
  isPinned: () => boolean;
}

/**
 * 窗口事件处理接口
 * 定义窗口事件处理程序需要的方法
 */
export interface IWindowEventHandler {
  handleWindowShow?: () => void;
  handleWindowHide?: () => void;
  handleWindowClose?: () => void;
  handleWindowFocus?: () => void;
  handleWindowBlur?: () => void;
} 