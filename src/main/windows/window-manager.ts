import { MainWindow } from './main-window';
import { FloatingBall } from './floating-ball';
import { ScreenCaptureWindow } from './screen-capture';
import { MultiScreenCaptureManager } from './multi-screen-capture-manager';
import { multiScreenDetector } from '../utils/multi-screen-detector';
import { ClipboardHistoryWindow } from './clipboard-history-window';
import { SettingsWindow } from './settings-window';
import { unregisterAllShortcuts, registerGlobalShortcuts } from './shortcuts';
import { loadShortcutsFromDatabase, cleanupAppShortcuts } from '../ipc/handlers/shortcut-handler';
import { FLOATING_BALL, WINDOW, THEME, LANGUAGE, CLIPBOARD_SETTINGS, USER_STATUS } from '../../shared/ipc';
import { ipcMain, app, screen } from 'electron';
import { IWindowManager, IWindowManagerStatic } from '../types/window';
import * as log from '../logging';
import { FocusManager } from '../utils/focusManager';
import { PetWindow } from './pet-window';
import { AppSettings, ThemeType, LanguageType } from '@shared/types/settings';
import { settingsService } from '@main/services/settingsService';
import { getCrossWindowService } from '../services/cross-window-communication';
import getWinPosition from '../utils/getWinPosition';
import { AUTH } from '../../shared/ipc';

// 使用全局对象来存储窗口状态，以防止热重载问题
const g = global as any;
if (!g.aido_window_manager_state) {
  log.debug('Initializing global state for WindowManager');
  g.aido_window_manager_state = {
    mainWindow: null,
    floatingBall: null,
    screenCaptureWindow: null,
    multiScreenCaptureManager: null,
    petWindow: null,
    clipboardHistoryWindow: null,
    settingsWindow: null,
    isToggling: false,
  };
}

// 为 state 提供类型定义
const state: {
  mainWindow: MainWindow | null;
  floatingBall: FloatingBall | null;
  screenCaptureWindow: ScreenCaptureWindow | null;
  multiScreenCaptureManager: MultiScreenCaptureManager | null;
  petWindow: PetWindow | null;
  clipboardHistoryWindow: ClipboardHistoryWindow | null;
  settingsWindow: SettingsWindow | null;
  isToggling: boolean;
} = g.aido_window_manager_state;

/**
 * 确保在创建新窗口前销毁旧实例
 * @param windowName 要销毁的窗口名称
 */
function destroyOldInstance(windowName: keyof typeof state) {
  if (state[windowName] && !(state[windowName] as any).isDestroyed()) {
    log.debug(`[WindowManager] 发现一个已存在的 ${windowName} 实例，将强制销毁。`);
    (state[windowName] as any).destroy();
    (state[windowName] as any) = null;
  }
}

/**
 * 窗口管理器
 * 负责所有窗口的创建、显示、隐藏和协调
 */
export class WindowManager {
  /**
   * 初始化所有窗口
   */
  public static init(): void {
    try {
      destroyOldInstance('mainWindow');
      state.mainWindow = new MainWindow(this as unknown as IWindowManagerStatic);

      destroyOldInstance('floatingBall');
      state.floatingBall = new FloatingBall();

      destroyOldInstance('clipboardHistoryWindow');
      const currentSettings = settingsService.getSettings();
      state.clipboardHistoryWindow = new ClipboardHistoryWindow(currentSettings.clipboard.clipboardWindowStyle);

      this.registerFloatingBallClickIpc();

      registerGlobalShortcuts(this as unknown as IWindowManagerStatic);

      setTimeout(() => {
        loadShortcutsFromDatabase().catch(error => {
          log.error('加载应用快捷键失败:', error);
        });
      }, 200);

      setTimeout(() => {
        try {
          getCrossWindowService().updateWindowMap();
        } catch (error) {
          log.error('更新跨窗口通信服务窗口映射失败:', error);
        }
      }, 100);

      ipcMain.on(FLOATING_BALL.SHOW, () => WindowManager.getFloatingBall()?.show());
      ipcMain.on(FLOATING_BALL.HIDE, () => WindowManager.getFloatingBall()?.hide());
    } catch (error) {
      log.error('窗口管理器初始化失败:', error);
    }
  }

  /**
   * 注册浮动球点击事件处理程序
   */
  private static registerFloatingBallClickIpc(): void {
    ipcMain.removeAllListeners(FLOATING_BALL.CLICK);
    ipcMain.on(FLOATING_BALL.CLICK, () => {
      if (!state.mainWindow || state.mainWindow.isDestroyed()) {
        destroyOldInstance('mainWindow');
        state.mainWindow = new MainWindow(this as unknown as IWindowManagerStatic);
        getCrossWindowService().updateWindowMap();
      } else {
        this.toggleMainWindow();
      }
    });
  }

  /**
   * 显示主窗口
   */
  public static showMainWindow(): void {
    if (state.mainWindow && !state.mainWindow.isDestroyed() && state.mainWindow.isVisible()) {
      return;
    }
    log.info('显示主窗口');
    if (process.platform === 'darwin') {
      app.show();
      app.focus({ steal: true });
    }
    state.mainWindow?.show();
    state.mainWindow?.getWindow()?.webContents.send(WINDOW.SHOW_EVENT);
  }

  /**
   * 隐藏主窗口
   */
  public static hideMainWindow(): void {
    log.info('隐藏主窗口');
    state.mainWindow?.hide();
  }

  /**
   * 切换主窗口显示状态
   */
  public static toggleMainWindow(): void {
    state.isToggling = true;

    if (!state.mainWindow || state.mainWindow.isDestroyed()) {
      log.debug('窗口不存在或已销毁，创建新窗口');
      destroyOldInstance('mainWindow');
      state.mainWindow = new MainWindow(this as unknown as IWindowManagerStatic);
      getCrossWindowService().updateWindowMap();
      this.showMainWindow();
      setTimeout(() => { state.isToggling = false; }, 100);
      return;
    }

    const isVisible = state.mainWindow.isVisible();
    if (isVisible) {
      this.hideMainWindow();
    } else {
      this.showMainWindow();
    }
    setTimeout(() => { state.isToggling = false; }, 100);
  }

  /**
   * 显示剪贴板历史窗口
   */
  public static async showClipboardHistory(): Promise<void> {
    log.info('显示剪贴板历史窗口');
    const currentSettings = settingsService.getSettings();

    if (currentSettings.clipboard.clipboardWindowStyle === 'center-window') {
      log.info('使用主窗口显示剪贴板历史');
      if (state.clipboardHistoryWindow && !state.clipboardHistoryWindow.isDestroyed()) {
        state.clipboardHistoryWindow.destroy();
        state.clipboardHistoryWindow = null;
      }
      await this.showMainWindowClipboardHistory();
      return;
    }

    if (!state.clipboardHistoryWindow || state.clipboardHistoryWindow.isDestroyed()) {
      state.clipboardHistoryWindow = new ClipboardHistoryWindow(currentSettings.clipboard.clipboardWindowStyle);
      getCrossWindowService().updateWindowMap();
    }
    await state.clipboardHistoryWindow.show();
  }

  /**
   * 在主窗口中显示剪贴板历史
   */
  public static async showMainWindowClipboardHistory(): Promise<void> {
    log.info('在主窗口中显示剪贴板历史');
    try {
      await FocusManager.rememberCurrentFocus();
    } catch (error) {
      log.error('主窗口模式：焦点记录失败', error);
    }

    if (!state.mainWindow || state.mainWindow.isDestroyed()) {
      destroyOldInstance('mainWindow');
      state.mainWindow = new MainWindow(this as unknown as IWindowManagerStatic);
    }
    this.showMainWindow();

    const browserWindow = state.mainWindow.getWindow();
    if (browserWindow && !browserWindow.isDestroyed()) {
      browserWindow.webContents.send(WINDOW.SHOW_CLIPBOARD_HISTORY);
    }
  }

  /**
   * 隐藏剪贴板历史窗口
   */
  public static hideClipboardHistory(): void {
    log.info('隐藏剪贴板历史窗口');
    const currentSettings = settingsService.getSettings();

    if (currentSettings.clipboard.clipboardWindowStyle === 'center-window') {
      if (state.mainWindow && !state.mainWindow.isDestroyed()) {
        state.mainWindow.getWindow()?.webContents.send('clipboard-window-reset');
      }
      this.hideMainWindow();
      return;
    }
    state.clipboardHistoryWindow?.hide();
  }

  /**
   * 切换剪贴板历史窗口显示状态
   */
  public static async toggleClipboardHistory(): Promise<void> {
    const currentSettings = settingsService.getSettings();
    if (currentSettings.clipboard.clipboardWindowStyle === 'center-window') {
      if (!state.mainWindow || state.mainWindow.isDestroyed() || !state.mainWindow.isVisible()) {
        await this.showClipboardHistory();
      } else {
        this.hideClipboardHistory();
      }
      return;
    }
    if (!state.clipboardHistoryWindow || state.clipboardHistoryWindow.isDestroyed()) {
      await this.showClipboardHistory();
      return;
    }
    await state.clipboardHistoryWindow.toggle();
  }

  /**
   * 更新剪贴板历史窗口样式
   */
  public static async updateClipboardHistoryStyle(style?: string): Promise<void> {
    const currentSettings = settingsService.getSettings();
    const clipboardWindowStyle = style || currentSettings.clipboard.clipboardWindowStyle;
    const wasVisiblePrev = !!(state.clipboardHistoryWindow && !state.clipboardHistoryWindow.isDestroyed() && state.clipboardHistoryWindow.isVisible());

    if (clipboardWindowStyle === 'center-window') {
      if (state.clipboardHistoryWindow && !state.clipboardHistoryWindow.isDestroyed()) {
        state.clipboardHistoryWindow.destroy();
        state.clipboardHistoryWindow = null;
      }
      if (wasVisiblePrev) {
        await this.showMainWindowClipboardHistory();
      }
    } else {
      if (state.mainWindow && !state.mainWindow.isDestroyed() && state.mainWindow.isVisible()) {
        this.hideMainWindow();
      }
      if (state.clipboardHistoryWindow && !state.clipboardHistoryWindow.isDestroyed()) {
        state.clipboardHistoryWindow.updateStyle(clipboardWindowStyle as any);
        if (wasVisiblePrev) state.clipboardHistoryWindow.show();
      } else {
        state.clipboardHistoryWindow = new ClipboardHistoryWindow(clipboardWindowStyle as any);
        if (wasVisiblePrev) state.clipboardHistoryWindow.show();
      }
    }
  }

  public static getClipboardHistoryWindow(): ClipboardHistoryWindow | null { return state.clipboardHistoryWindow; }
  public static toggleDevTools(): void {
    state.mainWindow?.toggleDevTools();
    state.floatingBall?.toggleDevTools();
    state.clipboardHistoryWindow?.toggleDevTools();
  }

  /**
   * 在macOS下恢复窗口
   */
  public static restoreMainWindow(): void {
    if (!state.mainWindow || state.mainWindow.isDestroyed()) {
      destroyOldInstance('mainWindow');
      state.mainWindow = new MainWindow(this as unknown as IWindowManagerStatic);
    } else {
      this.showMainWindow();
    }
  }

  public static getMainWindow(): MainWindow | null { return state.mainWindow; }
  public static getFloatingBall(): FloatingBall | null { return state.floatingBall; }
  public static getPetWindow(): PetWindow | null { return state.petWindow; }

  /**
   * 获取或创建截图窗口实例
   */
  public static getScreenCaptureWindow(): ScreenCaptureWindow {
    if (!state.screenCaptureWindow) {
      state.screenCaptureWindow = new ScreenCaptureWindow(state.floatingBall ? state.floatingBall.getWindow() : null);
    }
    return state.screenCaptureWindow;
  }

  public static startScreenCapture(): void {
    if (!multiScreenDetector.getIsInitialized()) {
      log.warn('多屏检测器未初始化，使用单屏模式');
      this.getScreenCaptureWindow().startCapture();
      return;
    }
    if (multiScreenDetector.isMultiDisplayEnvironment()) {
      this.startMultiScreenCapture();
    } else {
      this.getScreenCaptureWindow().startCapture();
    }
  }

  public static startMultiScreenCapture(): void { this.getMultiScreenCaptureManager().startMultiScreenCapture(); }
  public static startSingleDisplayCapture(displayId: string): void { this.getMultiScreenCaptureManager().startSingleDisplayCapture(displayId); }

  public static getMultiScreenCaptureManager(): MultiScreenCaptureManager {
    if (!state.multiScreenCaptureManager) {
      state.multiScreenCaptureManager = new MultiScreenCaptureManager(state.floatingBall ? state.floatingBall.getWindow() : null);
    }
    return state.multiScreenCaptureManager;
  }

  public static closeScreenCaptureWindow(): void {
    state.multiScreenCaptureManager?.closeAllCaptureWindows();
    if (state.screenCaptureWindow) {
      state.screenCaptureWindow.closeWindow();
      state.screenCaptureWindow = null;
      log.info('截图窗口已关闭');
    }
  }

  public static closeAllWindows(): void {
    log.info('关闭所有窗口...');
    Object.keys(state).forEach(key => {
      const windowKey = key as keyof typeof state;
      if (state[windowKey] && typeof (state[windowKey] as any).destroy === 'function') {
        (state[windowKey] as any).destroy();
      }
    });
  }

  public static cleanup(): void {
    unregisterAllShortcuts();
    cleanupAppShortcuts();
    state.clipboardHistoryWindow?.destroy();
    state.settingsWindow?.close();
    getCrossWindowService().cleanup();
    Object.keys(state).forEach(key => {
      (state as any)[key] = null;
    });
  }

  public static setPetResizable(resizable: boolean): boolean {
    if (!state.petWindow) return false;
    state.petWindow.getWindow().setResizable(resizable);
    return true;
  }

  public static isPetResizable(): boolean {
    if (!state.petWindow) return false;
    return state.petWindow.getWindow().isResizable();
  }

  public static setPetIgnoreMouseEvents(ignore: boolean): boolean {
    if (!state.petWindow) return false;
    state.petWindow.getWindow().setIgnoreMouseEvents(ignore, { forward: true });
    return true;
  }

  public static getConfig(): any { return (global as any).config || {}; }

  public static setPinned(pinned: boolean): void { state.mainWindow?.setPinned(pinned); }
  public static isPinned(): boolean { return state.mainWindow?.isPinned() ?? false; }
  public static getIsToggling(): boolean { return state.isToggling; }

  public static showSettingsWindow(): void {
    log.info('显示设置窗口');
    if (!state.settingsWindow || state.settingsWindow.isDestroyed()) {
      state.settingsWindow = new SettingsWindow();
      getCrossWindowService().updateWindowMap();
    }
    state.settingsWindow.show();
  }

  public static hideSettingsWindow(): void {
    log.info('隐藏设置窗口');
    state.settingsWindow?.hide();
  }

  public static toggleSettingsWindow(): void {
    if (!state.settingsWindow || state.settingsWindow.isDestroyed()) {
      this.showSettingsWindow();
    } else {
      state.settingsWindow.toggle();
    }
  }

  public static closeSettingsWindow(): void {
    log.info('关闭设置窗口');
    if (state.settingsWindow && !state.settingsWindow.isDestroyed()) {
      state.settingsWindow.close();
      state.settingsWindow = null;
    }
  }

  public static getSettingsWindow(): SettingsWindow | null { return state.settingsWindow; }

  private static broadcastToAllWindows(channel: string, ...args: any[]): void {
    const windows = [
      state.mainWindow,
      state.settingsWindow,
      state.clipboardHistoryWindow,
      state.petWindow,
      state.floatingBall,
    ];
    windows.forEach(win => {
      if (win && !win.isDestroyed()) {
        const browserWindow = win.getWindow();
        if (browserWindow && !browserWindow.isDestroyed()) {
          browserWindow.webContents.send(channel, ...args);
        }
      }
    });
  }

  public static broadcastThemeChange(theme: ThemeType): void {
    this.broadcastToAllWindows(THEME.CHANGED, theme);
    console.log(`主题变化已广播到所有窗口: ${theme}`);
  }

  public static broadcastClipboardSettingsChange(clipboardSettings: any): void {
    this.broadcastToAllWindows(CLIPBOARD_SETTINGS.CHANGED, clipboardSettings);
    console.log(`剪贴板设置变化已广播到所有窗口:`, clipboardSettings);
  }

  public static broadcastUserStatusChange(user: any): void {
    this.broadcastToAllWindows(USER_STATUS.CHANGED, user);
    console.log(`用户状态变化已广播到所有窗口:`, user);
  }

  public static broadcastLanguageChange(language: LanguageType): void {
    this.broadcastToAllWindows(LANGUAGE.CHANGED, language);
    console.log(`语言变化已广播到所有窗口: ${language}`);
  }

  public static broadcastClearAllTokens(): void {
    this.broadcastToAllWindows(AUTH.CLEAR_ALL_TOKENS);
    console.log('✅ 清除token事件已广播到所有窗口');
  }

  public static setMainWindowCompactMode(compact: boolean): void {
    state.mainWindow?.setCompactMode(compact);
  }

  public static moveWindow(mouseData: { mouseX: number, mouseY: number, width: number, height: number }): void {
    const { x, y } = screen.getCursorScreenPoint();
    const originWindow = WindowManager.getMainWindow();
    if (!originWindow) return;
    originWindow.setBounds({ x: x - mouseData.mouseX, y: y - mouseData.mouseY, width: mouseData.width, height: mouseData.height });
    getWinPosition.setPosition(x - mouseData.mouseX, y - mouseData.mouseY);
  }
}