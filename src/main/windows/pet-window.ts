import { BrowserWindow, screen, ipcMain, app , clipboard, nativeImage } from 'electron';
import path from 'node:path';
import { format as formatUrl } from 'url';
import { isDevelopment, WINDOW_CONFIG, isMac, APP_PATH} from '../common';
import { WindowManager } from './window-manager';
import * as log from '../logging';
import windowStateKeeper from 'electron-window-state';

/**
 * 悬浮球窗口类
 * 管理悬浮球窗口的创建、位置、拖动等
 */
export class PetWindow {
  private window: BrowserWindow | null = null;
  private windowState: any = null;
  
  constructor() {
    this.createWindow();
  }
  
  /**
   * 创建悬浮球窗口
   */
  private createWindow(): void {
    // 保存和恢复窗口状态
    this.windowState = windowStateKeeper({
      defaultWidth: WINDOW_CONFIG.pet.width,
      defaultHeight: WINDOW_CONFIG.pet.height,
      file: 'pet-window-state.json'
    });
    
    // 创建无边框、透明背景的窗口
    this.window = new BrowserWindow({
      width: this.windowState.width,
      height: this.windowState.height,
      x: this.windowState.x,
      y: this.windowState.y,
      alwaysOnTop: true,
      autoHideMenuBar: true,
      transparent: true,
      frame: false,
      skipTaskbar: true,
      minimizable: false,
      maximizable: false,
      resizable: false,
      fullscreenable: false,
      show: true,
      hasShadow: false,
      webPreferences: {
        preload: path.join(APP_PATH, '.vite/build/preload.js'),
        contextIsolation: true,
        nodeIntegration: false,
        devTools: isDevelopment,
        backgroundThrottling: false
      },
    });
    
    // 管理窗口状态
    this.windowState.manage(this.window);

    // 设置鼠标事件穿透，但允许特定区域（悬浮球本身）接收点击
    // this.window.setIgnoreMouseEvents(true, { forward: true });
    
    // macOS 特有的设置
    if (isMac) {
      app.dock.hide(); // 隐藏dock图标
    }
    
    this.loadContent();
    
    // 仅在开发环境下自动打开开发者工具
    if (isDevelopment) {
      // 打开开发者工具
      this.window.webContents.openDevTools({ mode: 'detach' });
    }
    
    log.debug('桌宠窗口创建成功');
  }
  
  /**
   * 加载窗口内容
   */
  private loadContent(): void {
    if (!this.window) return;
    
    if (isDevelopment) {
      // 添加重试机制，确保Vite服务器已启动
      const tryLoadURL = (retryCount = 0, maxRetries = 10) => {
        this.window?.loadURL('http://localhost:5173/#/pet')
          .then(() => {
            log.debug('桌宠成功连接到Vite开发服务器');
          })
          .catch(err => {
            log.error(`桌宠连接Vite服务器失败(${retryCount}): ${err.message}`);
            if (retryCount < maxRetries) {
              log.debug(`桌宠${retryCount + 1}秒后重试连接...`);
              setTimeout(() => tryLoadURL(retryCount + 1), 1000);
            } else {
              log.error('桌宠达到最大重试次数，无法连接Vite服务器');
            }
          });
      };
      
      tryLoadURL();
    } else {
      const htmlPath = path.join(APP_PATH, '.vite/renderer/main_window/index.html');
      this.window.loadURL(formatUrl({
        pathname: htmlPath,
        protocol: 'file:',
        slashes: true,
        hash: '/pet'
      }));
    }
  }
  
    /**
   * 切换窗口开发者工具
   */
    public toggleDevTools(): void {
      if (!isDevelopment || !this.window || this.window.isDestroyed()) return;
  
      const devToolsOpened = this.window.webContents.isDevToolsOpened();
      if (devToolsOpened) {
        this.window.webContents.closeDevTools();
      } else {
        // 确保窗口可见
        if (!this.window.isVisible()) {
          this.show();
        }
        this.window.webContents.openDevTools({ mode: 'detach' });
      }
    }
  
  /**
   * 显示桌宠
   */
  public show(): void {
    if (this.window && !this.window.isDestroyed()) {
      this.window.show();
    } else {
      this.createWindow();
    }
  }
  
  /**
   * 隐藏桌宠
   */
  public hide(): void {
    if (this.window && !this.window.isDestroyed()) {
      this.window.hide();
    }
  }
  
  /**
   * 切换桌宠显示状态
   */
  public toggle(): void {
    if (!this.window) {
      this.createWindow();
      return;
    }
    
    if (this.window.isVisible()) {
      this.hide();
    } else {
      this.show();
    }
  }
  
  /**
   * 检查桌宠是否可见
   */
  public isVisible(): boolean {
    return this.window ? this.window.isVisible() : false;
  }
  
  /**
   * 获取桌宠窗口实例
   */
  public getWindow(): BrowserWindow | null {
    return this.window;
  }

  /**
   * 检查窗口是否已销毁
   */
  public isDestroyed(): boolean {
    return !this.window || this.window.isDestroyed();
  }

  /**
   * 销毁窗口
   */
  public destroy(): void {
    if (this.window && !this.window.isDestroyed()) {
      this.window.destroy();
      this.window = null;
    }
  }
} 