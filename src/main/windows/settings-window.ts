import { BrowserWindow, screen, app, Menu, MenuItem } from 'electron';
import path from 'node:path';
import { APP_PATH, isDevelopment, WINDOW_CONFIG } from '../common';
import * as log from '../logging';

/**
 * 设置窗口类
 * 负责设置窗口的创建和管理
 */
export class SettingsWindow {
  private window?: BrowserWindow | null = null;

  constructor() {
    this.createWindow();
  }

  /**
   * 创建设置窗口
   */
  private createWindow(): void {
    this.window = new BrowserWindow({
      width: 900,
      height: 700,
      minWidth: 800,
      minHeight: 600,
      transparent: false,
      frame: true,
      resizable: true,
      show: false,
      alwaysOnTop: false,
      skipTaskbar: false,
      titleBarStyle: process.platform === 'darwin' ? 'default' : 'default',
      webPreferences: {
        preload: path.join(APP_PATH, '.vite/build/preload.js'),
        contextIsolation: true,
        nodeIntegration: false,
        devTools: isDevelopment,
        webSecurity: !isDevelopment
      },
    });

    this.loadContent();
    this.setupEventListeners();
    this.setupContextMenu();

    // 定位窗口到屏幕中央
    const primaryDisplay = screen.getPrimaryDisplay();
    const { width, height } = primaryDisplay.workAreaSize;
    const x = Math.floor(width / 2 - 900 / 2);
    const y = Math.floor(height / 2 - 700 / 2);
    this.window.setPosition(x, y);

    log.debug('设置窗口创建成功');
  }

  /**
   * 加载窗口内容
   */
  private loadContent(): void {
    if (!this.window) return;

    if (isDevelopment) {
      // 添加重试机制，确保Vite服务器已启动
      const tryLoadURL = (retryCount = 0, maxRetries = 10) => {
        this.window?.loadURL('http://localhost:5173/#/settings')
          .then(() => {
            log.debug('设置窗口成功连接到Vite开发服务器');
          })
          .catch((err: Error) => {
            log.error(`设置窗口连接Vite服务器失败(${retryCount}): ${err.message}`);
            if (retryCount < maxRetries) {
              log.debug(`设置窗口${retryCount + 1}秒后重试连接...`);
              setTimeout(() => tryLoadURL(retryCount + 1), 1000);
            } else {
              log.error('设置窗口达到最大重试次数，无法连接Vite服务器');
            }
          });
      };
      
      tryLoadURL();
    } else {
      // 根据应用路径构建HTML路径
      const htmlPath = path.join(APP_PATH, '.vite/renderer/main_window/index.html');
      this.window.loadURL(`file://${htmlPath}#/settings`).catch((err:Error) => {
        log.error('设置窗口加载HTML失败:', err);
      });
    }
  }

  /**
   * 设置右键菜单
   */
  private setupContextMenu(): void {
    if (!this.window) return;

    this.window.webContents.on('context-menu', () => {
      const menu = new Menu();
      
      // 添加开发者工具选项
      menu.append(new MenuItem({
        label: '打开开发者工具',
        click: () => {
          if (this.window && !this.window.isDestroyed()) {
            this.window.webContents.toggleDevTools();
          }
        }
      }));

      menu.popup();
    });
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    if (!this.window) return;

    // 窗口关闭时的处理
    this.window.on('closed', () => {
      this.window = null;
      log.debug('设置窗口已关闭');
    });

    // 窗口准备显示时
    this.window.once('ready-to-show', () => {
      log.debug('设置窗口准备显示');
    });

    // 页面加载完成后注入CSS样式，防止页面选择干扰拖拽
    this.window.webContents.once('did-finish-load', () => {
      // 注入CSS来禁用页面选择，确保拖拽正常工作
      this.window?.webContents.insertCSS(`
        body, html {
          -webkit-user-select: none;
          -webkit-app-region: no-drag;
          user-select: none;
        }
        
        /* 确保可以选择文本输入框内容 */
        input, textarea, [contenteditable] {
          -webkit-user-select: text;
          user-select: text;
        }
        
        /* 设置窗口可拖拽区域为顶部区域 */
        .settings-header, .sidebar-header {
          -webkit-app-region: drag;
        }
        
        /* 确保按钮和其他交互元素不被拖拽 */
        button, a, input, textarea, select, [role="button"] {
          -webkit-app-region: no-drag;
        }
      `);

      // 开发环境下自动打开开发者工具
      if (isDevelopment) {
        setTimeout(() => {
          this.window?.webContents.openDevTools({ mode: 'detach' });
        }, 500);
      }
    });
  }

  /**
   * 显示窗口
   */
  public show(): void {
    if (!this.window || this.window.isDestroyed()) {
      this.createWindow();
    }

    if (this.window) {
      this.window.show();
      this.window.focus();
    }
  }

  /**
   * 隐藏窗口
   */
  public hide(): void {
    if (this.window && !this.window.isDestroyed()) {
      this.window.hide();
    }
  }

  /**
   * 关闭窗口
   */
  public close(): void {
    if (this.window && !this.window.isDestroyed()) {
      this.window.close();
    }
  }

  /**
   * 切换窗口显示状态
   */
  public toggle(): void {
    if (!this.window || this.window.isDestroyed()) {
      this.show();
    } else if (this.window.isVisible()) {
      this.hide();
    } else {
      this.show();
    }
  }

  /**
   * 检查窗口是否可见
   */
  public isVisible(): boolean {
    return this.window ? this.window.isVisible() : false;
  }

  /**
   * 检查窗口是否已销毁
   */
  public isDestroyed(): boolean {
    return !this.window || this.window.isDestroyed();
  }

  /**
   * 获取窗口实例
   */
  public getWindow(): Electron.BrowserWindow | null {
    return this.window;
  }

  /**
   * 切换开发者工具
   */
  public toggleDevTools(): void {
    if (this.window && !this.window.isDestroyed()) {
      this.window.webContents.toggleDevTools();
    }
  }
} 