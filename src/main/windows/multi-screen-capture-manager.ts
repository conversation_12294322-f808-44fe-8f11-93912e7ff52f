import { BrowserWindow } from 'electron';
import { ScreenCaptureWindow } from './screen-capture';
import { multiScreenDetector, DisplayInfo } from '../utils/multi-screen-detector';
import { SCREEN_CAPTURE } from '../../shared/ipc';
import * as log from '../logging';

/**
 * 多屏截图管理器
 * 负责管理多个显示器的截图功能
 */
export class MultiScreenCaptureManager {
  private captureWindows: Map<string, ScreenCaptureWindow> = new Map();
  private floatingBallWindow: BrowserWindow | null = null;
  private isCapturing: boolean = false;

  constructor(floatingBallWindow: BrowserWindow | null = null) {
    this.floatingBallWindow = floatingBallWindow;
    log.info('多屏截图管理器已创建');
  }



  /**
   * 启动多屏截图模式
   * 在所有显示器上同时显示截图界面
   */
  public async startMultiScreenCapture(): Promise<void> {
    if (this.isCapturing) {
      log.warn('多屏截图已在进行中');
      return;
    }

    if (!multiScreenDetector.getIsInitialized()) {
      log.error('多屏检测器未初始化，无法启动多屏截图');
      return;
    }

    this.isCapturing = true;
    const startTime = Date.now();
    log.info(`[性能] 启动多屏截图模式，开始时间: ${startTime}`);

    try {
      const getDisplaysStart = Date.now();
      const displays = multiScreenDetector.getAllDisplays();
      const getDisplaysEnd = Date.now();
      log.info(`[性能] 获取显示器列表耗时: ${getDisplaysEnd - getDisplaysStart}ms，共${displays.length}个显示器`);

      // 并行为所有显示器创建截图窗口（性能优化）
      const createPromisesStart = Date.now();
      const createWindowPromises = displays.map((display, index) => {
        log.info(`[性能] 开始创建显示器${index + 1}(${display.name})的截图窗口`);
        return this.createCaptureWindowForDisplay(display);
      });
      const createPromisesEnd = Date.now();
      log.info(`[性能] 创建Promise数组耗时: ${createPromisesEnd - createPromisesStart}ms`);

      // 等待所有窗口创建完成
      const waitStart = Date.now();
      await Promise.all(createWindowPromises);
      const waitEnd = Date.now();
      log.info(`[性能] 等待所有窗口创建完成耗时: ${waitEnd - waitStart}ms`);

      // 通知浮动球窗口进入截图模式
      const notifyStart = Date.now();
      if (this.floatingBallWindow && !this.floatingBallWindow.isDestroyed()) {
        this.floatingBallWindow.webContents.send(SCREEN_CAPTURE.CHANGE, true);
      }
      const notifyEnd = Date.now();
      log.info(`[性能] 通知浮动球耗时: ${notifyEnd - notifyStart}ms`);

      // 性能日志
      const endTime = Date.now();
      log.info(`[性能] 多屏截图启动完成，总耗时: ${endTime - startTime}ms`);

    } catch (error) {
      log.error('启动多屏截图失败', error);
      this.isCapturing = false;
      this.closeAllCaptureWindows();
    }
  }

  /**
   * 启动单个显示器截图
   * @param displayId 显示器ID
   */
  public async startSingleDisplayCapture(displayId: string): Promise<void> {
    if (this.isCapturing) {
      log.warn('截图已在进行中');
      return;
    }

    if (!multiScreenDetector.getIsInitialized()) {
      log.error('多屏检测器未初始化，无法启动单显示器截图');
      return;
    }

    this.isCapturing = true;
    log.info(`启动单显示器截图: ${displayId}`);

    try {
      const display = multiScreenDetector.getDisplayById(displayId);
      if (!display) {
        throw new Error(`找不到显示器: ${displayId}`);
      }

      await this.createCaptureWindowForDisplay(display);

      // 通知浮动球窗口进入截图模式
      if (this.floatingBallWindow && !this.floatingBallWindow.isDestroyed()) {
        this.floatingBallWindow.webContents.send(SCREEN_CAPTURE.CHANGE, true);
      }

    } catch (error) {
      log.error('启动单显示器截图失败', error);
      this.isCapturing = false;
      this.closeAllCaptureWindows();
    }
  }

  /**
   * 为指定显示器创建截图窗口
   * @param display 显示器信息
   */
  private async createCaptureWindowForDisplay(display: DisplayInfo): Promise<void> {
    const windowStartTime = Date.now();
    log.info(`[性能] 开始为显示器 ${display.name} 创建截图窗口，时间: ${windowStartTime}`);

    try {
      // 创建截图窗口，传入显示器信息
      // 将DisplayInfo转换为Electron的Display格式
      const convertStart = Date.now();
      const electronDisplay = {
        id: display.electronId,
        bounds: display.bounds,
        size: display.size,
        workArea: display.workArea,
        workAreaSize: display.workAreaSize,
        scaleFactor: display.scaleFactor,
        rotation: display.rotation,
        touchSupport: display.touchSupport,
        monochrome: display.monochrome,
        accelerometerSupport: display.accelerometerSupport,
        colorSpace: display.colorSpace,
        colorDepth: display.colorDepth,
        depthPerComponent: display.depthPerComponent,
        displayFrequency: display.displayFrequency,
        label: display.label,
        internal: display.internal,
        detected: true,
        maximumCursorSize: { width: 32, height: 32 },
        nativeOrigin: { x: 0, y: 0 }
      };
      const convertEnd = Date.now();
      log.info(`[性能] 显示器信息转换耗时: ${convertEnd - convertStart}ms`);

      const createStart = Date.now();
      const captureWindow = new ScreenCaptureWindow(
        this.floatingBallWindow,
        electronDisplay
      );
      const createEnd = Date.now();
      log.info(`[性能] ScreenCaptureWindow实例创建耗时: ${createEnd - createStart}ms`);

      // 启动截图（跳过浮动球通知，稍后统一通知）
      const startCaptureStart = Date.now();
      captureWindow.startCapture(true);
      const startCaptureEnd = Date.now();
      log.info(`[性能] startCapture调用耗时: ${startCaptureEnd - startCaptureStart}ms`);

      // 存储窗口实例
      const storeStart = Date.now();
      this.captureWindows.set(display.id, captureWindow);
      const storeEnd = Date.now();
      log.info(`[性能] 存储窗口实例耗时: ${storeEnd - storeStart}ms`);

      const windowEndTime = Date.now();
      log.info(`[性能] 显示器 ${display.name} 截图窗口创建完成，总耗时: ${windowEndTime - windowStartTime}ms`);

    } catch (error) {
      const windowErrorTime = Date.now();
      log.error(`[性能] 显示器 ${display.name} 创建截图窗口失败，耗时: ${windowErrorTime - windowStartTime}ms，错误:`, error);
      throw error;
    }
  }

  /**
   * 关闭所有截图窗口
   */
  public closeAllCaptureWindows(): void {
    log.info('关闭所有截图窗口');
    
    this.captureWindows.forEach((window, displayId) => {
      try {
        window.closeWindow();
      } catch (error) {
        log.error(`关闭显示器 ${displayId} 的截图窗口失败`, error);
      }
    });
    
    this.captureWindows.clear();
    this.isCapturing = false;

    // 通知浮动球窗口退出截图模式
    if (this.floatingBallWindow && !this.floatingBallWindow.isDestroyed()) {
      this.floatingBallWindow.webContents.send(SCREEN_CAPTURE.CHANGE, false);
    }
  }

  /**
   * 检查是否正在截图
   */
  public isCapturingScreen(): boolean {
    return this.isCapturing;
  }

  /**
   * 获取活动的截图窗口数量
   */
  public getActiveCaptureWindowCount(): number {
    return this.captureWindows.size;
  }
}

/**
 * 为了简化实现，我们直接使用原始的ScreenCaptureWindow类
 * 但传入特定显示器的信息
 */
