import { BrowserWindow, screen, ipcMain, app, desktopCapturer, Display } from 'electron';
import path from 'node:path';
import { format as formatUrl } from 'url';
import { isDevelopment, isMac, APP_PATH } from '../common';
import { SCREEN_CAPTURE } from '../../shared/ipc';
import * as log from '../logging';

/**
 * 屏幕截图窗口类
 * 用于管理屏幕截图相关功能
 */
export class ScreenCaptureWindow {
  protected window: BrowserWindow | null = null;
  protected floatingBallWindow: BrowserWindow | null = null;
  protected isCapturing: boolean = false;
  protected ipcHandlersRegistered: boolean = false;
  protected targetDisplay: Display | null = null;

  constructor(floatingBallWindow: BrowserWindow | null = null, targetDisplay: Display | null = null) {
    this.floatingBallWindow = floatingBallWindow;
    this.targetDisplay = targetDisplay;
    this.registerIpcHandlers();
  }
  /**
   * 创建截图窗口
   */
  public createWindow(): void {
    const targetDisplay = this.targetDisplay || screen.getPrimaryDisplay();
    const { width, height } = targetDisplay.size;
    const { x, y } = targetDisplay.bounds;

    this.window = new BrowserWindow({
      width,
      height,
      x,
      y,
      transparent: true,
      frame: false,
      alwaysOnTop: true,
      skipTaskbar: true,
      movable: false,
      resizable: false,
      minimizable: false,
      type: 'panel',
      hasShadow: false,
      enableLargerThanScreen: true,
      webPreferences: {
        preload: path.join(APP_PATH, '.vite/build/preload.js'),
        contextIsolation: true,
        nodeIntegration: false,
        devTools: isDevelopment,
      }
    });
    if (isMac) {
      this.window.setVisibleOnAllWorkspaces(true, { visibleOnFullScreen: true });
      this.window.setFullScreenable(false);
      this.window.setWindowButtonVisibility(false);
      this.window.setAlwaysOnTop(true, 'pop-up-menu', 2);
    }
    this.window.show();
    this.window.focus();
    this.loadContent();
  }
  
  /**
   * 加载窗口内容
   */
  private loadContent(): void {
    if (!this.window) return;

    if (isDevelopment) {
      this.window.loadURL('http://localhost:5173/#/screen-capture');
    } else {
      const htmlPath = path.join(APP_PATH, '.vite/renderer/main_window/index.html');
      const url = formatUrl({
        pathname: htmlPath,
        protocol: 'file:',
        slashes: true,
        hash: '/screen-capture'
      });
      this.window.loadURL(url);
    }
  }
  
  /**
   * 注册截图相关的IPC处理程序
   */
  private registerIpcHandlers(): void {
    if (this.ipcHandlersRegistered) {
      return; // 已经注册过了
    }
    this.ipcHandlersRegistered = true;
    // 处理开始截图
    ipcMain.on(SCREEN_CAPTURE.START, async () => {
      if (this.isCapturing) return;
      this.isCapturing = true;
      try {
        // 隐藏窗口进行截图
        if (this.window && !this.window.isDestroyed()) {
          this.window.hide();
        }

        // 快速屏幕捕获
        const targetDisplay = this.targetDisplay || screen.getPrimaryDisplay();
        const sources = await desktopCapturer.getSources({
          types: ['screen'],
          thumbnailSize: {
            width: targetDisplay.size.width,
            height: targetDisplay.size.height
          },
          fetchWindowIcons: false
        });

        const targetSource = this.findScreenSourceForDisplay(sources, targetDisplay);

        if (targetSource && this.window && !this.window.isDestroyed()) {
          const imageDataUrl = targetSource.thumbnail.toDataURL();
          this.window.show();
          this.window.webContents.send(SCREEN_CAPTURE.IMAGE, {
            imageData: imageDataUrl
          });
        } else {
          this.closeWindow();
        }
      } catch (error) {
        this.closeWindow();
      } finally {
        this.isCapturing = false;
      }
    });
    
  }
  
  /**
   * 关闭截图窗口
   */
  public closeWindow(): void {
    if (this.window && !this.window.isDestroyed()) {
      this.window.close();
      this.window = null;
    }
    // 重置捕获标志
    this.isCapturing = false;
    // 如果浮动球窗口存在，显示它
    if (this.floatingBallWindow && !this.floatingBallWindow.isDestroyed()) {
      this.floatingBallWindow.show();
    }
  }
  
  /**
   * 启动截图模式
   */
  public startCapture(skipFloatingBallNotification: boolean = false): void {
    // 关闭旧窗口
    if (this.window && !this.window.isDestroyed()) {
      this.window.close();
      this.window = null;
    }

    // 创建新窗口
    this.createWindow();

    // 通知浮动球
    if (!skipFloatingBallNotification && this.floatingBallWindow && !this.floatingBallWindow.isDestroyed()) {
      this.floatingBallWindow.webContents.send(SCREEN_CAPTURE.CHANGE, true);
    }
  }

  /**
   * 检查窗口是否已销毁
   */
  public isDestroyed(): boolean {
    return !this.window || this.window.isDestroyed();
  }

  /**
   * 查找指定显示器对应的屏幕源
   * @param sources 屏幕源列表
   * @param display 目标显示器
   * @returns 对应的屏幕源，如果找不到则返回第一个源
   */
  protected findScreenSourceForDisplay(sources: Electron.DesktopCapturerSource[], display: Display): Electron.DesktopCapturerSource | null {
    if (sources.length === 0) {
      return null;
    }

    // 如果只有一个屏幕源，直接返回
    if (sources.length === 1) {
      return sources[0];
    }

    // 尝试匹配显示器ID
    // 注意：desktopCapturer返回的源ID格式通常为"screen:x:y"，其中x可能与显示器ID相关
    for (const source of sources) {
      const sourceIdParts = source.id.split(':');
      if (sourceIdParts.length > 1) {
        const sourceDisplayId = parseInt(sourceIdParts[1], 10);
        if (sourceDisplayId === display.id) {
          return source;
        }
      }
    }

    // 如果无法匹配ID，则尝试通过显示器名称匹配
    if (display.label) {
      for (const source of sources) {
        if (source.name.includes(display.label)) {
          return source;
        }
      }
    }

    // 如果都无法匹配，返回第一个源
    log.warn(`无法为显示器 ${display.id} 找到匹配的屏幕源，使用第一个源`);
    return sources[0];
  }

  /**
   * 销毁窗口
   */
  public destroy(): void {
    if (this.window && !this.window.isDestroyed()) {
      this.window.destroy();
      this.window = null;
    }
    this.isCapturing = false;
  }
}