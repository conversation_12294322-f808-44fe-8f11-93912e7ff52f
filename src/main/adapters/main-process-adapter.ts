/**
 * 主进程专用适配器 - 使用静态导入避免 Vite 警告
 */

import { BrowserWindow } from 'electron';
import { getMainProcessToken, getMainProcessComputerId, updateFieldCache } from '../services/rendererFieldService';
import { systemService } from '../services/systemService';

export class MainProcessAdapter {
  async getTokenAsync(): Promise<string | null> {
    try {
      console.log('🔍 [主进程] 开始获取token...');
      const token = await getMainProcessToken();
      
      console.log('🔍 [主进程] 获取token结果:', {
        hasToken: !!token,
        tokenPreview: token ? `${token.substring(0, 20)}...` : 'null',
        time: new Date().toLocaleTimeString()
      });
      
      return token;
    } catch (error) {
      console.error('🔍 [主进程] 获取token失败:', error);
      return null;
    }
  }

  async getComputerId(): Promise<string | null> {
    try {
      // 首先尝试从systemService直接获取
      try {
        const computerId = await systemService.getComputerId();
        console.log('🔍 主进程直接获取computerId:', computerId ? '✅ 成功' : '❌ 失败');
        if (computerId) {
          return computerId;
        }
      } catch (directError) {
        console.log('🔍 主进程直接获取computerId失败，尝试从渲染进程获取:', directError);
      }

      // 如果直接获取失败，再尝试从渲染进程获取
      const rendererComputerId = await getMainProcessComputerId();
      console.log('🔍 从渲染进程获取computerId:', rendererComputerId ? '✅ 成功' : '❌ 失败');
      return rendererComputerId;
    } catch (error) {
      console.error('主进程获取computerId失败:', error);
      return null;
    }
  }

  updateToken(token: string): void {
    // 主线程需要通过IPC更新渲染进程的token
    try {
      // 更新缓存
      updateFieldCache('token', token);
      console.log('🔄 主进程Token缓存更新成功');

      // 同时通知渲染进程更新localStorage
      const mainWindow = BrowserWindow.getAllWindows().find(win => !win.isDestroyed());
      if (mainWindow) {
        mainWindow.webContents.executeJavaScript(`
          (() => {
            try {
              const authData = localStorage.getItem('auth-storage');
              if (authData) {
                const parsed = JSON.parse(authData);
                const data = parsed.state || parsed;
                if (data) {
                  data.token = '${token}';
                  localStorage.setItem('auth-storage', JSON.stringify({ state: data }));
                  console.log('🔄 渲染进程Token更新成功');
                }
              }
            } catch (error) {
              console.error('渲染进程更新token失败:', error);
            }
          })()
        `).catch(error => {
          console.error('执行渲染进程token更新脚本失败:', error);
        });
      }
    } catch (error) {
      console.error('主进程updateToken操作失败:', error);
    }
  }
}
