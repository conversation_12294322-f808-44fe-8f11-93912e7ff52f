import { crashReporter } from 'electron';
import log from 'electron-log';
import path from 'node:path'
import { LOGS_PATH } from './common'
import fs from 'fs'

// 我设置 console 和 file 传输器的日志级别为 info，过滤掉 debug 及以下级别
// log.transports.console.level = 'info';
// log.transports.file.level = 'info';

export function init() {
  // 初始化 Electron crashReporter
  if (process.env.NODE_ENV !== 'development') {
    crashReporter.start({
      productName: 'Aido',
      companyName: 'SilliconGeek',
      submitURL: 'https://your-crash-server.com/submit', // 替换为你的崩溃报告服务器URL
      uploadToServer: true,
      ignoreSystemCrashHandler: false,
      rateLimit: false,
      compress: true
    });
    
    log.info('📊 CrashReporter 已启动');
  }
  
  // Windows 系统编码设置
  if (process.platform === 'win32') {
    // 设置控制台输出格式，解决中文乱码问题
    log.transports.console.format = '{y}-{m}-{d} {h}:{i}:{s}.{ms} > {text}';
    
    // 为文件传输器设置 UTF-8 编码
    log.transports.file.format = '{y}-{m}-{d} {h}:{i}:{s}.{ms} [{level}] {text}';
    
    // 确保日志输出使用 UTF-8 编码
    const originalWrite = process.stdout.write;
    process.stdout.write = function(string: any, encoding?: any, fd?: any) {
      if (typeof string === 'string') {
        // 确保字符串以 UTF-8 编码输出
        return originalWrite.call(this, string, 'utf8', fd);
      }
      return originalWrite.call(this, string, encoding, fd);
    };
  }
  
  // 设置日志文件路径
  const logFilePath = path.join(LOGS_PATH, 'app.log');
  log.transports.file.resolvePath = () => logFilePath;
  
  // 确保日志目录存在
  if (!fs.existsSync(LOGS_PATH)) {
    fs.mkdirSync(LOGS_PATH, { recursive: true });
  }
  
  // 输出日志文件路径到控制台，方便开发时查找
  log.info('📝 日志文件路径:', logFilePath);
}

export function captureException(error: Error | string) {
  log.error(error);
  
  // 使用 crashReporter 添加额外的崩溃信息
  if (process.env.NODE_ENV !== 'development') {
    const errorInfo = {
      error_type: 'exception',
      error_message: error instanceof Error ? error.message : String(error),
      error_stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString()
    };
    
    // 添加额外的崩溃参数
    crashReporter.addExtraParameter('last_error', JSON.stringify(errorInfo));
    
    log.info('💥 错误信息已添加到崩溃报告');
  }
}

export function captureWarning(warning: any) {
  log.warn(warning);
  
  // 对于警告，我们只记录到日志，不触发崩溃报告
  if (process.env.NODE_ENV !== 'development') {
    const warningInfo = {
      warning_type: 'warning',
      warning_message: String(warning),
      timestamp: new Date().toISOString()
    };
    
    crashReporter.addExtraParameter('last_warning', JSON.stringify(warningInfo));
  }
}

export function debug(...messages: any[]) {
  log.debug(messages);
}

export function info(...messages: any[]) {
  log.info(...messages);
}

export function warn(...messages: any[]) {
  log.warn(...messages);
}

export function error(...messages: any[]) {
  log.error(...messages);
}

// 新增：手动触发崩溃报告（用于测试）
export function triggerCrashReport(reason: string) {
  if (process.env.NODE_ENV !== 'development') {
    crashReporter.addExtraParameter('manual_crash_reason', reason);
    log.info('🚨 手动触发崩溃报告:', reason);
  }
}

// 新增：获取崩溃报告参数
export function getCrashReporterParameters() {
  if (process.env.NODE_ENV !== 'development') {
    return crashReporter.getParameters();
  }
  return {};
}
