/* eslint-disable no-console */
import Database, { Statement } from 'better-sqlite3';
import path from 'path';
import { USER_DATA_PATH } from '../common';
import * as logging from '../logging';
import { getAllProjectToolsList } from './apiService';
import type { ClipboardItem } from '../../shared/types/clipboard';
import type { MCPTool } from '../../shared/types/mcpTool';
logging.info('userData目录:', USER_DATA_PATH);

class DbService {
  private db: Database.Database;
  private dbPath: string;

  constructor() {
    this.dbPath = path.join(USER_DATA_PATH, 'aido.db');
    this.db = new Database(this.dbPath);
    this.initialize();
  }

  private initialize(): void {
    const initDatabase = this.db.transaction(() => {
      logging.debug('Initializing database...');

      this.db.pragma('foreign_keys = ON');
      this.createTableFolders();
      this.createTableChats();
      this.createTableMessages();
      this.createTableBookmarks();
      this.createTablePrompts();
      this.createTableUsages();
      this.createTableKnowledgeCollections();
      this.createTableKnowledgeFiles();
      this.createTableChatKnowledgeRels();
      this.createTableMcpServers();
      this.createTableMcpServerTools();
      this.createTableClipboardHistory();
      // v1.0.0
      this.alertTableFolders();
      this.alertTableMcpServers();
      logging.info('Database initialized.');
    });

    this.db.pragma('journal_mode = WAL'); // performance reason
    initDatabase();
  }

  private createTableFolders(): void {
    this.db
      .prepare(
        `
    CREATE TABLE IF NOT EXISTS "folders" (
      "id" text(31) NOT NULL,
      "name" text,
      "provider" text,
      "model" text,
      "systemMessage" text,
      "temperature" real,
      "maxTokens" integer,
      "knowledgeCollectionIds" text,
      "stream" integer(1) DEFAULT 1,
      "maxCtxMessages" integer DEFAULT 10,
      "createdAt" integer,
      PRIMARY KEY ("id")
    )`,
      )
      .run();
  }

  private createTableChats(): void {
    this.db
      .prepare(
        `
    CREATE TABLE IF NOT EXISTS "chats" (
      "id" text(31) NOT NULL,
      "folderId" text(31),
      "name" text,
      "summary" text,
      "provider" text,
      "model" text,
      "systemMessage" text,
      "temperature" real,
      "maxTokens" integer,
      "stream" integer(1) DEFAULT 1,
      "context" text,
      "maxCtxMessages" integer DEFAULT 10,
      "prompt" TEXT,
      "input" TEXT,
      "createdAt" integer,
      PRIMARY KEY ("id")
    )`,
      )
      .run();
  }

  private createTableMessages(): void {
    this.db
      .prepare(
        `CREATE TABLE IF NOT EXISTS "messages" (
        "id" text(31) NOT NULL,
        "prompt" TEXT COLLATE NOCASE,
        "reply" TEXT COLLATE NOCASE,
        "reasoning" TEXT,
        "inputTokens" integer,
        "outputTokens" integer,
        "chatId" text(31),
        "temperature" real,
        "model" text,
        "memo" text,
        "createdAt" integer,
        "isActive" integer(1),
        "citedFiles"	TEXT,
        "citedChunks"	TEXT,
        "maxTokens" INTEGER,
        PRIMARY KEY ("id"),
        CONSTRAINT "fk_messages_chats" FOREIGN KEY ("chatId") REFERENCES "chats" ("id") ON DELETE CASCADE ON UPDATE CASCADE
      )`,
      )
      .run();
  }

  private createTableBookmarks(): void {
    this.db
      .prepare(
        `CREATE TABLE IF NOT EXISTS "bookmarks" (
      "id" text(31) NOT NULL,
      "msgId" text NOT NULL,
      "prompt" TEXT,
      "reply" TEXT,
      "reasoning" TEXT,
      "temperature" real,
      "model" text,
      "memo" text,
      "favorite" integer(1) DEFAULT 0,
      "citedFiles"	TEXT,
      "citedChunks"	TEXT,
      "createdAt" integer,
      PRIMARY KEY ("id"),
      CONSTRAINT "uix_msg_id" UNIQUE ("msgId" COLLATE BINARY ASC)
    )`,
      )
      .run();
  }

  private createTablePrompts(): void {
    this.db
      .prepare(
        `CREATE TABLE IF NOT EXISTS "prompts" (
      "id" text(31) NOT NULL,
      "name" text,
      "systemMessage" TEXT,
      "userMessage" text,
      "systemVariables" text,
      "userVariables" text,
      "models" text,
      "temperature" real,
      "maxTokens" integer,
      "createdAt" integer,
      "updatedAt" integer,
      "pinedAt" integer DEFAULT NULL,
      PRIMARY KEY ("id")
    )`,
      )
      .run();
  }

  private createTableUsages(): void {
    this.db
      .prepare(
        `CREATE TABLE IF NOT EXISTS "usages" (
      "id" text(31),
      "provider" text,
      "model" text,
      "InputTokens" integer,
      "outputTokens" integer,
      "inputPrice" number,
      "outputPrice" NUMBER,
      "createdAt" integer,
      PRIMARY KEY ("id")
    )`,
      )
      .run();
  }

  private createTableKnowledgeCollections(): void {
    this.db
      .prepare(
        `CREATE TABLE IF NOT EXISTS "knowledge_collections" (
       "id" text(31) NOT NULL,
       "name" varchar NOT NULL,
       "memo" text,
       "pinedAt" integer,
       "favorite" integer(1),
       "createdAt" integer NOT NULL,
       "updatedAt" integer NOT NULL,
       PRIMARY KEY (id));`,
      )
      .run();
  }

  private createTableKnowledgeFiles(): void {
    this.db
      .prepare(
        `CREATE TABLE IF NOT EXISTS "knowledge_files" (
      "id" text(31) NOT NULL,
      "collectionId" text(31) NOT NULL,
      "name" varchar NOT NULL,
      "size" integer,
      "numOfChunks" integer,
      "createdAt" integer NOT NULL,
      "updatedAt" integer NOT NULL,
      PRIMARY KEY (id),
      FOREIGN KEY (collectionId)
          REFERENCES knowledge_collections(id)
          ON DELETE CASCADE
      );`,
      )
      .run();
  }

  private createTableChatKnowledgeRels(): void {
    this.db
      .prepare(
        `CREATE TABLE IF NOT EXISTS "chat_knowledge_rels" (
  	"id" text NOT NULL,
  	"chatId" text NOT NULL,
  	"collectionId" text NOT NULL,
  	FOREIGN KEY("chatId") REFERENCES "chats"("id") ON DELETE CASCADE,
  	FOREIGN KEY("collectionId") REFERENCES "knowledge_collections"("id") ON DELETE CASCADE,
  	PRIMARY KEY (id)
  )`,
      )
      .run();
  }

  private createTableMcpServers(): void {
    this.db
      .prepare(
        `CREATE TABLE IF NOT EXISTS "mcp_servers" (
      "id" text(31) NOT NULL,
      "name" text NOT NULL,
      "type" text NOT NULL,
      "isActive" integer(1) DEFAULT 0,
      "command" text,
      "description" text,
      "args" text,
      "env" text,
      "baseUrl" text,
      "headers" text,
      "logoUrl" text,
      "provider" text,
      "providerUrl" text,
      "tags" text,
      "createdAt" integer,
      "updatedAt" integer,
      PRIMARY KEY ("id")
    )`,
      )
      .run();
  }

  private createTableMcpServerTools(): void {
    this.db
      .prepare(
        `CREATE TABLE IF NOT EXISTS "mcp_server_tools" (
      "id" text NOT NULL,
      "c_name" text,
      "description" text,
      "descriptionChinese" text,
      "fullName" text NOT NULL,
      "inputSchema" text,
      "name" text,
      "outputSchema" text,
      "points" integer,
      "projectUUId" text,
      "regex" text,
      "logoUrl" text,
      "is_single_call" integer,
      "supportedExtensions" text,
      "multiFileType" integer,
      "keywords" text,
      "platform" text,
      "createdAt" integer,
      "updatedAt" integer,
      PRIMARY KEY ("id"),
      FOREIGN KEY ("projectUUId") REFERENCES "mcp_servers" ("id") ON DELETE CASCADE
    )`,
      )
      .run();
  }

  private createTableClipboardHistory(): void {
    this.db
      .prepare(
        `CREATE TABLE IF NOT EXISTS "clipboard_history" (
          "id" text(31) NOT NULL,
          "type" text NOT NULL,
          "items" text NOT NULL,
          "timestamp" integer NOT NULL,
          "count" integer DEFAULT 1,
          "source" text,
          "contentType" text,
          "tags" text,
          "favorite" integer(1) DEFAULT 0,
          "memo" text,
          "searchableContent" text,
          "fileCount" integer DEFAULT 0,
          "totalSize" integer DEFAULT 0,
          "contentHash" text,
          "appName" text,
          "appBundleId" text,
          "windowTitle" text,
          "deletedAt" integer,
          "usageCount" integer DEFAULT 0,
          "lastUsedAt" integer,
          "createdAt" integer NOT NULL,
          "updatedAt" integer NOT NULL,
          PRIMARY KEY ("id")
        )`
      )
      .run();
    
    // 创建索引以提高查询性能
    this.db.prepare(`CREATE INDEX IF NOT EXISTS "idx_clipboard_timestamp" ON "clipboard_history" ("timestamp" DESC)`).run();
    this.db.prepare(`CREATE INDEX IF NOT EXISTS "idx_clipboard_type" ON "clipboard_history" ("type")`).run();
    this.db.prepare(`CREATE INDEX IF NOT EXISTS "idx_clipboard_content_hash" ON "clipboard_history" ("contentHash")`).run();
    this.db.prepare(`CREATE INDEX IF NOT EXISTS "idx_clipboard_deleted_at" ON "clipboard_history" ("deletedAt")`).run();
    this.db.prepare(`CREATE INDEX IF NOT EXISTS "idx_clipboard_favorite" ON "clipboard_history" ("favorite")`).run();
  }

  private alertTableFolders(): void {
    const columns = this.db.prepare(`PRAGMA table_info(folders)`).all();
    const hasProviderColumn = columns.some(
      (column: any) => column.name === 'provider',
    );
    if (!hasProviderColumn) {
      this.db.prepare(`ALTER TABLE folders ADD COLUMN provider TEXT`).run();
      logging.debug('Added [provider] column to [folders] table');
    } else {
      logging.debug('[provider] column already exists in [folders] table');
    }
  }

  private alertTableMcpServers(): void {
    const columns = this.db.prepare(`PRAGMA table_info(mcp_servers)`).all();
    const hasIsAidoColumn = columns.some(
      (column: any) => column.name === 'isAido',
    );
    if (!hasIsAidoColumn) {
      this.db.prepare(`ALTER TABLE mcp_servers ADD COLUMN isAido INTEGER DEFAULT 0`).run();
      logging.debug('Added [isAido] column to [mcp_servers] table');
    } else {
      logging.debug('[isAido] column already exists in [mcp_servers] table');
    }
  }

  // 提供获取数据库实例的方法，如果其他模块需要直接操作数据库
  public getDbInstance(): Database.Database {
    return this.db;
  }

  // MCP服务器相关方法
  public saveMcpServer(id: string, server: any, isAido: boolean): void {
    const now = Date.now();
    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO mcp_servers (
        id, name, type, isActive, command, description, args, env, 
        baseUrl, headers, logoUrl, provider, providerUrl, tags, 
        createdAt, updatedAt, isAido
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    stmt.run(
      id,
      server.name,
      server.type,
      server.isActive ? 1 : 0,
      server.command || null,
      server.description || null,
      server.args ? JSON.stringify(server.args) : null,
      server.env ? JSON.stringify(server.env) : null,
      server.baseUrl || null,
      server.headers ? JSON.stringify(server.headers) : null,
      server.logoUrl || null,
      server.provider || null,
      server.providerUrl || null,
      server.tags ? JSON.stringify(server.tags) : null,
      now,
      now,
      isAido ? 1 : 0,
    );
    // 获取并保存服务器技能列表
    console.log(`🔧 开始获取MCP服务器技能列表: ${id}`);
    getAllProjectToolsList(id).then((res) => {
      if (res && res.data && Array.isArray(res.data)) {
        this.saveMcpServerTools(id, res.data, server.logoUrl);
        console.log(`✅ 技能保存完成: ${id}`);
      } else {
        console.warn(`⚠️ API响应格式不正确:`, res);
      }
    }).catch((error) => {
      console.error('❌ 获取技能列表失败:', error);
    });
  }

  public getMcpServer(id: string): any | null {
    const stmt = this.db.prepare(`
      SELECT * FROM mcp_servers WHERE id = ?
    `);

    const row: any = stmt.get(id);
    if (!row) return null;

    return {
      name: row.name,
      type: row.type,
      isActive: row.isActive === 1,
      command: row.command,
      description: row.description,
      args: row.args ? JSON.parse(row.args) : undefined,
      env: row.env ? JSON.parse(row.env) : undefined,
      baseUrl: row.baseUrl,
      headers: row.headers ? JSON.parse(row.headers) : undefined,
      logoUrl: row.logoUrl,
      provider: row.provider,
      providerUrl: row.providerUrl,
      tags: row.tags ? JSON.parse(row.tags) : undefined,
    };
  }

  public getAllMcpServers(): Record<string, any> {
    const stmt = this.db.prepare(`
      SELECT * FROM mcp_servers ORDER BY createdAt
    `);

    const rows: any[] = stmt.all();
    const servers: Record<string, any> = {};

    rows.forEach((row) => {
      servers[row.id] = {
        name: row.name,
        type: row.type,
        isActive: row.isActive === 1,
        command: row.command,
        description: row.description,
        args: row.args ? JSON.parse(row.args) : undefined,
        env: row.env ? JSON.parse(row.env) : undefined,
        baseUrl: row.baseUrl,
        headers: row.headers ? JSON.parse(row.headers) : undefined,
        logoUrl: row.logoUrl,
        provider: row.provider,
        providerUrl: row.providerUrl,
        tags: row.tags ? JSON.parse(row.tags) : undefined,
        isAido: row.isAido === 1,
      };
    });

    return servers;
  }

  public deleteMcpServer(id: string): boolean {
    const stmt = this.db.prepare(`DELETE FROM mcp_servers WHERE id = ?`);
    const result = stmt.run(id);
    return result.changes > 0;
  }

  // MCP服务器技能相关方法
  public saveMcpServerTools(mcpId: string, tools: MCPTool[],logoUrl:string): void {
    const now = Date.now();
    
    // 先删除该服务器下的所有技能
    const deleteStmt = this.db.prepare(`DELETE FROM mcp_server_tools WHERE projectUUId = ?`);
    deleteStmt.run(mcpId);
    
    // 批量插入新的技能数据
    const insertStmt = this.db.prepare(`
      INSERT INTO mcp_server_tools (
        id, c_name, description, descriptionChinese, fullName,
        inputSchema, name, outputSchema, points, projectUUId, regex, logoUrl,
        is_single_call, supportedExtensions, multiFileType, keywords, platform,
        createdAt, updatedAt
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const insertMany = this.db.transaction((tools: any[]) => {
      for (const tool of tools) {
        insertStmt.run(
          tool.fullName, // 使用 fullName 作为 id
          tool.c_name || null,
          tool.description || null,
          tool.descriptionChinese || null,
          tool.fullName,
          tool.inputSchema ? JSON.stringify(tool.inputSchema) : null,
          tool.name || null,
          tool.outputSchema ? JSON.stringify(tool.outputSchema) : null,
          tool.points || null,
          tool.projectUUId || null,
          tool.regex || null,
          logoUrl || null,
          tool.is_single_call,
          tool.supportedExtensions ? JSON.stringify(tool.supportedExtensions) : null,
          tool.multiFileType || null,
          tool.keywords || null,
          tool.platform || null,
          now,
          now
        );
      }
    });

    insertMany(tools);
  }

  public getAllMcpServerTools(): MCPTool[] {
    const stmt = this.db.prepare(`
      SELECT * FROM mcp_server_tools WHERE is_single_call = 1 ORDER BY name
    `);

    const rows: any[] = stmt.all();
    return rows.map((row) => ({
      id: row.id,
      c_name: row.c_name,
      description: row.description,
      descriptionChinese: row.descriptionChinese,
      fullName: row.fullName,
      inputSchema: row.inputSchema ? JSON.parse(row.inputSchema) : undefined,
      name: row.name,
      outputSchema: row.outputSchema ? JSON.parse(row.outputSchema) : undefined,
      points: row.points,
      projectUUId: row.projectUUId,
      regex: row.regex,
      logoUrl: row.logoUrl,
      is_single_call: row.is_single_call,
      supportedExtensions: row.supportedExtensions ? JSON.parse(row.supportedExtensions) : undefined,
      multiFileType: row.multiFileType,
      keywords: row.keywords,
      platform: row.platform
    }));
  }

  public getMcpServerToolById(id: string): MCPTool[] {
    const stmt = this.db.prepare(`
      SELECT * FROM mcp_server_tools WHERE id = ?
    `);

    const rows: any[] = stmt.all(id);
    return rows.map((row) => ({
      id: row.id,
      c_name: row.c_name,
      description: row.description,
      descriptionChinese: row.descriptionChinese,
      fullName: row.fullName,
      inputSchema: row.inputSchema ? JSON.parse(row.inputSchema) : undefined,
      name: row.name,
      outputSchema: row.outputSchema ? JSON.parse(row.outputSchema) : undefined,
      points: row.points,
      projectUUId: row.projectUUId,
      regex: row.regex,
      logoUrl: row.logoUrl,
      is_single_call: row.is_single_call,
      supportedExtensions: row.supportedExtensions ? JSON.parse(row.supportedExtensions) : undefined,
      multiFileType: row.multiFileType,
      keywords: row.keywords,
      platform: row.platform
    }));
  }

  // 剪贴板历史相关方法

  /**
   * 保存剪贴板历史记录
   */
  public saveClipboardItem(item: ClipboardItem): void {
    const now = Date.now();
    
    // 生成可搜索内容
    const searchableContent = this.generateSearchableContent(item);
    
    // 计算内容哈希
    const contentHash = this.generateContentHash(item);
    
    // 计算文件数量和总大小
    const { fileCount, totalSize } = this.calculateItemStats(item);
    
    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO clipboard_history (
        id, type, items, timestamp, count, source, contentType,
        searchableContent, fileCount, totalSize, contentHash,
        appName, usageCount, lastUsedAt, createdAt, updatedAt
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    stmt.run(
      item.id,
      item.type,
      JSON.stringify(item.items),
      item.timestamp,
      item.count || 1,
      item.source || null,
      item.contentType || null,
      searchableContent,
      fileCount,
      totalSize,
      contentHash,
      item.source || null, // appName暂时使用source
      0, // usageCount初始为0
      null, // lastUsedAt初始为null
      now,
      now
    );
  }

  /**
   * 获取剪贴板历史记录（分页）
   */
  public getClipboardHistory(options: {
    page?: number;
    limit?: number;
    type?: string;
    favorite?: boolean;
    search?: string;
    includeDeleted?: boolean;
  } = {}): {
    items: ClipboardItem[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  } {
    const {
      page = 1,
      limit = 50,
      type,
      favorite,
      search,
      includeDeleted = false
    } = options;

    const whereConditions: string[] = [];
    const params: any[] = [];

    // 软删除条件
    if (!includeDeleted) {
      whereConditions.push('deletedAt IS NULL');
    }

    // 类型筛选
    if (type) {
      whereConditions.push('type = ?');
      params.push(type);
    }

    // 收藏筛选
    if (favorite !== undefined) {
      whereConditions.push('favorite = ?');
      params.push(favorite ? 1 : 0);
    }

    // 搜索条件
    if (search) {
      whereConditions.push('searchableContent LIKE ?');
      params.push(`%${search}%`);
    }

    const whereClause = whereConditions.length > 0 
      ? `WHERE ${whereConditions.join(' AND ')}`
      : '';

    // 获取总数
    const countStmt = this.db.prepare(`
      SELECT COUNT(*) as total FROM clipboard_history ${whereClause}
    `);
    const { total } = countStmt.get(...params) as { total: number };

    // 获取分页数据
    const offset = (page - 1) * limit;
    const dataStmt = this.db.prepare(`
      SELECT * FROM clipboard_history 
      ${whereClause}
      ORDER BY timestamp DESC
      LIMIT ? OFFSET ?
    `);
    
    const rows: any[] = dataStmt.all(...params, limit, offset);
    
    const items: ClipboardItem[] = rows.map(row => ({
      id: row.id,
      type: row.type,
      items: JSON.parse(row.items),
      timestamp: row.timestamp,
      count: row.count,
      source: row.source,
      contentType: row.contentType,
      // 添加扩展字段
      tags: row.tags ? JSON.parse(row.tags) : undefined,
      favorite: row.favorite === 1,
      memo: row.memo,
      usageCount: row.usageCount,
      lastUsedAt: row.lastUsedAt,
    }));

    const totalPages = Math.ceil(total / limit);

    return {
      items,
      total,
      page,
      limit,
      totalPages
    };
  }

  /**
   * 获取单个剪贴板记录
   */
  public getClipboardItem(id: string): ClipboardItem | null {
    const stmt = this.db.prepare(`
      SELECT * FROM clipboard_history WHERE id = ? AND deletedAt IS NULL
    `);
    
    const row: any = stmt.get(id);
    if (!row) return null;

    return {
      id: row.id,
      type: row.type,
      items: JSON.parse(row.items),
      timestamp: row.timestamp,
      count: row.count,
      source: row.source,
      contentType: row.contentType,
    };
  }

  /**
   * 更新剪贴板记录的使用次数和最后使用时间
   */
  public updateClipboardUsage(id: string): boolean {
    const now = Date.now();
    const stmt = this.db.prepare(`
      UPDATE clipboard_history 
      SET usageCount = usageCount + 1, lastUsedAt = ?, updatedAt = ?
      WHERE id = ? AND deletedAt IS NULL
    `);
    
    const result = stmt.run(now, now, id);
    return result.changes > 0;
  }

  /**
   * 软删除剪贴板记录
   */
  public deleteClipboardItem(id: string): boolean {
    const now = Date.now();
    const stmt = this.db.prepare(`
      UPDATE clipboard_history 
      SET deletedAt = ?, updatedAt = ?
      WHERE id = ? AND deletedAt IS NULL
    `);
    
    const result = stmt.run(now, now, id);
    return result.changes > 0;
  }

  /**
   * 批量删除剪贴板记录
   */
  public deleteClipboardItems(ids: string[]): number {
    if (ids.length === 0) return 0;
    
    const now = Date.now();
    const placeholders = ids.map(() => '?').join(',');
    const stmt = this.db.prepare(`
      UPDATE clipboard_history 
      SET deletedAt = ?, updatedAt = ?
      WHERE id IN (${placeholders}) AND deletedAt IS NULL
    `);
    
    const result = stmt.run(now, now, ...ids);
    return result.changes;
  }

  /**
   * 清理所有剪贴板历史记录（软删除）
   */
  public clearClipboardHistory(): number {
    const now = Date.now();
    const stmt = this.db.prepare(`
      UPDATE clipboard_history 
      SET deletedAt = ?, updatedAt = ?
      WHERE deletedAt IS NULL
    `);
    
    const result = stmt.run(now, now);
    return result.changes;
  }

  /**
   * 永久删除已软删除的记录（清理）
   */
  public purgeDeletedClipboardItems(olderThanDays: number = 30): number {
    const cutoffTime = Date.now() - (olderThanDays * 24 * 60 * 60 * 1000);
    const stmt = this.db.prepare(`
      DELETE FROM clipboard_history 
      WHERE deletedAt IS NOT NULL AND deletedAt < ?
    `);
    
    const result = stmt.run(cutoffTime);
    return result.changes;
  }

  /**
   * 设置收藏状态
   */
  public setClipboardFavorite(id: string, favorite: boolean): boolean {
    const now = Date.now();
    const stmt = this.db.prepare(`
      UPDATE clipboard_history 
      SET favorite = ?, updatedAt = ?
      WHERE id = ? AND deletedAt IS NULL
    `);
    
    const result = stmt.run(favorite ? 1 : 0, now, id);
    return result.changes > 0;
  }

  /**
   * 更新剪贴板记录的备注
   */
  public updateClipboardMemo(id: string, memo: string): boolean {
    const now = Date.now();
    const stmt = this.db.prepare(`
      UPDATE clipboard_history 
      SET memo = ?, updatedAt = ?
      WHERE id = ? AND deletedAt IS NULL
    `);
    
    const result = stmt.run(memo, now, id);
    return result.changes > 0;
  }

  /**
   * 更新剪贴板记录的标签
   */
  public updateClipboardTags(id: string, tags: string[]): boolean {
    const now = Date.now();
    const stmt = this.db.prepare(`
      UPDATE clipboard_history 
      SET tags = ?, updatedAt = ?
      WHERE id = ? AND deletedAt IS NULL
    `);
    
    const result = stmt.run(JSON.stringify(tags), now, id);
    return result.changes > 0;
  }

  /**
   * 根据内容哈希查找重复项
   */
  public findDuplicateClipboardItem(contentHash: string): ClipboardItem | null {
    const stmt = this.db.prepare(`
      SELECT * FROM clipboard_history 
      WHERE contentHash = ? AND deletedAt IS NULL
      ORDER BY timestamp DESC
      LIMIT 1
    `);
    
    const row: any = stmt.get(contentHash);
    if (!row) return null;

    return {
      id: row.id,
      type: row.type,
      items: JSON.parse(row.items),
      timestamp: row.timestamp,
      count: row.count,
      source: row.source,
      contentType: row.contentType,
    };
  }

  /**
   * 生成可搜索的内容（用于全文搜索）
   */
  private generateSearchableContent(item: ClipboardItem): string {
    const searchableTexts: string[] = [];
    
    for (const subItem of item.items) {
      if ('content' in subItem && subItem.content) {
        searchableTexts.push(subItem.content);
      }
      if ('name' in subItem && subItem.name) {
        searchableTexts.push(subItem.name);
      }
    }
    
    // 添加类型和来源信息
    searchableTexts.push(item.type);
    if (item.source) {
      searchableTexts.push(item.source);
    }
    
    return searchableTexts.join(' ').toLowerCase();
  }

  /**
   * 生成内容哈希（用于去重）
   */
  public generateContentHash(item: ClipboardItem): string {
    const crypto = require('crypto');
    const contentString = JSON.stringify({
      type: item.type,
      items: item.items.map(subItem => {
        if ('content' in subItem) {
          return subItem.content;
        }
        if ('dataURL' in subItem) {
          return subItem.dataURL;
        }
        return JSON.stringify(subItem);
      })
    });
    
    return crypto.createHash('md5').update(contentString).digest('hex');
  }

  /**
   * 计算项目统计信息
   */
  private calculateItemStats(item: ClipboardItem): { fileCount: number; totalSize: number } {
    let fileCount = 0;
    let totalSize = 0;
    
    for (const subItem of item.items) {
      if ('size' in subItem && typeof subItem.size === 'number') {
        totalSize += subItem.size;
      }
      
      if (item.type === 'file' || item.type === 'directory' || item.type === 'multiple') {
        fileCount++;
      }
    }
    
    return { fileCount, totalSize };
  }
}

// 导出 DbService 的单例实例
export const dbService = new DbService();

// 如果你需要直接访问数据库实例，可以使用 dbService.getDbInstance()
// 例如: const database = dbService.getDbInstance();
// 但通常建议将数据库操作封装在 DbService 类的方法中