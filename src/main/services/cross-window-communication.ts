import { BrowserWindow, ipcMain } from 'electron';
import { EventEmitter } from 'events';
import { v4 as uuidv4 } from 'uuid';
import { WindowManager } from '../windows/window-manager';
import { CROSS_WINDOW, WindowType, CrossWindowEventData } from '../../shared/ipc';
import * as log from '../logging';

/**
 * 跨窗口通信服务
 * 负责管理所有窗口之间的事件通信，同时扩展主进程内部的事件总线功能
 *
 * 功能包括：
 * 1. 主进程内部的发布订阅（扩展原有 mainAppBus）
 * 2. 主进程直接向窗口发送事件
 * 3. 渲染进程之间的跨窗口通信
 * 4. 统一的事件管理系统
 */
export class CrossWindowCommunicationService extends EventEmitter {
  private static instance: CrossWindowCommunicationService | null = null;

  // 全局窗口映射，所有实例共享
  private static globalWindowMap = new Map<WindowType, BrowserWindow>();

  // 窗口类型到 BrowserWindow 的映射（指向全局映射）
  private get windowMap(): Map<WindowType, BrowserWindow> {
    return CrossWindowCommunicationService.globalWindowMap;
  }
  
  // 事件监听器管理
  private eventListeners: Map<string, Set<(...args: any[]) => void>> = new Map();
  
  // 窗口事件监听器管理 (窗口类型 -> 事件名 -> 监听器集合)
  private windowEventListeners: Map<WindowType, Map<string, Set<(...args: any[]) => void>>> = new Map();

  // 主进程内部事件总线（替代原有的 mainAppBus）
  private mainEventBus: EventEmitter = new EventEmitter();

  private constructor() {
    super();
    this.setupIpcHandlers();
    // 不在构造函数中立即更新窗口映射，避免循环依赖
    log.info('跨窗口通信服务已初始化');
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): CrossWindowCommunicationService {
    // 使用全局变量来确保跨模块重载的单例
    const globalKey = '__AIDO_CROSS_WINDOW_SERVICE__';
    if (!(global as any)[globalKey]) {
      log.debug('创建跨窗口通信服务实例（全局单例）');
      (global as any)[globalKey] = new CrossWindowCommunicationService();
    } else {
      log.debug('返回现有跨窗口通信服务实例（全局单例）');
    }

    this.instance = (global as any)[globalKey];
    return this.instance;
  }

  /**
   * 设置 IPC 处理器
   */
  private setupIpcHandlers(): void {
    // 防止重复注册 IPC 处理器（开发模式热重载问题）
    try {
      // 先尝试移除已存在的处理器
      ipcMain.removeHandler(CROSS_WINDOW.EMIT);
      ipcMain.removeHandler(CROSS_WINDOW.BROADCAST);
    } catch (error) {
      // 如果处理器不存在，忽略错误
    }
    // 处理来自渲染进程的事件发送请求
    ipcMain.handle(CROSS_WINDOW.EMIT, async (event, eventName: string, data: any, targetWindow?: WindowType | WindowType[]) => {
      const sourceWindow = this.getWindowTypeByWebContents(event.sender);
      if (!sourceWindow) {
        log.warn('无法确定发送事件的窗口类型');
        return false;
      }

      // 直接调用内部方法，避免重复发布
      return this.emitEvent(eventName, data, sourceWindow, targetWindow, true);
    });

    // 处理广播请求
    ipcMain.handle(CROSS_WINDOW.BROADCAST, async (event, eventName: string, data: any) => {
      const sourceWindow = this.getWindowTypeByWebContents(event.sender);
      if (!sourceWindow) {
        log.warn('无法确定广播事件的窗口类型');
        return false;
      }

      // 直接调用内部方法，避免重复发布
      return this.broadcastEvent(eventName, data, sourceWindow, true);
    });

    // 处理事件监听器注册
    ipcMain.on(CROSS_WINDOW.ON, (event, eventName: string) => {
      const sourceWindow = this.getWindowTypeByWebContents(event.sender);
      if (!sourceWindow) {
        log.warn('无法确定注册监听器的窗口类型');
        return;
      }

      this.registerWindowEventListener(sourceWindow, eventName);
    });

    // 处理事件监听器移除
    ipcMain.on(CROSS_WINDOW.OFF, (event, eventName: string) => {
      const sourceWindow = this.getWindowTypeByWebContents(event.sender);
      if (!sourceWindow) {
        log.warn('无法确定移除监听器的窗口类型');
        return;
      }

      this.unregisterWindowEventListener(sourceWindow, eventName);
    });

    log.info('跨窗口通信 IPC 处理程序注册成功');
  }

  /**
   * 更新窗口映射
   */
  public updateWindowMap(): void {
    try {
      this.windowMap.clear();

      // 检查 WindowManager 是否可用
      if (typeof WindowManager === 'undefined') {
        log.warn('WindowManager 尚未初始化，跳过窗口映射更新');
        return;
      }

      // 获取各种窗口实例
      const mainWindow = WindowManager.getMainWindow();
      const settingsWindow = WindowManager.getSettingsWindow();
      const clipboardHistoryWindow = WindowManager.getClipboardHistoryWindow();
      const floatingBall = WindowManager.getFloatingBall();
      const petWindow = WindowManager.getPetWindow();

    // 映射窗口类型到 BrowserWindow
    if (mainWindow && !mainWindow.isDestroyed()) {
      this.windowMap.set(WindowType.MAIN, mainWindow.getWindow());
    }

    if (settingsWindow && !settingsWindow.isDestroyed()) {
      this.windowMap.set(WindowType.SETTINGS, settingsWindow.getWindow());
    }

    if (clipboardHistoryWindow && !clipboardHistoryWindow.isDestroyed()) {
      this.windowMap.set(WindowType.CLIPBOARD_HISTORY, clipboardHistoryWindow.getWindow());
    }

    if (floatingBall && !floatingBall.isDestroyed()) {
      this.windowMap.set(WindowType.FLOATING_BALL, floatingBall.getWindow());
    }

    if (petWindow && !petWindow.isDestroyed()) {
      this.windowMap.set(WindowType.PET, petWindow.getWindow());
    }

    log.debug(`窗口映射已更新，当前活跃窗口: ${Array.from(this.windowMap.keys()).join(', ')}`);
    } catch (error) {
      log.error('更新窗口映射失败:', error);
    }
  }

  /**
   * 根据 WebContents 获取窗口类型
   */
  private getWindowTypeByWebContents(webContents: Electron.WebContents): WindowType | null {
    for (const [windowType, browserWindow] of this.windowMap.entries()) {
      if (browserWindow.webContents === webContents) {
        return windowType;
      }
    }

    // 如果在映射中找不到，尝试通过其他方式判断
    const url = webContents.getURL();
    const title = webContents.getTitle();

    // 根据 URL 或标题判断窗口类型
    if (url.includes('settings') || title.includes('Settings') || title.includes('设置')) {
      return WindowType.SETTINGS;
    } else if (url.includes('clipboard') || title.includes('Clipboard') || title.includes('剪贴板')) {
      return WindowType.CLIPBOARD_HISTORY;
    } else if (url.includes('floating-ball') || title.includes('FloatingBall')) {
      return WindowType.FLOATING_BALL;
    } else if (url.includes('screen-capture') || title.includes('ScreenCapture')) {
      return WindowType.SCREEN_CAPTURE;
    } else if (url.includes('pet') || title.includes('Pet')) {
      return WindowType.PET;
    }

    // 默认返回主窗口类型
    return WindowType.MAIN;
  }

  /**
   * 发送事件到指定窗口或广播
   */
  public async emitEvent<T = any>(
    eventName: string,
    data: T,
    sourceWindow: WindowType,
    targetWindow?: WindowType | WindowType[],
    triggerMainEvent: boolean = false
  ): Promise<boolean> {
    try {
      // 检查当前进程是否有有效的 WindowManager
      if (typeof WindowManager === 'undefined' || !WindowManager.getMainWindow()) {
        log.warn(`当前进程没有有效的 WindowManager，无法处理事件: ${eventName}。这可能是由于热重载导致的多进程问题。`);
        return false;
      }

      const eventData: CrossWindowEventData<T> = {
        eventName,
        data,
        sourceWindow,
        targetWindow,
        timestamp: Date.now(),
        id: uuidv4(),
      };

      log.debug(`发送跨窗口事件: ${eventName}`, {
        sourceWindow,
        targetWindow,
        eventId: eventData.id,
      });

      // 如果需要触发主进程事件（来自渲染进程的事件）
      if (triggerMainEvent) {
        this.mainEventBus.emit(`cross-window:${eventName}`, eventData);
      }

      // 如果没有指定目标窗口，广播到所有窗口（除了发送者）
      if (!targetWindow) {
        return this.broadcastEventData(eventData);
      }

      // 发送到指定窗口
      const targets = Array.isArray(targetWindow) ? targetWindow : [targetWindow];
      let success = true;

      // 如果窗口映射为空，尝试重新更新
      if (this.windowMap.size === 0) {
        log.debug('窗口映射为空，尝试重新更新窗口映射');
        this.updateWindowMap();
      }

      for (const target of targets) {
        let browserWindow = this.windowMap.get(target);

        // 如果在映射中找不到窗口，尝试直接从 WindowManager 获取
        if (!browserWindow && typeof WindowManager !== 'undefined') {
          log.debug(`在映射中找不到窗口 ${target}，尝试直接从 WindowManager 获取`);
          try {
            switch (target) {
              case WindowType.MAIN: {
                const mainWindow = WindowManager.getMainWindow();
                log.debug(`WindowManager.getMainWindow() 返回: ${!!mainWindow}, isDestroyed: ${mainWindow ? mainWindow.isDestroyed() : 'N/A'}`);
                if (mainWindow && !mainWindow.isDestroyed()) {
                  browserWindow = mainWindow.getWindow();
                  log.debug(`mainWindow.getWindow() 返回: ${!!browserWindow}`);
                }
                break;
              }
              case WindowType.SETTINGS: {
                const settingsWindow = WindowManager.getSettingsWindow();
                if (settingsWindow && !settingsWindow.isDestroyed()) {
                  browserWindow = settingsWindow.getWindow();
                }
                break;
              }
              case WindowType.CLIPBOARD_HISTORY: {
                const clipboardWindow = WindowManager.getClipboardHistoryWindow();
                if (clipboardWindow && !clipboardWindow.isDestroyed()) {
                  browserWindow = clipboardWindow.getWindow();
                }
                break;
              }
              case WindowType.FLOATING_BALL: {
                const floatingBall = WindowManager.getFloatingBall();
                if (floatingBall && !floatingBall.isDestroyed()) {
                  browserWindow = floatingBall.getWindow();
                }
                break;
              }
              case WindowType.PET: {
                const petWindow = WindowManager.getPetWindow();
                if (petWindow && !petWindow.isDestroyed()) {
                  browserWindow = petWindow.getWindow();
                }
                break;
              }
            }
          } catch (error) {
            log.error(`直接获取窗口 ${target} 失败:`, error);
          }
        }

        log.debug(`查找目标窗口 ${target}: 在映射中=${this.windowMap.has(target)}, 直接获取=${!!browserWindow}, 当前映射的窗口=${Array.from(this.windowMap.keys()).join(', ')}`);

        if (browserWindow && !browserWindow.isDestroyed()) {
          browserWindow.webContents.send(CROSS_WINDOW.FORWARD, eventData);
          log.debug(`事件已发送到窗口: ${target}`);
        } else {
          log.warn(`目标窗口不存在或已销毁: ${target}, browserWindow=${!!browserWindow}, isDestroyed=${browserWindow ? browserWindow.isDestroyed() : 'N/A'}`);
          success = false;
        }
      }

      return success;
    } catch (error) {
      log.error('发送跨窗口事件失败:', error);
      return false;
    }
  }

  /**
   * 广播事件到所有窗口（除了发送者）
   */
  public async broadcastEvent<T = any>(
    eventName: string,
    data: T,
    sourceWindow: WindowType,
    triggerMainEvent: boolean = false
  ): Promise<boolean> {
    const eventData: CrossWindowEventData<T> = {
      eventName,
      data,
      sourceWindow,
      timestamp: Date.now(),
      id: uuidv4(),
    };

    // 如果需要触发主进程事件（来自渲染进程的事件）
    if (triggerMainEvent) {
      this.mainEventBus.emit(`cross-window:${eventName}`, eventData);
    }

    return this.broadcastEventData(eventData);
  }

  /**
   * 广播事件数据
   */
  private broadcastEventData<T = any>(eventData: CrossWindowEventData<T>): boolean {
    try {
      let success = true;

      for (const [windowType, browserWindow] of this.windowMap.entries()) {
        // 不发送给发送者自己
        if (windowType === eventData.sourceWindow) {
          continue;
        }

        if (browserWindow && !browserWindow.isDestroyed()) {
          browserWindow.webContents.send(CROSS_WINDOW.FORWARD, eventData);
          log.debug(`事件已广播到窗口: ${windowType}`);
        } else {
          log.warn(`广播目标窗口不存在或已销毁: ${windowType}`);
          success = false;
        }
      }

      return success;
    } catch (error) {
      log.error('广播跨窗口事件失败:', error);
      return false;
    }
  }

  /**
   * 注册窗口事件监听器
   */
  private registerWindowEventListener(windowType: WindowType, eventName: string): void {
    if (!this.windowEventListeners.has(windowType)) {
      this.windowEventListeners.set(windowType, new Map());
    }

    const windowListeners = this.windowEventListeners.get(windowType)!;
    if (!windowListeners.has(eventName)) {
      windowListeners.set(eventName, new Set());
    }
  }

  /**
   * 移除窗口事件监听器
   */
  private unregisterWindowEventListener(windowType: WindowType, eventName: string): void {
    const windowListeners = this.windowEventListeners.get(windowType);
    if (windowListeners) {
      windowListeners.delete(eventName);
    }
  }

  /**
   * 获取所有活跃窗口类型
   */
  public getActiveWindows(): WindowType[] {
    return Array.from(this.windowMap.keys());
  }

  /**
   * 清理资源
   */
  public cleanup(): void {
    this.windowMap.clear();
    this.eventListeners.clear();
    this.windowEventListeners.clear();
    this.removeAllListeners();

    // 清理主进程内部事件总线
    this.mainEventBus.removeAllListeners();

    log.info('跨窗口通信服务已清理');
  }

  /**
   * 销毁服务实例
   */
  public static destroy(): void {
    if (this.instance) {
      this.instance.cleanup();
      this.instance = null;
    }
  }

  // ================= 主进程专用方法 =================

  /**
   * 主进程发送事件到指定窗口或广播
   * 这是主进程专用的方法，不需要通过 IPC
   */
  public emitFromMain<T = any>(
    eventName: string,
    data: T,
    targetWindow?: WindowType | WindowType[]
  ): boolean {
    try {
      const eventData: CrossWindowEventData<T> = {
        eventName,
        data,
        sourceWindow: WindowType.MAIN, // 主进程固定为 MAIN
        targetWindow,
        timestamp: Date.now(),
        id: uuidv4(),
      };

      log.debug(`主进程发送跨窗口事件: ${eventName}`, {
        targetWindow,
        eventId: eventData.id,
      });

      // 同时触发主进程事件总线
      this.mainEventBus.emit(`cross-window:${eventName}`, eventData);

      // 如果没有指定目标窗口，广播到所有窗口
      if (!targetWindow) {
        return this.broadcastEventData(eventData);
      }

      // 发送到指定窗口
      const targets = Array.isArray(targetWindow) ? targetWindow : [targetWindow];
      let success = true;

      for (const target of targets) {
        const browserWindow = this.windowMap.get(target);
        if (browserWindow && !browserWindow.isDestroyed()) {
          browserWindow.webContents.send(CROSS_WINDOW.FORWARD, eventData);
          log.debug(`主进程事件已发送到窗口: ${target}`);
        } else {
          log.warn(`主进程发送失败，目标窗口不存在或已销毁: ${target}`);
          success = false;
        }
      }

      return success;
    } catch (error) {
      log.error('主进程发送跨窗口事件失败:', error);
      return false;
    }
  }

  /**
   * 主进程广播事件到所有窗口
   */
  public broadcastFromMain<T = any>(eventName: string, data: T): boolean {
    return this.emitFromMain(eventName, data);
  }

  /**
   * 主进程监听跨窗口事件
   * 可以监听来自渲染进程或主进程的事件
   */
  public onInMain<T = any>(
    eventName: string,
    listener: (data: T, sourceWindow: WindowType, eventId: string) => void
  ): () => void {
    const wrappedListener = (eventData: CrossWindowEventData<T>) => {
      listener(eventData.data, eventData.sourceWindow, eventData.id);
    };

    // 监听主进程事件总线
    this.mainEventBus.on(`cross-window:${eventName}`, wrappedListener);

    // 返回取消监听的函数
    return () => {
      this.mainEventBus.off(`cross-window:${eventName}`, wrappedListener);
    };
  }

  /**
   * 主进程移除事件监听器
   */
  public offInMain(eventName: string, listener?: (...args: any[]) => void): void {
    if (listener) {
      this.mainEventBus.off(`cross-window:${eventName}`, listener);
    } else {
      this.mainEventBus.removeAllListeners(`cross-window:${eventName}`);
    }
  }

  // ================= 主进程内部事件总线方法（扩展原有 mainAppBus） =================

  /**
   * 主进程内部发送事件（不跨窗口，只在主进程内部）
   * 这个方法直接使用原有的 mainAppBus，保持向后兼容性
   */
  public emitInMain(eventName: string, ...args: any[]): boolean {
    try {
      return this.mainEventBus.emit(eventName, ...args);
    } catch (error) {
      log.error('主进程内部事件发送失败:', error);
      return false;
    }
  }

  /**
   * 主进程内部监听事件（不跨窗口，只在主进程内部）
   * 这个方法直接使用原有的 mainAppBus，保持向后兼容性
   */
  public onInMainOnly(eventName: string, listener: (...args: any[]) => void): () => void {
    try {
      this.mainEventBus.on(eventName, listener);
      return () => {
        this.mainEventBus.off(eventName, listener);
      };
    } catch (error) {
      log.error('主进程内部事件监听器注册失败:', error);
      return () => {}; // 返回空函数避免错误
    }
  }

  /**
   * 主进程内部一次性监听事件
   */
  public onceInMainOnly(eventName: string, listener: (...args: any[]) => void): () => void {
    try {
      this.mainEventBus.once(eventName, listener);
      return () => {
        this.mainEventBus.off(eventName, listener);
      };
    } catch (error) {
      log.error('主进程内部一次性事件监听器注册失败:', error);
      return () => {};
    }
  }

  /**
   * 主进程内部移除事件监听器
   */
  public offInMainOnly(eventName: string, listener?: (...args: any[]) => void): void {
    try {
      if (listener) {
        this.mainEventBus.off(eventName, listener);
      } else {
        this.mainEventBus.removeAllListeners(eventName);
      }
    } catch (error) {
      log.error('主进程内部事件监听器移除失败:', error);
    }
  }

  /**
   * 获取主进程内部事件的监听器数量
   */
  public getMainEventListenerCount(eventName: string): number {
    try {
      return this.mainEventBus.listenerCount(eventName);
    } catch (error) {
      log.error('获取主进程事件监听器数量失败:', error);
      return 0;
    }
  }

  /**
   * 获取主进程内部所有事件名称
   */
  public getMainEventNames(): (string | symbol)[] {
    try {
      return this.mainEventBus.eventNames();
    } catch (error) {
      log.error('获取主进程事件名称失败:', error);
      return [];
    }
  }

  /**
   * 设置主进程事件总线的最大监听器数量
   */
  public setMainMaxListeners(n: number): void {
    try {
      this.mainEventBus.setMaxListeners(n);
    } catch (error) {
      log.error('设置主进程最大监听器数量失败:', error);
    }
  }

  // ================= 统一的事件方法（同时支持主进程内部和跨窗口） =================

  /**
   * 统一的事件发送方法
   * 可以选择只在主进程内部发送，或者同时发送到窗口
   *
   * @param eventName 事件名称
   * @param data 事件数据
   * @param options 发送选项
   */
  public emitUnified<T = any>(
    eventName: string,
    data: T,
    options: {
      /** 是否在主进程内部发送 */
      inMain?: boolean;
      /** 是否发送到窗口 */
      toWindows?: boolean;
      /** 目标窗口，不指定则广播到所有窗口 */
      targetWindow?: WindowType | WindowType[];
    } = { inMain: true, toWindows: true }
  ): boolean {
    let success = true;

    try {
      // 在主进程内部发送
      if (options.inMain !== false) {
        const mainSuccess = this.emitInMain(eventName, data);
        if (!mainSuccess) {
          success = false;
        }
      }

      // 发送到窗口
      if (options.toWindows !== false) {
        const windowSuccess = this.emitFromMain(eventName, data, options.targetWindow);
        if (!windowSuccess) {
          success = false;
        }
      }

      return success;
    } catch (error) {
      log.error('统一事件发送失败:', error);
      return false;
    }
  }

  /**
   * 统一的事件监听方法
   * 可以同时监听主进程内部事件和跨窗口事件
   *
   * @param eventName 事件名称
   * @param listener 事件监听器
   * @param options 监听选项
   */
  public onUnified<T = any>(
    eventName: string,
    listener: (data: T, source: 'main' | WindowType, eventId?: string) => void,
    options: {
      /** 是否监听主进程内部事件 */
      fromMain?: boolean;
      /** 是否监听跨窗口事件 */
      fromWindows?: boolean;
    } = { fromMain: true, fromWindows: true }
  ): () => void {
    const unsubscribeFunctions: (() => void)[] = [];

    try {
      // 监听主进程内部事件
      if (options.fromMain !== false) {
        const unsubscribeMain = this.onInMainOnly(eventName, (data: T) => {
          listener(data, 'main');
        });
        unsubscribeFunctions.push(unsubscribeMain);
      }

      // 监听跨窗口事件
      if (options.fromWindows !== false) {
        const unsubscribeWindows = this.onInMain(eventName, (data: T, sourceWindow: WindowType, eventId: string) => {
          listener(data, sourceWindow, eventId);
        });
        unsubscribeFunctions.push(unsubscribeWindows);
      }

      // 返回统一的取消监听函数
      return () => {
        unsubscribeFunctions.forEach(fn => fn());
      };
    } catch (error) {
      log.error('统一事件监听器注册失败:', error);
      return () => {};
    }
  }
}

// 导出获取服务实例的函数，避免循环依赖
export function getCrossWindowService(): CrossWindowCommunicationService {
  return CrossWindowCommunicationService.getInstance();
}

// ================= 主进程跨窗口通信工具类 =================

/**
 * 主进程跨窗口通信工具类
 * 提供简洁的 API 供主进程使用
 *
 * 功能包括：
 * 1. 主进程内部的发布订阅（替代原有 mainAppBus）
 * 2. 主进程直接向窗口发送事件
 * 3. 统一的事件管理
 */
export class MainCrossWindow {
  private static get service() {
    return getCrossWindowService();
  }

  /**
   * 发送事件到指定窗口或广播到所有窗口
   * @param eventName 事件名称
   * @param data 事件数据
   * @param targetWindow 目标窗口，不指定则广播到所有窗口
   */
  public static emit<T = any>(
    eventName: string,
    data: T,
    targetWindow?: WindowType | WindowType[]
  ): boolean {
    try {
      return this.service.emitFromMain(eventName, data, targetWindow);
    } catch (error) {
      log.error('主进程发送跨窗口事件失败:', error);
      return false;
    }
  }

  /**
   * 广播事件到所有窗口
   * @param eventName 事件名称
   * @param data 事件数据
   */
  public static broadcast<T = any>(eventName: string, data: T): boolean {
    try {
      return this.service.broadcastFromMain(eventName, data);
    } catch (error) {
      log.error('主进程广播跨窗口事件失败:', error);
      return false;
    }
  }

  /**
   * 监听跨窗口事件（包括来自渲染进程和主进程的事件）
   * @param eventName 事件名称
   * @param listener 事件监听器
   * @returns 取消监听的函数
   */
  public static on<T = any>(
    eventName: string,
    listener: (data: T, sourceWindow: WindowType, eventId: string) => void
  ): () => void {
    try {
      return this.service.onInMain(eventName, listener);
    } catch (error) {
      log.error('主进程注册跨窗口事件监听器失败:', error);
      return () => {}; // 返回空函数避免错误
    }
  }

  /**
   * 移除事件监听器
   * @param eventName 事件名称
   * @param listener 可选的具体监听器，不提供则移除所有监听器
   */
  public static off(eventName: string, listener?: (...args: any[]) => void): void {
    try {
      this.service.offInMain(eventName, listener);
    } catch (error) {
      log.error('主进程移除跨窗口事件监听器失败:', error);
    }
  }

  /**
   * 一次性监听事件
   * @param eventName 事件名称
   * @param listener 事件监听器
   * @returns 取消监听的函数
   */
  public static once<T = any>(
    eventName: string,
    listener: (data: T, sourceWindow: WindowType, eventId: string) => void
  ): () => void {
    const unsubscribe = this.on(eventName, (data, sourceWindow, eventId) => {
      unsubscribe(); // 自动取消监听
      listener(data, sourceWindow, eventId);
    });
    return unsubscribe;
  }

  /**
   * 发送事件到主窗口
   * @param eventName 事件名称
   * @param data 事件数据
   */
  public static emitToMain<T = any>(eventName: string, data: T): boolean {
    return this.emit(eventName, data, WindowType.MAIN);
  }

  /**
   * 发送事件到设置窗口
   * @param eventName 事件名称
   * @param data 事件数据
   */
  public static emitToSettings<T = any>(eventName: string, data: T): boolean {
    return this.emit(eventName, data, WindowType.SETTINGS);
  }

  /**
   * 发送事件到剪贴板历史窗口
   * @param eventName 事件名称
   * @param data 事件数据
   */
  public static emitToClipboard<T = any>(eventName: string, data: T): boolean {
    return this.emit(eventName, data, WindowType.CLIPBOARD_HISTORY);
  }

  /**
   * 发送事件到悬浮球窗口
   * @param eventName 事件名称
   * @param data 事件数据
   */
  public static emitToFloatingBall<T = any>(eventName: string, data: T): boolean {
    return this.emit(eventName, data, WindowType.FLOATING_BALL);
  }

  /**
   * 获取当前活跃窗口列表
   */
  public static getActiveWindows(): WindowType[] {
    try {
      return this.service.getActiveWindows();
    } catch (error) {
      log.error('主进程获取活跃窗口列表失败:', error);
      return [];
    }
  }

  /**
   * 更新窗口映射
   * 当有新窗口创建或销毁时调用
   */
  public static updateWindowMap(): void {
    try {
      this.service.updateWindowMap();
    } catch (error) {
      log.error('主进程更新窗口映射失败:', error);
    }
  }

  // ================= 主进程内部事件管理（替代原有 mainAppBus） =================

  /**
   * 主进程内部发送事件（不跨窗口）
   * 这个方法可以替代原有的 mainAppBus.emit()
   *
   * @param eventName 事件名称
   * @param args 事件参数
   */
  public static emitInMain(eventName: string, ...args: any[]): boolean {
    try {
      return this.service.emitInMain(eventName, ...args);
    } catch (error) {
      log.error('主进程内部事件发送失败:', error);
      return false;
    }
  }

  /**
   * 主进程内部监听事件（不跨窗口）
   * 这个方法可以替代原有的 mainAppBus.on()
   *
   * @param eventName 事件名称
   * @param listener 事件监听器
   * @returns 取消监听的函数
   */
  public static onInMain(eventName: string, listener: (...args: any[]) => void): () => void {
    try {
      return this.service.onInMainOnly(eventName, listener);
    } catch (error) {
      log.error('主进程内部事件监听器注册失败:', error);
      return () => {};
    }
  }

  /**
   * 主进程内部一次性监听事件
   * 这个方法可以替代原有的 mainAppBus.once()
   *
   * @param eventName 事件名称
   * @param listener 事件监听器
   * @returns 取消监听的函数
   */
  public static onceInMain(eventName: string, listener: (...args: any[]) => void): () => void {
    try {
      return this.service.onceInMainOnly(eventName, listener);
    } catch (error) {
      log.error('主进程内部一次性事件监听器注册失败:', error);
      return () => {};
    }
  }

  /**
   * 主进程内部移除事件监听器
   * 这个方法可以替代原有的 mainAppBus.off()
   *
   * @param eventName 事件名称
   * @param listener 可选的具体监听器
   */
  public static offInMain(eventName: string, listener?: (...args: any[]) => void): void {
    try {
      this.service.offInMainOnly(eventName, listener);
    } catch (error) {
      log.error('主进程内部事件监听器移除失败:', error);
    }
  }

  // ================= 统一事件管理 =================

  /**
   * 统一的事件发送方法
   * 可以同时在主进程内部和向窗口发送事件
   *
   * @param eventName 事件名称
   * @param data 事件数据
   * @param options 发送选项
   */
  public static emitUnified<T = any>(
    eventName: string,
    data: T,
    options: {
      inMain?: boolean;
      toWindows?: boolean;
      targetWindow?: WindowType | WindowType[];
    } = {}
  ): boolean {
    try {
      return this.service.emitUnified(eventName, data, options);
    } catch (error) {
      log.error('统一事件发送失败:', error);
      return false;
    }
  }

  /**
   * 统一的事件监听方法
   * 可以同时监听主进程内部事件和跨窗口事件
   *
   * @param eventName 事件名称
   * @param listener 事件监听器
   * @param options 监听选项
   */
  public static onUnified<T = any>(
    eventName: string,
    listener: (data: T, source: 'main' | WindowType, eventId?: string) => void,
    options: {
      fromMain?: boolean;
      fromWindows?: boolean;
    } = {}
  ): () => void {
    try {
      return this.service.onUnified(eventName, listener, options);
    } catch (error) {
      log.error('统一事件监听器注册失败:', error);
      return () => {};
    }
  }

  // ================= 便捷方法 =================

  /**
   * 获取事件监听器数量
   */
  public static getListenerCount(eventName: string): number {
    try {
      return this.service.getMainEventListenerCount(eventName);
    } catch (error) {
      log.error('获取事件监听器数量失败:', error);
      return 0;
    }
  }

  /**
   * 获取所有事件名称
   */
  public static getEventNames(): (string | symbol)[] {
    try {
      return this.service.getMainEventNames();
    } catch (error) {
      log.error('获取事件名称失败:', error);
      return [];
    }
  }

  /**
   * 设置最大监听器数量
   */
  public static setMaxListeners(n: number): void {
    try {
      this.service.setMainMaxListeners(n);
    } catch (error) {
      log.error('设置最大监听器数量失败:', error);
    }
  }
}

// 导出便捷的别名
export const mainCrossWindow = MainCrossWindow;
