import { storeService } from './storeService';
import { AppSettings, DisplayScreenType, LanguageType, MainWindowStyle, ThemeType } from '../../shared/types/settings';
import { isMac } from '@main/common/env';
import { app } from 'electron';
import {DESKTOP_PATH,DOCUMENTS_PATH} from '@main/common/paths'
import { TrayManager } from '@main/tray/tray-manager';
import { WindowManager } from '@main/windows/window-manager';
import { registerMainWindowShortcut, reregisterClipboardShortcut } from '@main/windows/shortcuts';


/**
 * 设置服务类
 * 负责管理应用的所有设置
 */
class SettingsService {
    // 默认配置
    private DEFAULT_SETTINGS: AppSettings = {
        general: {
            launchAtLogin: true,
            showTray: true,
            hotkey: isMac ? 'Option+Space' : 'Alt+Space',
            language: app.getLocale().includes('zh') ? 'zh' : 'en' as LanguageType,
            displayScreen: 'mouse' as DisplayScreenType,
            theme: 'system' as ThemeType,
            mainWindowStyle: 'complete' as MainWindowStyle,
            appearance: 'default',
            pinned: false,
        },
        ai: {
            model: 'gpt-4o-mini',
            temperature: 0.7,
            maxTokens: 1000
        },
        desktopAssistant: {
            floatingBall: 'floating-ball',
            agentId: '1234567890'
        },
        pet: {
            available: false,
            alwaysOnTop: true,
            recommend: true,
            showTool: true
        },
        clipboard: {
            clipboardWindowStyle: 'bottom-bar',
            maxHistoryCount: 100,
            autoHide: true,
            hotkey: isMac ? 'Cmd+Ctrl+V' : 'Ctrl+Alt+V',
            checkInterval: 500 // 500毫秒检查一次剪切板
        },
        path:{
            allowWritePaths: [DESKTOP_PATH,DOCUMENTS_PATH],
        }
    };
    private settings: AppSettings = { ...this.DEFAULT_SETTINGS };


    /**
     * 加载配置，合并默认值和存储的值
     */
    loadSettings(): AppSettings {
        try {
            const storedSettings = storeService.get('settings') as Partial<AppSettings>;
            if (storedSettings && Object.keys(storedSettings).length > 0 && storedSettings.clipboard) {
                // 深度合并配置，保留默认值
                this.settings = {
                    general: { ...this.DEFAULT_SETTINGS.general, ...storedSettings.general },
                    ai: { ...this.DEFAULT_SETTINGS.ai, ...storedSettings.ai },
                    desktopAssistant: { ...this.DEFAULT_SETTINGS.desktopAssistant, ...storedSettings.desktopAssistant },
                    pet: { ...this.DEFAULT_SETTINGS.pet, ...storedSettings.pet },
                    clipboard: { ...this.DEFAULT_SETTINGS.clipboard, ...storedSettings.clipboard },
                    path: { ...this.DEFAULT_SETTINGS.path, ...storedSettings.path },
                };
            } else {
                this.settings = { ...this.DEFAULT_SETTINGS };
                // 注意：这里不立即保存，等渲染进程同步配置
            }
            // 返回设置的深拷贝，确保没有循环引用
            return JSON.parse(JSON.stringify(this.settings));
        } catch (error) {
            console.error('主进程加载配置失败，使用默认配置:', error);
            this.settings = { ...this.DEFAULT_SETTINGS };
        }
        // 返回设置的深拷贝，确保没有循环引用
        return JSON.parse(JSON.stringify(this.settings));
    }

    /**
     * 保存配置
     */
    saveSettings(): void {
        storeService.set('settings', this.settings);
    }

    /**
     * 更新配置并保存
     */
    updateSettings(newSettings: Partial<AppSettings>): void {
        // 首先从store读取最新配置，确保我们有最新的数据
        const storedSettings = storeService.get('settings') as Partial<AppSettings>;

        if (storedSettings) {
            this.settings = {
                general: { ...this.DEFAULT_SETTINGS.general, ...storedSettings.general },
                ai: { ...this.DEFAULT_SETTINGS.ai, ...storedSettings.ai },
                desktopAssistant: { ...this.DEFAULT_SETTINGS.desktopAssistant, ...storedSettings.desktopAssistant },
                pet: { ...this.DEFAULT_SETTINGS.pet, ...storedSettings.pet },
                clipboard: { ...this.DEFAULT_SETTINGS.clipboard, ...storedSettings.clipboard },
                path: { ...this.DEFAULT_SETTINGS.path, ...storedSettings.path },
            };
        }

        // 深度合并新配置
        if (newSettings.general) {
            this.settings.general = { ...this.settings.general, ...newSettings.general };
        }
        if (newSettings.ai) {
            this.settings.ai = { ...this.settings.ai, ...newSettings.ai };
        }
        if (newSettings.desktopAssistant) {
            this.settings.desktopAssistant = { ...this.settings.desktopAssistant, ...newSettings.desktopAssistant };
        }
        if (newSettings.pet) {
            this.settings.pet = { ...this.settings.pet, ...newSettings.pet };
        }
        if (newSettings.clipboard) {
            this.settings.clipboard = { ...this.settings.clipboard, ...newSettings.clipboard };
        }
        if (newSettings.path) {
            this.settings.path = { ...this.settings.path, ...newSettings.path };
        }

        // 实际设置系统自动启动
        if (typeof newSettings.general?.launchAtLogin !== 'undefined') {
            if (newSettings.general.launchAtLogin) {
                app.setLoginItemSettings({
                    openAtLogin: true,
                    openAsHidden: true
                });
            } else {
                app.setLoginItemSettings({
                    openAtLogin: false
                });
            }
        }

        // 设置托盘图标
        if (typeof newSettings.general?.showTray !== 'undefined') {
            if (newSettings.general.showTray) {
                TrayManager.init();
            } else {
                TrayManager.cleanup();
            }
        }

        // 设置主窗口快捷键
        if (typeof newSettings.general?.hotkey !== 'undefined') {
            registerMainWindowShortcut(WindowManager);
        }

        // 设置主窗口显示屏幕
        if (typeof newSettings.general?.displayScreen !== 'undefined') {
            const mainWindow = WindowManager.getMainWindow();
            if (mainWindow) {
                mainWindow.updateScreenPosition(newSettings.general.displayScreen);
            }
        }

        // 如果语言发生变化，向所有窗口广播语言变化事件
        if (typeof newSettings.general?.language !== 'undefined') {
            WindowManager.broadcastLanguageChange(newSettings.general.language);
        }

        // 如果主题发生变化，向所有窗口广播主题变化事件
        if (typeof newSettings.general?.theme !== 'undefined') {
            WindowManager.broadcastThemeChange(newSettings.general.theme);
        }

        // 如果包含剪贴板配置，同时更新窗口样式
        if (typeof newSettings.clipboard?.clipboardWindowStyle !== 'undefined') {
            WindowManager.updateClipboardHistoryStyle(newSettings.clipboard.clipboardWindowStyle);
        }

        // 设置剪贴板快捷键
        if (typeof newSettings.clipboard?.hotkey !== 'undefined') {
            reregisterClipboardShortcut(WindowManager);
        }

        // 如果剪贴板设置发生变化，广播到所有窗口
        if (newSettings.clipboard) {
            WindowManager.broadcastClipboardSettingsChange(this.settings.clipboard);
        }

        // 处理桌面助手设置
        if (newSettings.desktopAssistant) {
            // 处理悬浮球助手开关
            if (typeof newSettings.desktopAssistant.floatingBall !== 'undefined') {
                const floatingBall = WindowManager.getFloatingBall();
                if (newSettings.desktopAssistant.floatingBall === 'floating-ball') {
                    floatingBall?.show();
                } else {
                    floatingBall?.hide();
                }
            }
        }

        // 处理pet助手设置
        if (newSettings.pet) {
            // 处理动态桌面助手开关（pet助手）
            if (typeof newSettings.pet.available !== 'undefined') {
                const petWindow = WindowManager.getPetWindow();
                if (newSettings.pet.available) {
                    petWindow?.show();
                } else {
                    petWindow?.hide();
                }
            }
        }

        // 保存到存储
        this.saveSettings();
    }

    /**
     * 获取当前设置
     */
    getSettings(): AppSettings {
        // 返回设置的深拷贝，确保没有循环引用
        return JSON.parse(JSON.stringify(this.settings));
    }

    /**
     * 获取特定设置项
     */
    getSetting<K extends keyof AppSettings>(key: K): AppSettings[K] {
        return this.settings[key];
    }


    /**
     * 更新单个设置项
     */
    updateSetting<K extends keyof AppSettings, SK extends keyof AppSettings[K]>(
        category: K,
        key: SK,
        value: AppSettings[K][SK]
    ): void {
        this.updateSettings({
            [category]: {
                [key]: value
            }
        } as Partial<AppSettings>);
    }

}


// 导出单例
export const settingsService = new SettingsService();


