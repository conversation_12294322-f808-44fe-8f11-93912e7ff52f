import * as fs from 'fs/promises';
import * as path from 'path';
import globby from 'globby';

/**
 * 模型服务
 * 负责模型文件的处理与管理
 */
class PetService {
  /**
   * 获取模型文件路径
   * @param fileOrPath File对象或字符串路径
   * @returns 模型文件路径数组
   */
  async getModels(fileOrPath: string | { path: string }): Promise<string[]> {
    // 处理File对象或路径字符串
    const filePath = typeof fileOrPath === 'string' 
      ? fileOrPath 
      : fileOrPath.path;
    
    // 如果是模型文件，直接返回
    if (filePath.endsWith('model.json') || filePath.endsWith('.model3.json')) {
      return [filePath];
    }

    try {
      console.log('待处理文件:', filePath);
      
      // 获取文件状态
      const stat = await fs.stat(filePath);
      
      // 如果是目录，搜索其中的模型文件
      if (stat.isDirectory()) {
        const result = await globby(['**/*model.json', '**.model3.json'], {
          cwd: filePath,
        });
        
        console.log('找到模型文件:', result);
        
        // 将相对路径转换为绝对路径
        return result.map((f) => path.join(filePath, f));
      }
      
      // 不是模型文件也不是目录，返回空数组
      return [filePath];
    } catch (error) {
      console.error('获取模型文件失败:', error);
      return [];
    }
  }
}

export const petService = new PetService(); 