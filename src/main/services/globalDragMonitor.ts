import { BrowserWindow, screen } from 'electron';
import * as log from '../logging';
import { FLOATING_BALL } from '../../shared/ipc';

/**
 * 全局拖拽监控服务
 * 监控系统级别的文件拖拽操作，当检测到拖拽时通知悬浮球
 */
class GlobalDragMonitor {
  private isGlobalDragActive = false;
  private floatingBallWindow: BrowserWindow | null = null;
  private dragCheckInterval: NodeJS.Timeout | null = null;

  /**
   * 初始化全局拖拽监控
   */
  public init(floatingBallWindow: BrowserWindow): void {
    this.floatingBallWindow = floatingBallWindow;
    this.setupAlternativeDragMonitoring();
    log.info('全局拖拽监控服务已初始化');
  }

  /**
   * 设置替代的拖拽监控方案
   * 不依赖悬浮球窗口的连接状态
   */
  private setupAlternativeDragMonitoring(): void {
    if (!this.floatingBallWindow) return;

    // 方案1: 监听悬浮球窗口的拖拽事件（无论是否连接到Vite）
    this.floatingBallWindow.webContents.on('dom-ready', () => {
      this.injectDragMonitorScript();
    });

    // 方案2: 立即尝试注入脚本（如果窗口已经准备好）
    if (this.floatingBallWindow.webContents.isLoading() === false) {
      setTimeout(() => {
        this.injectDragMonitorScript();
      }, 1000);
    }

    // 方案3: 监听窗口的拖拽事件作为备用
    this.setupWindowDragEvents();
  }

  /**
   * 注入拖拽监控脚本
   */
  private injectDragMonitorScript(): void {
    if (!this.floatingBallWindow) return;

    const dragMonitorScript = `
      (function() {
        // 等待DOM加载完成
        function waitForFloatingBall() {
          const floatingBall = document.getElementById('floating-ball');
          if (floatingBall) {
            console.log('🎯 找到悬浮球元素，开始设置拖拽监听');
            setupFloatingBallDragListeners(floatingBall);
          } else {
            console.log('⏳ 等待悬浮球元素...');
            setTimeout(waitForFloatingBall, 100);
          }
        }

        // 定期检查拖拽监听器状态
        setInterval(() => {
          const floatingBall = document.getElementById('floating-ball');
          if (floatingBall) {
            console.log('🔍 悬浮球元素存在，拖拽状态:', isDragActive);
          } else {
            console.log('❌ 悬浮球元素丢失！');
          }
        }, 5000); // 每5秒检查一次

        // 发送拖拽状态到主进程
        function sendDragStateToMain(isActive) {
          if (window.electron && window.electron.floatingBall) {
            if (isActive) {
              window.electron.floatingBall.onGlobalDragStart();
              console.log('📡 发送全局拖拽开始到主进程');
            } else {
              window.electron.floatingBall.onGlobalDragEnd();
              console.log('📡 发送全局拖拽结束到主进程');
            }
          } else {
            console.error('❌ 无法发送拖拽状态 - API不存在');
          }
        }

        // 提供给React组件的全局函数，用于立即重置拖拽状态
        window.clearFloatingBallDragState = function() {
          console.log('🔄 React组件请求立即清除拖拽状态');
          if (dragLeaveTimer) {
            clearTimeout(dragLeaveTimer);
            dragLeaveTimer = null;
          }
          if (isDragActive) {
            isDragActive = false;
            sendDragStateToMain(false);
          }
        };

        // 拖拽状态管理
        let isDragActive = false;
        let dragLeaveTimer = null;

        // 设置悬浮球拖拽监听器
        function setupFloatingBallDragListeners(element) {
          console.log('🔧 设置悬浮球拖拽监听器');

          // 拖拽进入（不冒泡）
          element.addEventListener('dragenter', function(e) {
            // 不阻止事件，让React组件也能接收到
            console.log('📥 文件拖拽进入悬浮球');

            // 清除可能存在的离开定时器
            if (dragLeaveTimer) {
              clearTimeout(dragLeaveTimer);
              dragLeaveTimer = null;
            }

            // 只有在未激活状态时才发送开始信号
            if (!isDragActive) {
              isDragActive = true;
              sendDragStateToMain(true);
            }
          });

          // 拖拽悬停（必须阻止默认行为以允许drop）
          element.addEventListener('dragover', function(e) {
            e.preventDefault();
            // 不阻止事件传播，让React组件也能处理
          });

          // 拖拽离开（不冒泡）
          element.addEventListener('dragleave', function(e) {
            // 不阻止事件传播，让React组件也能处理
            console.log('📤 文件离开悬浮球');

            // 增加延迟时间，避免与React组件的drop处理冲突
            dragLeaveTimer = setTimeout(() => {
              if (isDragActive) {
                isDragActive = false;
                sendDragStateToMain(false);
                console.log('🔄 延迟处理拖拽离开');
              }
              dragLeaveTimer = null;
            }, 200); // 增加到200ms延迟，给React组件更多处理时间
          });

          // 完全移除 drop 事件监听，让React组件独自处理
          // drop 事件的状态重置由 dragleave 的延迟处理来完成
          console.log('� 全局监控脚本不监听drop事件，完全交给React组件处理');
        }

        // 注释：sendFileInfoToMain 函数已移除
        // 文件信息处理现在完全由React组件的handleDrop处理
        // 与桌宠逻辑保持一致

        // 开始等待悬浮球元素
        waitForFloatingBall();
      })();
    `;

    this.floatingBallWindow.webContents.executeJavaScript(dragMonitorScript)
      .then(() => {
        log.info('全局拖拽监控脚本已注入');
      })
      .catch((error) => {
        log.error('注入全局拖拽监控脚本失败:', error);
      });
  }

  /**
   * 设置窗口级别的拖拽事件监听
   */
  private setupWindowDragEvents(): void {
    if (!this.floatingBallWindow) return;

    // 监听窗口的拖拽事件
    this.floatingBallWindow.webContents.on('will-navigate', (event, navigationUrl) => {
      // 阻止导航，但可以用来检测拖拽
      if (navigationUrl.startsWith('file://')) {
        event.preventDefault();
        log.info('检测到文件拖拽到悬浮球窗口');
        this.handleGlobalDragStart();
      }
    });

    // 添加系统级别的拖拽检测
    this.setupSystemDragDetection();
  }

  /**
   * 设置系统级别的拖拽检测
   */
  private setupSystemDragDetection(): void {
    if (!this.floatingBallWindow) return;

    // 恢复文件拖拽检测，但只在必要时临时禁用穿透
    const checkInterval = setInterval(() => {
      try {
        // 只有在没有全局拖拽活动时才进行检测
        if (!this.isGlobalDragActive) {
          const cursorPos = screen.getCursorScreenPoint();
          const windowBounds = this.floatingBallWindow?.getBounds();

          if (windowBounds) {
            const isOverFloatingBall =
              cursorPos.x >= windowBounds.x &&
              cursorPos.x <= windowBounds.x + windowBounds.width &&
              cursorPos.y >= windowBounds.y &&
              cursorPos.y <= windowBounds.y + windowBounds.height;

            if (isOverFloatingBall) {
              // 临时禁用穿透来检测可能的文件拖拽
              console.log('🔍 鼠标在悬浮球上，临时禁用穿透检测文件拖拽');
              this.floatingBallWindow?.setIgnoreMouseEvents(false);

              // 快速恢复，让渲染进程的逻辑接管
              setTimeout(() => {
                if (!this.isGlobalDragActive) {
                  console.log('🔍 恢复穿透检测完成，让渲染进程接管');
                  // 不强制设置穿透，让渲染进程决定当前状态
                  // this.floatingBallWindow?.setIgnoreMouseEvents(true, { forward: true });
                }
              }, 100);
            }
          }
        }
      } catch (error) {
        log.error('系统拖拽检测错误:', error);
      }
    }, 300); // 300ms间隔，平衡性能和响应性

    // 清理定时器
    this.floatingBallWindow.on('closed', () => {
      clearInterval(checkInterval);
    });
  }

  /**
   * 处理全局拖拽开始
   */
  public handleGlobalDragStart(): void {
    if (this.isGlobalDragActive) return;

    this.isGlobalDragActive = true;
    log.info('全局拖拽开始');

    // 通知悬浮球窗口，并设置为不可穿透以接收文件拖拽
    if (this.floatingBallWindow && !this.floatingBallWindow.isDestroyed()) {
      this.floatingBallWindow.webContents.send(FLOATING_BALL.GLOBAL_DRAG_START);

      // 文件拖拽时必须设置为不可穿透，否则无法接收拖拽事件
      this.floatingBallWindow.setIgnoreMouseEvents(false);
    }
  }

  /**
   * 处理全局拖拽结束
   */
  public handleGlobalDragEnd(): void {
    if (!this.isGlobalDragActive) return;

    this.isGlobalDragActive = false;
    log.info('全局拖拽结束');

    // 通知悬浮球窗口，文件拖拽结束后让渲染进程决定穿透状态
    if (this.floatingBallWindow && !this.floatingBallWindow.isDestroyed()) {
      this.floatingBallWindow.webContents.send(FLOATING_BALL.GLOBAL_DRAG_END);

      // 文件拖拽结束后，暂时不设置穿透状态，让渲染进程根据当前状态决定
      // 这样可以避免与渲染进程的穿透控制逻辑冲突
      // this.floatingBallWindow.setIgnoreMouseEvents(true, { forward: true });
    }
  }

  /**
   * 获取当前全局拖拽状态
   */
  public isGlobalDragActiveState(): boolean {
    return this.isGlobalDragActive;
  }

  /**
   * 清理资源
   */
  public cleanup(): void {
    this.isGlobalDragActive = false;

    if (this.dragCheckInterval) {
      clearInterval(this.dragCheckInterval);
      this.dragCheckInterval = null;
    }

    this.floatingBallWindow = null;
    log.info('全局拖拽监控服务已清理');
  }
}

// 导出单例
export const globalDragMonitor = new GlobalDragMonitor();
