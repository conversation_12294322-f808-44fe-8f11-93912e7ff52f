import { app } from 'electron';
import * as os from 'os';
import * as crypto from 'crypto';
import { machineId } from 'node-machine-id';
import * as log from '../logging';

export interface SystemInfo {
  computerId: string;
  homePath: string;
  desktopPath: string; // 新增：桌面目录
  documentsPath: string; // 新增：文档目录
  platform: string;
  arch: string;
  version: string;
  hostname: string;
  username: string;
  cpus: number;
  totalMemory: number;
  freeMemory: number;
  uptime: number;
  networkInterfaces: any;
}

/**
 * 系统信息服务
 */
class SystemService {
  private computerId: string | null = null;

  /**
   * 获取电脑唯一标识符
   */
  async getComputerId(): Promise<string> {
    if (this.computerId) {
      return this.computerId;
    }

    try {
      // 使用 node-machine-id 获取机器唯一标识符
      this.computerId  = await machineId(true);
      
      // 对ID进行哈希处理，确保格式一致
      // this.computerId = crypto.createHash('sha256').update(id).digest('hex').substring(0, 32);
      
      log.info(`获取电脑ID成功: ${this.computerId.substring(0, 8)}...`);
      return this.computerId;
    } catch (error) {
      log.error('获取电脑ID失败:', error);
      log.error('备用方案也失败，生成随机ID');
      // 如果所有方法都失败，生成一个基于当前时间和随机数的ID
      this.computerId = crypto.randomUUID().replace(/-/g, '');
      return this.computerId;
      
    }
  }
  /**
   * 获取用户主目录路径
   */
  getHomePath(): string {
    try {
      return app.getPath('home');
    } catch (error) {
      log.error('获取主目录路径失败:', error);
      return os.homedir();
    }
  }

  /**
   * 获取完整的系统信息
   */
  async getSystemInfo(): Promise<SystemInfo> {
    try {
      const computerId = await this.getComputerId();
      const homePath = app.getPath('home') || '';
      const desktopPath = app.getPath('desktop') || ''; // 新增
      const documentsPath = app.getPath('documents') || ''; // 新增
      const networkInterfaces = os.networkInterfaces();
      
      // 过滤敏感信息
      const filteredNetworkInterfaces: any = {};
      for (const [name, nets] of Object.entries(networkInterfaces)) {
        if (nets) {
          filteredNetworkInterfaces[name] = nets.map(net => ({
            family: net.family,
            internal: net.internal,
            mac: net.mac
          }));
        }
      }

      const systemInfo: SystemInfo = {
        computerId, // 设备唯一标识码
        homePath, // 用户主目录
        desktopPath, // 桌面目录
        documentsPath, // 文档目录
        platform: os.platform(), // 操作系统名称
        arch: os.arch(), // 操作系统架构
        version: os.release(), // 操作系统版本
        hostname: os.hostname(), // 主机名
        username: os.userInfo().username, // 用户名
        cpus: os.cpus().length, // 处理器核心数
        totalMemory: os.totalmem(), // 总内存 
        freeMemory: os.freemem(), // 可用内存
        uptime: os.uptime(), // 系统运行时间
        networkInterfaces: filteredNetworkInterfaces // 网络接口信息
      };

      log.info('获取系统信息成功', JSON.stringify(systemInfo));
      return systemInfo;
    } catch (error) {
      log.error('获取系统信息失败:', error);
      throw error;
    }
  }
}

// 导出单例
export const systemService = new SystemService(); 