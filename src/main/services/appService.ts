import { app, screen, nativeImage } from 'electron';
import * as fs from 'fs';
import * as path from 'path';
import { exec } from 'child_process';
import { EventEmitter } from 'events';
import appSearch from '../../core/app-search';

export interface AppInfo {
  id: string;
  name: string;
  path: string;
  icon?: string;
  lastUsed?: number;
  type: 'app';
}

/**
 * 应用管理服务
 * 负责扫描、搜索、启动和管理应用
 */
class AppManager extends EventEmitter {
  private allApps: AppInfo[] = [];
  private lastScanTime: number = 0;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存
  private isScanning: boolean = false;
  
  constructor() {
    super();
    this.launchApp = this.launchApp.bind(this);
    // 启动时进行一次扫描
    this.initializeAppList();
    // 设置定期检查
    this.setupPeriodicCheck();
  }

  // 初始化应用列表
  private async initializeAppList(): Promise<void> {
    try {
      await this.scanApps();
    } catch (error) {
      console.error('初始化应用列表失败:', error);
    }
  }

  // 设置定期检查（每30分钟检查一次）
  private setupPeriodicCheck(): void {
    setInterval(async () => {
      try {
        await this.scanApps(true);
      } catch (error) {
        console.error('定期扫描应用失败:', error);
      }
    }, 30 * 60 * 1000);
  }

  // 扫描应用程序
  private async scanApps(force: boolean = false): Promise<void> {
    const now = Date.now();
    
    // 如果正在扫描，直接返回
    if (this.isScanning) {
      return;
    }
    
    // 检查缓存是否有效
    if (!force && this.allApps.length > 0 && (now - this.lastScanTime) < this.CACHE_DURATION) {
      return;
    }
    
    this.isScanning = true;
    
    try {
      console.log('开始扫描应用程序...');
      const newApps = await appSearch(nativeImage);
      
      // 检查是否有变化
      const hasChanges = this.hasAppListChanged(newApps);
      
      if (hasChanges || force) {
        this.allApps = newApps;
        this.lastScanTime = now;
        
        // 发射应用列表更新事件
        this.emit('listUpdated', this.allApps, {
          count: this.allApps.length,
          timestamp: this.lastScanTime
        });
        
        console.log(`应用列表已更新，共 ${newApps.length} 个应用`);
      }
    } finally {
      this.isScanning = false;
    }
  }

  // 检查应用列表是否有变化
  private hasAppListChanged(newApps: AppInfo[]): boolean {
    if (this.allApps.length !== newApps.length) {
      return true;
    }
    
    // 简单比较：检查应用路径集合是否相同
    const oldPaths = new Set(this.allApps.map(app => app.path));
    const newPaths = new Set(newApps.map(app => app.path));
    
    if (oldPaths.size !== newPaths.size) {
      return true;
    }
    
    for (const path of newPaths) {
      if (!oldPaths.has(path)) {
        return true;
      }
    }
    
    return false;
  }

  // 启动应用
  public async launchApp(appPath: string): Promise<boolean> {
    try {
      if (process.platform === 'darwin') {
        exec(`open "${appPath}"`);
      } else if (process.platform === 'win32') {
        exec(`start "" "${appPath}"`);
      } else if (process.platform === 'linux') {
        exec(`xdg-open "${appPath}"`);
      }
      return true;
    } catch (error) {
      console.error(`启动应用 ${appPath} 出错:`, error);
      return false;
    }
  }
  
  // 获取应用列表（优化版）
  public async getAppList(): Promise<AppInfo[]> {
    try {
      // 先尝试从缓存获取
      await this.scanApps();
      return [...this.allApps]; // 返回副本，避免外部修改
    } catch (error) {
      console.error('获取应用列表失败:', error);
      return [];
    }
  }

  // 强制刷新应用列表
  public async refreshAppList(): Promise<AppInfo[]> {
    try {
      await this.scanApps(true);
      return [...this.allApps];
    } catch (error) {
      console.error('刷新应用列表失败:', error);
      return [];
    }
  }
}

// 导出单例实例
export const appService = new AppManager();