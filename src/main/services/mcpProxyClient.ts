// 恢复必要的导入
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { InMemoryTransport } from '@modelcontextprotocol/sdk/inMemory.js';
import { createServer } from './mcpProxy';
import { MCPServer } from '../../shared/types/mcp';
import * as logging from '../logging';
import WebSocket from 'ws';
import { JSONRPCMessage, JSONRPCMessageSchema } from '@modelcontextprotocol/sdk/types.js';
import { Transport } from '@modelcontextprotocol/sdk/shared/transport.js';
import { createClients,ConnectedClient } from './mcpClient';

// WebSocket MCP Transport 类 - 用于与远程客户端通信
class WebSocketMCPTransport implements Transport {
  private ws: WebSocket | null = null;
  private url: string;
  private isStarted = false;
  private isReconnecting = false;
  private reconnectAttempt = 0;
  private backoffDelay = 5000; // 初始延迟 5 秒，增加稳定性
  private maxBackoffDelay = 120000; // 最大延迟 120 秒
  private maxReconnectAttempts = 10; // 最大重连次数
  private reconnectTimer: NodeJS.Timeout | null = null;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private lastPongTime = 0;
  private lastPingTime = 0; // 添加：记录最后发送ping的时间
  private consecutiveFailures = 0; // 添加：连续失败次数
  private disableReconnect = false; // 新增：禁用重连标志

  onclose?: () => void;
  onerror?: (error: Error) => void;
  onmessage?: (message: JSONRPCMessage) => void;
  sessionId?: string;

  constructor(url: string) {
    this.url = url;
  }

  // 新增：禁用重连方法
  public disableReconnectAttempts(): void {
    this.disableReconnect = true;
    logging.info('已禁用WebSocket重连尝试');
    
    // 清理重连定时器
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  // 新增：启用重连方法
  public enableReconnectAttempts(): void {
    this.disableReconnect = false;
    logging.info('已启用WebSocket重连尝试');
  }

  private startHeartbeat(): void {
    // 停止任何现有的心跳，以防重复
    this.stopHeartbeat();
    // 每30秒主动发送ping验证连接
    this.heartbeatTimer = setInterval(() => {
      this.performHealthCheck();
    }, 30000); // 检查频率改为30秒
    logging.debug('心跳监控已启动 (30秒间隔)');
  }

  private async performHealthCheck(): Promise<void> {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      logging.warn(`WebSocket连接状态异常: readyState = ${this.ws?.readyState || 'null'}`);
      // 如果连接不是打开状态，直接尝试重连，不计为ping失败
      this.handleConnectionFailure();
      return;
    }

    // 检查上一个ping是否超时
    if (this.lastPingTime > 0) {
      const elapsed = Date.now() - this.lastPingTime;
      logging.warn(`上一个健康检查ping超时 (${elapsed}ms)`);
      this.consecutiveFailures++;
      
      // 3次连续失败就重连
      if (this.consecutiveFailures >= 3) {
        logging.error(`健康检查连续 ${this.consecutiveFailures} 次失败，判定连接已断开`);
        this.handleConnectionFailure();
        return; // 如果决定重连，则不发送新的ping
      }
    }

    try {
      // 发送新的ping
      const now = Date.now();
      const pingId = `health-check-${now}`;
      const pingMessage = {
        jsonrpc: '2.0' as const,
        id: pingId,
        method: 'ping',
        params: {}
      };
      
      this.lastPingTime = now;
      await this.send(pingMessage);
      logging.debug(`发送健康检查ping: ${pingId}`);
      
    } catch (error) {
      logging.error('发送健康检查ping失败:', error);
      // 发送失败也算作一次失败
      this.consecutiveFailures++;
      if (this.consecutiveFailures >= 3) {
        this.handleConnectionFailure();
      }
    }
  }

  private handleConnectionFailure(): void {
    logging.warn('检测到连接失败，准备重连');
    
    // 停止心跳监控，重连后会重新启动
    this.stopHeartbeat();
    
    // 强制触发重连
    this.isStarted = false;
    this.scheduleReconnect();
  }

  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
      logging.debug('已停止心跳监控');
    }
    
    // 重置ping状态
    this.lastPingTime = 0;
    this.consecutiveFailures = 0;
  }

  async start(): Promise<void> {
    if (this.isStarted && !this.isReconnecting) {
      return;
    }

    return this.connect();
  }

  private async connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      // 清理之前的连接
      if (this.ws) {
        this.ws.removeAllListeners();
        this.ws.close();
      }

      this.ws = new WebSocket(this.url);

      this.ws.on('open', async () => {
        logging.info(`WebSocket MCP Transport 连接成功: ${this.url}`);
        this.isStarted = true;
        this.isReconnecting = false;
        
        // 重置重连参数
        this.reconnectAttempt = 0;
        this.backoffDelay = 5000;
        this.lastPongTime = Date.now();
        this.consecutiveFailures = 0;
        this.lastPingTime = 0;
        
        // 启动心跳监控
        this.startHeartbeat();
        
        // 延迟进行首次健康检查，确保连接真的稳定
        setTimeout(() => {
          this.performHealthCheck().catch(error => {
            logging.error('初始健康检查失败:', error);
          });
        }, 10000); // 延迟10秒进行初始检查，给连接充分时间稳定
        
        resolve();
      });

      // 处理原生WebSocket ping帧
      this.ws.on('ping', (data) => {
        logging.debug('收到原生WebSocket ping帧');
        this.ws?.pong(data);
        logging.debug('已回应原生WebSocket pong帧');
      });

      // 处理原生WebSocket pong帧
      this.ws.on('pong', (data) => {
        logging.debug('收到原生WebSocket pong帧');
      });

      this.ws.on('error', (error) => {
        logging.error('WebSocket MCP Transport error:', error);
        this.onerror?.(error as Error);
        
        if (!this.isStarted) {
          // 如果是初始连接失败，拒绝 Promise
          reject(error);
        } else {
          // 如果是已建立连接后的错误，触发重连
          this.scheduleReconnect();
        }
      });

      this.ws.on('close', (code, reason) => {
        const reasonStr = reason?.toString() || 'unknown';
        logging.warn(`WebSocket MCP Transport 连接关闭: ${code} ${reasonStr}`);
        
        this.isStarted = false;
        this.stopHeartbeat();
        
        // 如果禁用重连，不进行重连
        if (this.disableReconnect) {
          logging.info('重连已禁用，不进行重连尝试');
          this.onclose?.();
          return;
        }
        
        // 如果不是主动关闭，则尝试重连
        if (code !== 1000 && !this.isReconnecting) {
          this.scheduleReconnect();
        } else if (code === 1000) {
          // 正常关闭，通知上层
          this.onclose?.();
        }
      });

      this.ws.on('message', (data) => {
        try {
          const messageText = data.toString();
          logging.debug('Received WebSocket message:', messageText);
          
          // 预处理消息，确保 params 字段符合 MCP 协议要求
          const rawMessage = JSON.parse(messageText);
          if (rawMessage.params === null) {
            rawMessage.params = {};
          }
          
          const message = JSONRPCMessageSchema.parse(rawMessage);
          // 特殊处理 ping 消息 - 保持连接活跃
          if ('method' in message && message.method === 'ping' && 'id' in message) {
            this.lastPongTime = Date.now(); // 更新最后收到ping的时间
            logging.info(`收到服务器 ping 消息，ID: ${message.id}`);
            
            try {
              // 直接回应 pong，不转发给 MCP 服务器
              const pongResponse = {
                jsonrpc: '2.0' as const,
                id: message.id,
                result: {}
              };
              
              const pongText = JSON.stringify(pongResponse);
              this.ws?.send(pongText);
              logging.debug(`已回应 pong 消息，ID: ${message.id}`);
              return; // 不继续处理
            } catch (pongError) {
              logging.error('发送 pong 响应失败:', pongError);
              return;
            }
          }
          
          // 特殊处理健康检查的pong响应
          if ('result' in message && 'id' in message && typeof message.id === 'string' && message.id.startsWith('health-check-')) {
            const now = Date.now();
            
            // 从健康检查ID中提取时间戳进行响应时间计算
            const pingIdMatch = message.id.match(/health-check-(\d+)/);
            let responseTime = 0;
            if (pingIdMatch) {
              const pingTimestamp = parseInt(pingIdMatch[1]);
              responseTime = now - pingTimestamp;
            }
            
            logging.debug(`收到健康检查响应: ${message.id}, 响应时间: ${responseTime}ms`);
            // 重置状态
            this.consecutiveFailures = 0;
            this.lastPingTime = 0;
            return; // 不继续处理
          }
          
          // 其他消息正常转发给 MCP 服务器
          this.onmessage?.(message);
        } catch (error) {
          logging.error('Error parsing WebSocket message:', error);
          this.onerror?.(error as Error);
        }
      });
    });
  }

  private scheduleReconnect(): void {
    // 如果禁用重连，直接返回
    if (this.disableReconnect) {
      logging.info('重连已禁用，跳过重连尝试');
      return;
    }

    if (this.isReconnecting) {
      return;
    }

    // 检查是否超过最大重连次数
    if (this.reconnectAttempt >= this.maxReconnectAttempts) {
      logging.error(`已达到最大重连次数 (${this.maxReconnectAttempts})，停止重连尝试`);
      this.onclose?.();
      return;
    }

    this.isReconnecting = true;
    this.reconnectAttempt++;

    // 计算退避延迟（指数退避 + 随机抖动）
    const baseDelay = Math.min(this.backoffDelay, this.maxBackoffDelay);
    const jitter = Math.random() * 0.1 * baseDelay; // 10% 随机抖动
    const delay = baseDelay + jitter;

    logging.info(`WebSocket 将在 ${Math.round(delay/1000)} 秒后尝试重连 (第${this.reconnectAttempt}/${this.maxReconnectAttempts}次尝试)`);

    this.reconnectTimer = setTimeout(async () => {
      try {
        await this.connect();
        logging.info('WebSocket 重连成功');
        // 重连成功后重置退避延迟
        this.backoffDelay = 5000;
      } catch (error) {
        logging.error('WebSocket 重连失败:', error);
        
        // 增加退避延迟（指数退避）
        this.backoffDelay = Math.min(this.backoffDelay * 2, this.maxBackoffDelay);
        
        // 继续尝试重连
        this.isReconnecting = false;
        this.scheduleReconnect();
      }
    }, delay);
  }

  async close(): Promise<void> {
    // 清理重连定时器
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    // 清理心跳定时器
    this.stopHeartbeat();

    this.isReconnecting = false;
    this.isStarted = false;
    this.consecutiveFailures = 0;
    this.lastPingTime = 0;

    if (this.ws) {
      // 设置为正常关闭
      this.ws.close(1000, 'Normal closure');
      this.ws = null;
    }
  }

  async send(message: JSONRPCMessage): Promise<void> {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      throw new Error('WebSocket is not connected');
    }

    try {
      const messageText = JSON.stringify(message);
      this.ws.send(messageText);
      logging.debug('Sent WebSocket message:', messageText);
    } catch (error) {
      logging.error('Error sending WebSocket message:', error);
      throw error;
    }
  }
}

export class ProxyMCPClient {
  // 恢复本地代理相关属性
  private localClient: Client | null = null;
  private proxyServer: any = null;
  private proxyCleanup: (() => Promise<void>) | null = null;
  
  // 保留WebSocket代理相关属性
  private wsProxyServer: any = null;
  private wsProxyCleanup: (() => Promise<void>) | null = null;
  private wsTransport: WebSocketMCPTransport | null = null;
  private isConnected = false;
  
  // 添加：跟踪已加载的服务器
  private loadedServers: Set<string> = new Set();

  async connect(servers: Record<string, MCPServer>, websocketUrl?: string) {
    logging.info(`开始连接代理服务器，当前连接状态: ${this.isConnected}, WebSocket URL: ${websocketUrl || '无'}`);
    
    if (this.isConnected) {
      logging.info('代理已经连接，跳过重复连接');
      return;
    }

    try {
      // 1. 始终创建空的本地代理服务器
      logging.info('步骤1: 设置本地代理服务器...');
      await this.setupLocalProxy();
      
      // 2. 如果提供了WebSocket URL，则同时创建WebSocket代理
      if (websocketUrl) {
        logging.info('步骤2: 设置WebSocket代理服务器...');
        await this.setupWebSocketBridge(websocketUrl);
      } else {
        logging.info('步骤2: 跳过WebSocket代理设置（无URL）');
      }
      
      // 3. 统一加载服务器到所有代理
      logging.info('步骤3: 加载MCP服务器到所有代理...');
      await this.loadServersToAllProxies(servers);
      
      this.isConnected = true;
      
      // 4. 启用WebSocket重连机制
      if (websocketUrl) {
        logging.info('步骤4: 启用WebSocket重连机制...');
        this.enableReconnect();
      }
      
      logging.info('✅ 代理服务器连接成功（本地代理 + WebSocket代理）');
    } catch (error) {
      logging.error('代理服务器连接失败:', error);
      await this.disconnect();
      throw error;
    }
  }

  private async setupLocalProxy() {
    logging.info('正在设置本地代理服务器...');
    
    try {
      // 创建空的本地代理服务器
      const { server: proxyServer, cleanup: proxyCleanup } = await createServer({});
      this.proxyServer = proxyServer;
      this.proxyCleanup = proxyCleanup;
      
      // 创建内存传输和本地客户端
      const [localClientTransport, localServerTransport] = InMemoryTransport.createLinkedPair();
      this.localClient = new Client(
        {
          name: 'local-mcp-client',
          version: '1.0.0',
        },
        {
          capabilities: {
            prompts: {},
            resources: { subscribe: true },
            tools: {},
          },
        }
      );
      
      // 连接本地客户端到代理服务器
      await Promise.all([
        this.localClient.connect(localClientTransport),
        this.proxyServer.connect(localServerTransport)
      ]);
      
      logging.info('本地代理服务器设置成功');
    } catch (error) {
      logging.error('本地代理服务器设置失败:', error);
      throw error;
    }
  }

  private async setupWebSocketBridge(websocketUrl: string) {
    logging.info(`正在连接到远程 WebSocket: ${websocketUrl}`);
    
    try {
      // 创建空的WebSocket代理服务器实例
      const { server: wsProxyServer, cleanup: wsCleanup } = await createServer({});
      this.wsProxyServer = wsProxyServer;
      this.wsProxyCleanup = wsCleanup;
    
      // 创建带重连功能的 WebSocket 传输
      this.wsTransport = new WebSocketMCPTransport(websocketUrl);
      
      // 将 WebSocket 传输连接到独立的代理服务器
      await this.wsProxyServer.connect(this.wsTransport);
      
      logging.info('WebSocket 独立代理服务器连接成功');
    } catch (error) {
      logging.error('WebSocket 代理服务器连接失败:', error);
      throw error;
    }
  }

  // 新方法：统一加载服务器到所有可用的代理
  private async loadServersToAllProxies(servers: Record<string, MCPServer>) {
    logging.info('开始加载MCP服务器到所有代理...');
    
    // 分类服务器：快速启动的和需要安装依赖的
    const fastServers: Record<string, MCPServer> = {};
    const slowServers: Record<string, MCPServer> = {};
    
    Object.entries(servers).forEach(([key, server]) => {
      if (server.type === 'sse' || server.type === 'streamableHttp') {
        fastServers[key] = server;
      } else {
        slowServers[key] = server;
      }
    });
    
    try {
      // 1. 优先加载快速启动的服务器
      if (Object.keys(fastServers).length > 0) {
        logging.info('加载快速启动的MCP服务器...');
        await this.loadServerBatchToAllProxies(fastServers);
      }
      
      // 2. 并行加载需要安装依赖的服务器
      if (Object.keys(slowServers).length > 0) {
        logging.info('开始并行加载需要依赖的MCP服务器...');
        const slowServerPromises = Object.entries(slowServers).map(async ([key, server]) => {
          try {
            logging.info(`后台加载MCP服务器: ${key} (${server.name})`);
            await this.addSingleServerToAllProxies(key, server);
            logging.info(`成功加载MCP服务器: ${key}`);
            return { key, success: true };
          } catch (error) {
            logging.warn(`加载MCP服务器失败: ${key}`, error);
            return { key, success: false, error };
          }
        });
        
        await Promise.allSettled(slowServerPromises);
      }
      
      logging.info('所有MCP服务器已加载到所有代理');
    } catch (error) {
      logging.error('加载MCP服务器时出错:', error);
    }
  }

  // 新方法：批量加载服务器到所有代理
  private async loadServerBatchToAllProxies(servers: Record<string, MCPServer>) {
    const serverEntries = Object.entries(servers);
    const batchSize = 3;
    
    for (let i = 0; i < serverEntries.length; i += batchSize) {
      const batch = serverEntries.slice(i, i + batchSize);
      const batchPromises = batch.map(async ([key, server]) => {
        try {
          await this.addSingleServerToAllProxies(key, server);
          return { key, success: true };
        } catch (error) {
          logging.warn(`批量加载服务器失败: ${key}`, error);
          return { key, success: false, error };
        }
      });
      
      await Promise.allSettled(batchPromises);
      
      // 批次间稍作延迟
      if (i + batchSize < serverEntries.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
  }

  // 新方法：添加单个服务器到所有代理
  private async addSingleServerToAllProxies(key: string, server: MCPServer) {
    // 检查是否已经加载过
    if (this.loadedServers.has(key)) {
      logging.info(`服务器 ${key} 已经加载，跳过重复加载`);
      return;
    }
  
    try {
      // 从共享池获取或创建服务器实例
      const sharedPool = SharedMCPServerPool.getInstance();
      
      // 在重新连接时，强制重新创建服务器实例
      // 因为现有的实例可能在断开连接时已经失效
      if (this.isConnected && sharedPool.getServer(key)) {
        logging.info(`重新连接时清理现有服务器实例: ${key}`);
        await sharedPool.removeServer(key);
      }
      
      const serverInstance = await sharedPool.getOrCreateServer(key, server);
      
      // 将服务器实例注册到本地代理（不创建新实例，只是引用）
      // 暂时注册掉，如需要本地代理直接执行mcp时打开
      // if (this.proxyServer && typeof this.proxyServer.registerSharedServer === 'function') {
      //   await this.proxyServer.registerSharedServer(key, serverInstance);
      //   logging.info(`服务器 ${key} 已注册到本地代理`);
      // }
  
      // 将服务器实例注册到WebSocket代理（不创建新实例，只是引用）
      if (this.wsProxyServer && typeof this.wsProxyServer.registerSharedServer === 'function') {
        await this.wsProxyServer.registerSharedServer(key, serverInstance);
        logging.info(`服务器 ${key} 已注册到WebSocket代理`);
      }
  
      this.loadedServers.add(key); // 标记为已加载
    } catch (error) {
      logging.error(`添加服务器到共享池失败: ${key}`, error);
      throw error;
    }
  }

  // 在ProxyMCPClient类中添加静态方法
  static async globalCleanup(): Promise<void> {
    try {
      logging.info('开始全局清理...');
      const sharedPool = SharedMCPServerPool.getInstance();
      await sharedPool.cleanup();
      logging.info('全局清理完成');
    } catch (error) {
      logging.error('全局清理失败:', error);
    }
  }
  
  // 修改disconnect方法
  async disconnect(): Promise<void> {
    logging.info(`开始断开代理连接，当前连接状态: ${this.isConnected}`);
    
    // 禁用WebSocket重连
    this.disableReconnect();
    
    // 断开WebSocket传输
    if (this.wsTransport) {
      try {
        logging.info('断开WebSocket传输...');
        await this.wsTransport.close();
        logging.info('✅ WebSocket传输已断开');
      } catch (error) {
        logging.error('断开WebSocket传输失败:', error);
      }
    }
    
    // 清理WebSocket代理服务器
    if (this.wsProxyCleanup) {
      try {
        logging.info('清理WebSocket代理服务器...');
        await this.wsProxyCleanup();
        logging.info('✅ WebSocket代理服务器已清理');
      } catch (error) {
        logging.error('清理WebSocket代理服务器失败:', error);
      }
    }
    
    // 清理本地代理服务器
    if (this.proxyCleanup) {
      try {
        logging.info('清理本地代理服务器...');
        await this.proxyCleanup();
        logging.info('✅ 本地代理服务器已清理');
      } catch (error) {
        logging.error('清理本地代理服务器失败:', error);
      }
    }
    
    // 清理共享服务器池
    try {
      logging.info('清理共享服务器池...');
      const sharedPool = SharedMCPServerPool.getInstance();
      await sharedPool.cleanup();
      logging.info('✅ 共享服务器池已清理');
    } catch (error) {
      logging.error('清理共享服务器池失败:', error);
    }
    
    // 重置状态
    this.isConnected = false;
    this.loadedServers.clear();
    this.wsTransport = null;
    this.wsProxyServer = null;
    this.wsProxyCleanup = null;
    this.proxyServer = null;
    this.proxyCleanup = null;
    
    logging.info('✅ 代理连接已断开');
  }

  /**
   * 启用WebSocket重连机制
   */
  public enableReconnect(): void {
    if (this.wsTransport) {
      this.wsTransport.enableReconnectAttempts();
      logging.info('✅ 已启用WebSocket重连机制');
    } else {
      logging.warn('WebSocket传输不存在，无法启用重连机制');
    }
  }

  /**
   * 禁用WebSocket重连机制
   */
  public disableReconnect(): void {
    if (this.wsTransport) {
      this.wsTransport.disableReconnectAttempts();
      logging.info('✅ 已禁用WebSocket重连机制');
    } else {
      logging.warn('WebSocket传输不存在，无法禁用重连机制');
    }
  }

  /**
   * 只重新连接WebSocket部分，保持本地连接不变
   * @param servers 服务器配置
   * @param websocketUrl WebSocket URL
   */
  public async reconnectWebSocketOnly(servers: Record<string, MCPServer>, websocketUrl: string): Promise<void> {
    try {
      logging.info('开始重新连接WebSocket部分...');
      
      // 禁用现有WebSocket重连机制
      this.disableReconnect();
      
      // 断开现有WebSocket传输
      if (this.wsTransport) {
        try {
          logging.info('断开现有WebSocket传输...');
          await this.wsTransport.close();
          logging.info('✅ 现有WebSocket传输已断开');
        } catch (error) {
          logging.error('断开现有WebSocket传输失败:', error);
        }
      }
      
      // 清理现有WebSocket代理服务器
      if (this.wsProxyCleanup) {
        try {
          logging.info('清理现有WebSocket代理服务器...');
          await this.wsProxyCleanup();
          logging.info('✅ 现有WebSocket代理服务器已清理');
        } catch (error) {
          logging.error('清理现有WebSocket代理服务器失败:', error);
        }
      }
      
      // 重新设置WebSocket桥接
      logging.info('重新设置WebSocket桥接...');
      await this.setupWebSocketBridge(websocketUrl);
      
      // 重新加载服务器到WebSocket代理
      logging.info('重新加载服务器到WebSocket代理...');
      await this.loadServersToAllProxies(servers);
      
      // 重新启用WebSocket重连机制
      logging.info('重新启用WebSocket重连机制...');
      this.enableReconnect();
      
      logging.info('✅ WebSocket部分重连成功');
    } catch (error) {
      logging.error('WebSocket部分重连失败:', error);
      // 不抛出错误，因为本地连接仍然可用
    }
  }

  async addSingleServer(key: string, server: MCPServer): Promise<void> {
    // 添加到本地代理
    if (this.proxyServer && typeof this.proxyServer.addSingleServer === 'function') {
      try {
        await this.proxyServer.addSingleServer(key, server);
        logging.info(`本地代理服务器添加服务器成功: ${key}`);
      } catch (error) {
        logging.error(`本地代理服务器添加服务器失败: ${key}`, error);
        throw error;
      }
    }
    
    // 添加到WebSocket代理（如果存在）
    if (this.wsProxyServer && typeof this.wsProxyServer.addSingleServer === 'function') {
      try {
        await this.wsProxyServer.addSingleServer(key, server);
        logging.info(`WebSocket代理服务器添加服务器成功: ${key}`);
      } catch (error) {
        logging.error(`WebSocket代理服务器添加服务器失败: ${key}`, error);
        // WebSocket添加失败不影响本地代理
      }
    }
  }

  async removeSingleServer(key: string): Promise<void> {
    try {
      logging.info(`开始移除服务器: ${key}`);
      
      // 从本地代理注销
      if (this.proxyServer && typeof this.proxyServer.unregisterSharedServer === 'function') {
        await this.proxyServer.unregisterSharedServer(key);
      }
      
      // 从WebSocket代理注销
      if (this.wsProxyServer && typeof this.wsProxyServer.unregisterSharedServer === 'function') {
        await this.wsProxyServer.unregisterSharedServer(key);
      }
      
      // 从共享池移除（这会实际清理服务器实例）
      const sharedPool = SharedMCPServerPool.getInstance();
      await sharedPool.removeServer(key);
      
      // 从已加载列表移除
      this.loadedServers.delete(key);
      
      logging.info(`服务器 ${key} 已完全移除`);
    } catch (error) {
      logging.error(`移除服务器失败: ${key}`, error);
      throw error;
    }
  }

  // 恢复本地技能调用方法
  async listTools() {
    if (!this.localClient) {
      logging.warn('本地客户端未连接，无法列出技能');
      return [];
    }

    try {
      const result = await this.localClient.listTools();
      return result.tools || [];
    } catch (error) {
      logging.error('列出技能失败:', error);
      return [];
    }
  }

  async callTool(name: string, args: any) {
    if (!this.localClient) {
      throw new Error('本地客户端未连接，无法调用技能');
    }

    try {
      return await this.localClient.callTool({ name, arguments: args });
    } catch (error) {
      logging.error('调用技能失败:', error);
      throw error;
    }
  }

  getConnectionStatus() {
    return this.isConnected;
  }
}

// 导出单例实例
export const proxyMCPClient = new ProxyMCPClient();

// 在文件顶部添加共享服务器池类
class SharedMCPServerPool {
  private static instance: SharedMCPServerPool;
  private serverInstances: Map<string, ConnectedClient> = new Map();
  private serverConfigs: Map<string, MCPServer> = new Map();

  static getInstance(): SharedMCPServerPool {
    if (!SharedMCPServerPool.instance) {
      SharedMCPServerPool.instance = new SharedMCPServerPool();
    }
    return SharedMCPServerPool.instance;
  }

  async getOrCreateServer(key: string, serverConfig: MCPServer): Promise<ConnectedClient> {
    // 如果服务器已存在，直接返回
    if (this.serverInstances.has(key)) {
      logging.info(`复用现有服务器实例: ${key}`);
      return this.serverInstances.get(key)!;
    }

    // 创建新的服务器实例
    try {
      const singleServerMap = { [key]: serverConfig };
      const singleClientArray = await createClients(singleServerMap);
      const client = singleClientArray.length > 0 ? singleClientArray[0] : null;
      
      if (client) {
        this.serverInstances.set(key, client);
        this.serverConfigs.set(key, serverConfig);
        logging.info(`服务器实例创建成功: ${key}`);
        return client;
      } else {
        throw new Error(`无法创建服务器实例: ${key}`);
      }
    } catch (error) {
      logging.error(`创建单个客户端失败 ${key}:`, error);
      throw error;
    }
  }

  async removeServer(key: string): Promise<void> {
    const client = this.serverInstances.get(key);
    if (client) {
      logging.info(`移除服务器实例: ${key}`);
      await client.cleanup();
      this.serverInstances.delete(key);
      this.serverConfigs.delete(key);
    }
  }

  getServer(key: string): ConnectedClient | undefined {
    return this.serverInstances.get(key);
  }

  getAllServers(): ConnectedClient[] {
    return Array.from(this.serverInstances.values());
  }

  async cleanup(): Promise<void> {
    logging.info('清理共享服务器池...');
    for (const [key, client] of this.serverInstances) {
      try {
        await client.cleanup();
      } catch (error) {
        logging.error(`清理服务器 ${key} 失败:`, error);
      }
    }
    this.serverInstances.clear();
    this.serverConfigs.clear();
  }
}
