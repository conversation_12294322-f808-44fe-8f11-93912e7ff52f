import fs from 'fs';
import path from 'path';
import { app } from 'electron';
import * as util from 'util';
import { exec } from 'child_process';
import { USER_DATA_PATH } from '../common';
import { FileInfo } from '../../shared/types/file';

const execPromise = util.promisify(exec);

/**
 * 文件管理服务
 * 负责文件的搜索、打开、读取等操作
 */
class FileManager {
  private recentFiles: FileInfo[] = [];
  private fileCache: Map<string, FileInfo> = new Map();
  private maxRecentFiles = 50; // 最近文件的最大数量
  private isScanning = false;
  private cacheDir: string;
  private cacheFilePath: string;
  
  constructor() {
    this.scanFiles = this.scanFiles.bind(this);
    this.searchFiles = this.searchFiles.bind(this);
    this.getRecentFiles = this.getRecentFiles.bind(this);
    this.updateFileUsage = this.updateFileUsage.bind(this);
    this.loadRecentFiles = this.loadRecentFiles.bind(this);
    this.saveRecentFiles = this.saveRecentFiles.bind(this);
    this.readFilePreview = this.readFilePreview.bind(this);
    
    // 初始化缓存路径
    this.cacheDir = path.join(USER_DATA_PATH, 'cache');
    this.cacheFilePath = path.join(this.cacheDir, 'recent-files.json');
    
    // 确保缓存目录存在
    if (!fs.existsSync(this.cacheDir)) {
      fs.mkdirSync(this.cacheDir, { recursive: true });
    }
    
    // 初始化时加载最近文件
    this.loadRecentFiles();
  }
  
  // 获取最近打开的文件
  public getRecentFiles(): FileInfo[] {
    // 按照最后打开时间倒序排序
    return this.recentFiles.sort((a, b) => {
      return (b.lastOpened || 0) - (a.lastOpened || 0);
    });
  }
  
  // 搜索文件
  public async searchFiles(query: string, directories?: string[]): Promise<FileInfo[]> {
    if (!query) return this.getRecentFiles().slice(0, 20);
    
    if (this.isScanning) {
      return [];
    }
    
    this.isScanning = true;
    
    try {
      let searchDirs = directories || [];
      
      // 如果没有指定目录，搜索默认目录
      if (searchDirs.length === 0) {
        // 避免直接搜索整个home目录，而是搜索其中常用的子目录
        const homeDir = app.getPath('home');
        searchDirs = [
          path.join(homeDir, 'Documents'),
          path.join(homeDir, 'Downloads'),
          path.join(homeDir, 'Desktop'),
          path.join(homeDir, 'Pictures'),
          app.getPath('documents'),
          app.getPath('downloads'),
          app.getPath('desktop')
        ];
      }
      
      // 执行搜索
      const results: FileInfo[] = [];
      
      // 根据系统选择不同的搜索方法
      if (process.platform === 'darwin') {
        await this.searchMacFiles(query, searchDirs, results);
      } else if (process.platform === 'win32') {
        await this.searchWindowsFiles(query, searchDirs, results);
      } else if (process.platform === 'linux') {
        await this.searchLinuxFiles(query, searchDirs, results);
      }
      
      // 最多返回100个结果
      return results.slice(0, 100);
    } catch (error) {
      console.error('搜索文件出错:', error);
      return [];
    } finally {
      this.isScanning = false;
    }
  }
  
  // 读取文件预览内容
  public async readFilePreview(filePath: string): Promise<string> {
    try {
      if (!fs.existsSync(filePath)) {
        return '文件不存在';
      }
      
      const stats = fs.statSync(filePath);
      
      // 如果文件过大，只读取前后部分
      const MAX_FILE_SIZE = 1024 * 100; // 100KB
      
      if (stats.size > MAX_FILE_SIZE) {
        const buffer = Buffer.alloc(MAX_FILE_SIZE);
        const fd = fs.openSync(filePath, 'r');
        
        // 读取前半部分
        fs.readSync(fd, buffer, 0, MAX_FILE_SIZE / 2, 0);
        
        // 读取后半部分
        fs.readSync(fd, buffer, MAX_FILE_SIZE / 2, MAX_FILE_SIZE / 2, stats.size - (MAX_FILE_SIZE / 2));
        
        fs.closeSync(fd);
        
        return buffer.toString('utf8', 0, MAX_FILE_SIZE / 2) +
               '\n\n... 文件内容过大，已省略中间部分 ...\n\n' +
               buffer.toString('utf8', MAX_FILE_SIZE / 2, MAX_FILE_SIZE);
      } else {
        // 直接读取整个文件
        return fs.readFileSync(filePath, 'utf8');
      }
    } catch (error) {
      console.error(`读取文件预览出错: ${filePath}`, error);
      return '无法读取文件内容';
    }
  }
  
  // 更新文件使用时间
  public updateFileUsage(filePath: string): void {
    const now = Date.now();
    
    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      return;
    }
    
    // 获取文件信息
    const stat = fs.statSync(filePath);
    const fileExt = path.extname(filePath).toLowerCase();
    const fileName = path.basename(filePath);
    
    // 创建或更新文件信息
    const fileInfo: FileInfo = {
      id: `file-${filePath}`,
      name: fileName,
      path: filePath,
      size: stat.size,
      mtime: stat.mtimeMs,
      extension: fileExt,
      isDirectory: stat.isDirectory(),
      lastOpened: now
    };
    
    // 更新缓存
    this.fileCache.set(filePath, fileInfo);
    
    // 更新最近文件列表
    const existingIndex = this.recentFiles.findIndex(f => f.path === filePath);
    if (existingIndex !== -1) {
      this.recentFiles[existingIndex] = fileInfo;
    } else {
      this.recentFiles.push(fileInfo);
      
      // 限制最近文件数量
      if (this.recentFiles.length > this.maxRecentFiles) {
        this.recentFiles = this.recentFiles.sort((a, b) => {
          return (b.lastOpened || 0) - (a.lastOpened || 0);
        }).slice(0, this.maxRecentFiles);
      }
    }
    
    // 保存最近文件列表
    this.saveRecentFiles();
  }
  
  // 扫描文件（用于初始化）
  public async scanFiles(directories?: string[]): Promise<FileInfo[]> {
    // 暂不实现完整扫描，只返回最近文件
    return this.getRecentFiles();
  }
  
  // 检查文件是否存在
  public async checkFileExists(filePath: string): Promise<boolean> {
    try {
      return fs.existsSync(filePath);
    } catch (error) {
      console.error('检查文件存在性出错:', error);
      return false;
    }
  }
  
  // 检查文件是否可访问（包括权限检查）
  private async isFileAccessible(filePath: string): Promise<{ accessible: boolean; error?: string }> {
    try {
      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        return { accessible: false, error: '文件不存在' };
      }
      
      // 检查文件访问权限
      await fs.promises.access(filePath, fs.constants.F_OK | fs.constants.R_OK);
      
      return { accessible: true };
    } catch (error: any) {
      let errorMsg = '文件访问权限错误';
      if (error.code === 'ENOENT') {
        errorMsg = '文件不存在';
      } else if (error.code === 'EACCES') {
        errorMsg = '文件访问权限不足';
      } else if (error.code === 'EPERM') {
        errorMsg = '操作不被允许';
      }
      
      return { accessible: false, error: `${errorMsg}: ${error.message || error}` };
    }
  }
  
  // 打开文件
  public async openFile(filePath: string): Promise<boolean> {
    try {
      console.log(`=== 开始处理文件打开请求 ===`);
      console.log(`请求路径: ${filePath}`);
      console.log(`当前平台: ${process.platform}`);
      
      // 检查文件可访问性
      const accessCheck = await this.isFileAccessible(filePath);
      if (!accessCheck.accessible) {
        console.error(`打开文件失败: ${accessCheck.error}`);
        return false;
      }
      console.log(`路径可访问性检查通过`);

      // 更新使用时间
      this.updateFileUsage(filePath);

      // 检查路径是否为目录
      const stat = await fs.promises.stat(filePath);
      const isDirectory = stat.isDirectory();
      console.log(`路径类型检查: ${isDirectory ? '目录' : '文件'}`);

      // 根据系统选择不同的打开方法
      if (process.platform === 'darwin') {
        try {
          // macOS: 使用 open 命令，支持各种文件类型
          const escapedPath = filePath.replace(/'/g, "'\"'\"'");
          
          let command: string;
          if (isDirectory) {
            // 对于目录，使用更强制性的AppleScript来确保Finder窗口在最前面
            const escapedPathForAppleScript = filePath.replace(/\\/g, '\\\\').replace(/"/g, '\\"');
            // 组合命令：激活Finder -> 打开目录 -> 强制将Finder窗口置于前台 -> 设置为活动应用
            command = `osascript -e 'tell application "Finder"' -e 'activate' -e 'open folder POSIX file "${escapedPathForAppleScript}"' -e 'set frontmost to true' -e 'activate' -e 'end tell' && sleep 0.1 && osascript -e 'tell application "System Events" to set frontmost of process "Finder" to true'`;
            console.log(`=== 目录处理逻辑（使用强制前台AppleScript）===`);
            console.log(`原始路径: ${filePath}`);
            console.log(`AppleScript转义路径: ${escapedPathForAppleScript}`);
            console.log(`完整命令: ${command}`);
          } else {
            // 对于文件，使用原来的逻辑
            command = `open '${escapedPath}'`;
            console.log(`=== 文件处理逻辑 ===`);
            console.log(`转义后路径: ${escapedPath}`);
          }
          
          console.log(`macOS: 即将执行命令: ${command}`);
          
          const result = await execPromise(command);
          console.log(`命令执行结果:`, result);
          console.log(`macOS: 成功打开${isDirectory ? '目录' : '文件'}: ${filePath}`);
          return true;
        } catch (error: any) {
          console.error(`macOS: 无法打开${isDirectory ? '目录' : '文件'}: ${error.message || error}`);
          console.error(`完整错误信息:`, error);
          return false;
        }
      } else if (process.platform === 'win32') {
        try {
          // Windows: 使用 start 命令
          const normalizedPath = filePath.replace(/\//g, '\\');
          const command = `start "" "${normalizedPath}"`;
          console.log(`Windows: 执行命令: ${command}`);
          
          await execPromise(command);
          console.log(`Windows: 成功打开${isDirectory ? '目录' : '文件'}: ${filePath}`);
          return true;
        } catch (error: any) {
          console.error(`Windows: 无法打开${isDirectory ? '目录' : '文件'}: ${error.message || error}`);
          return false;
        }
      } else if (process.platform === 'linux') {
        try {
          // Linux: 首先尝试 xdg-open (最通用的方法)
          const escapedPath = filePath.replace(/'/g, "'\"'\"'");
          const command = `xdg-open '${escapedPath}'`;
          console.log(`Linux: 执行命令: ${command}`);
          
          await execPromise(command);
          console.log(`Linux: 成功打开${isDirectory ? '目录' : '文件'}: ${filePath}`);
          return true;
        } catch (error: any) {
          console.error(`Linux: xdg-open 失败，尝试其他方法: ${error.message || error}`);
          
          // 回退到其他可能的打开方式
          const fallbackCommands = [
            `gnome-open '${filePath.replace(/'/g, "'\"'\"'")}'`,
            `kde-open '${filePath.replace(/'/g, "'\"'\"'")}'`,
            `exo-open '${filePath.replace(/'/g, "'\"'\"'")}'`
          ];
          
          for (const fallbackCmd of fallbackCommands) {
            try {
              console.log(`Linux: 尝试回退命令: ${fallbackCmd}`);
              await execPromise(fallbackCmd);
              console.log(`Linux: 回退命令成功打开${isDirectory ? '目录' : '文件'}: ${filePath}`);
              return true;
            } catch (fallbackError: any) {
              console.log(`Linux: 回退命令失败: ${fallbackError.message || fallbackError}`);
              continue;
            }
          }
          
          console.error(`Linux: 所有打开${isDirectory ? '目录' : '文件'}的方法都失败了`);
          return false;
        }
      }

      return false;
    } catch (error: any) {
      console.error(`打开文件 ${filePath} 出错:`, error.message || error);
      return false;
    }
  }
  
  // 打开文件所在目录
  public async openFileLocation(filePath: string): Promise<boolean> {
    try {
      // 检查文件可访问性
      const accessCheck = await this.isFileAccessible(filePath);
      if (!accessCheck.accessible) {
        console.error(`定位文件失败: ${accessCheck.error}`);
        return false;
      }

      console.log(`准备定位文件: ${filePath}`);
      console.log(`运行平台: ${process.platform}`);

      // 根据系统选择不同的打开方法
      if (process.platform === 'darwin') {
        // macOS: 使用 open -R 来在Finder中显示并选中文件
        try {
          // 确保路径被正确转义
          const escapedPath = filePath.replace(/'/g, "'\"'\"'");
          // 使用更强制性的组合命令来确保Finder窗口在前台显示
          const command = `open -R '${escapedPath}' && sleep 0.1 && osascript -e 'tell application "Finder"' -e 'activate' -e 'set frontmost to true' -e 'end tell' && osascript -e 'tell application "System Events" to set frontmost of process "Finder" to true'`;
          console.log(`macOS: 执行命令: ${command}`);
          
          const result = await execPromise(command);
          console.log(`命令执行结果:`, result);
          console.log(`macOS: 成功在Finder中显示并选中文件: ${filePath}`);
          return true;
        } catch (error: any) {
          console.error(`macOS: 无法在Finder中显示文件: ${error.message || error}`);
          console.error(`macOS: 错误详情:`, error);
          
          // 回退到打开目录
          try {
            const dirPath = path.dirname(filePath);
            const escapedDirPath = dirPath.replace(/'/g, "'\"'\"'");
            const fallbackCommand = `open '${escapedDirPath}'`;
            console.log(`macOS: 回退命令: ${fallbackCommand}`);
            
            await execPromise(fallbackCommand);
            console.log(`macOS: 回退成功，打开目录: ${dirPath}`);
            return true;
          } catch (fallbackError: any) {
            console.error(`macOS: 回退也失败: ${fallbackError.message || fallbackError}`);
            return false;
          }
        }
      } else if (process.platform === 'win32') {
        // Windows: 使用 explorer /select 来在资源管理器中显示并选中文件
        try {
          // Windows路径处理：确保使用反斜杠并正确转义
          const normalizedPath = filePath.replace(/\//g, '\\');
          const command = `explorer /select,"${normalizedPath}"`;
          console.log(`Windows: 执行命令: ${command}`);
          
          const { stdout, stderr } = await execPromise(command);
          
          if (stderr) {
            console.error(`Windows: 命令执行有警告: ${stderr}`);
          }
          if (stdout) {
            console.log(`Windows: 命令执行输出: ${stdout}`);
          }
          
          console.log(`Windows: 成功在资源管理器中显示并选中文件: ${filePath}`);
          return true;
        } catch (error: any) {
          console.error(`Windows: 无法在资源管理器中显示文件: ${error.message || error}`);
          // Windows回退方案：打开包含目录
          try {
            const dirPath = path.dirname(filePath).replace(/\//g, '\\');
            const fallbackCommand = `explorer "${dirPath}"`;
            console.log(`Windows: 回退命令: ${fallbackCommand}`);
            
            await execPromise(fallbackCommand);
            console.log(`Windows: 回退成功，打开目录: ${dirPath}`);
            return true;
          } catch (fallbackError: any) {
            console.error(`Windows: 回退也失败: ${fallbackError.message || fallbackError}`);
            return false;
          }
        }
      } else if (process.platform === 'linux') {
        // Linux: 尝试使用不同的文件管理器来显示并选中文件
        const escapedPath = filePath.replace(/'/g, "'\"'\"'");
        const fileManagers = [
          // GNOME Nautilus - 支持 --select
          { cmd: 'nautilus', args: `--select '${escapedPath}'`, name: 'Nautilus (GNOME)' },
          // KDE Dolphin - 支持 --select  
          { cmd: 'dolphin', args: `--select '${escapedPath}'`, name: 'Dolphin (KDE)' },
          // Thunar - 不支持--select，但可以打开包含目录
          { cmd: 'thunar', args: `'${path.dirname(escapedPath)}'`, name: 'Thunar (XFCE)' },
          // Nemo - 支持文件路径，会自动选中
          { cmd: 'nemo', args: `'${escapedPath}'`, name: 'Nemo (Cinnamon)' },
          // PCManFM - 不支持选中，打开包含目录
          { cmd: 'pcmanfm', args: `'${path.dirname(escapedPath)}'`, name: 'PCManFM (LXDE)' },
          // 通用文件管理器尝试
          { cmd: 'xdg-open', args: `'${path.dirname(escapedPath)}'`, name: 'XDG Default' }
        ];

        // 尝试每个文件管理器
        for (const fm of fileManagers) {
          try {
            // 首先检查命令是否存在
            await execPromise(`which ${fm.cmd}`);
            
            const command = `${fm.cmd} ${fm.args}`;
            console.log(`Linux: 尝试 ${fm.name}，命令: ${command}`);
            
            const { stdout, stderr } = await execPromise(command);
            
            if (stderr && !stderr.includes('Warning')) { // 忽略警告信息
              console.log(`Linux: ${fm.name} 有错误: ${stderr}`);
              continue;
            }
            if (stdout) {
              console.log(`Linux: ${fm.name} 输出: ${stdout}`);
            }
            
            console.log(`Linux: 成功使用 ${fm.name} 显示文件: ${filePath}`);
            return true;
          } catch (error: any) {
            console.log(`Linux: ${fm.name} 不可用或执行失败: ${error.message || error}`);
            continue;
          }
        }

        // 如果所有文件管理器都失败，最后尝试系统默认方式
        console.log(`Linux: 所有专用文件管理器都不可用，使用系统默认方式打开目录`);
        try {
          const dirPath = path.dirname(filePath);
          const command = `xdg-open '${dirPath.replace(/'/g, "'\"'\"'")}'`;
          console.log(`Linux: 最终回退命令: ${command}`);
          
          await execPromise(command);
          console.log(`Linux: 成功打开目录: ${dirPath}`);
          return true;
        } catch (error: any) {
          console.error(`Linux: 最终回退也失败: ${error.message || error}`);
          return false;
        }
      }

      return false;
    } catch (error: any) {
      console.error(`打开文件所在目录出错 ${filePath}:`, error.message || error);
      return false;
    }
  }
  
  // 保存最近文件列表到磁盘
  private saveRecentFiles(): void {
    try {
      const data = JSON.stringify(this.recentFiles.slice(0, this.maxRecentFiles));
      fs.writeFileSync(this.cacheFilePath, data);
    } catch (error) {
      console.error('保存最近文件列表出错:', error);
    }
  }
  
  // 从磁盘加载最近文件列表
  private loadRecentFiles(): void {
    try {
      if (fs.existsSync(this.cacheFilePath)) {
        const data = fs.readFileSync(this.cacheFilePath, 'utf8');
        const files = JSON.parse(data) as FileInfo[];
        
        // 过滤掉不存在的文件
        this.recentFiles = files.filter(file => {
          try {
            return fs.existsSync(file.path);
          } catch (error) {
            return false;
          }
        });
        
        // 更新缓存
        this.recentFiles.forEach(file => {
          this.fileCache.set(file.path, file);
        });
      }
    } catch (error) {
      console.error('加载最近文件列表出错:', error);
      this.recentFiles = [];
    }
  }
  
  // macOS 特定的文件搜索方法
  private async searchMacFiles(query: string, directories: string[], results: FileInfo[]): Promise<void> {
    try {
      // 尝试使用mdfind命令进行搜索
      let command = `mdfind -onlyin "${directories[0]}" "${query}"`;
      if (directories.length > 1) {
        command = `mdfind -name "${query}"`;
      }
      
      const { stdout, stderr } = await execPromise(command);
      
      if (stderr) {
        console.error('macOS搜索出错:', stderr);
        throw new Error(stderr);
      }
      
      // 解析结果
      const filePaths = stdout.split('\n').filter(Boolean);
      
      // 只处理前100个结果以避免性能问题
      const filePathsToProcess = filePaths.slice(0, 100);
      
      // 为每个文件创建FileInfo对象
      for (const filePath of filePathsToProcess) {
        try {
          const stat = fs.statSync(filePath);
          
          // 忽略隐藏文件和系统文件
          if (this.isTemporaryFile(path.basename(filePath))) {
            continue;
          }
          
          // 创建文件信息对象
          const fileInfo = this.createFileInfo(filePath, stat);
          
          // 如果文件已在缓存中，使用缓存中的lastOpened
          if (this.fileCache.has(filePath)) {
            const cachedFile = this.fileCache.get(filePath);
            if (cachedFile) {
              fileInfo.lastOpened = cachedFile.lastOpened;
            }
          }
          
          results.push(fileInfo);
        } catch (error) {
          // 忽略无法访问的文件
          continue;
        }
      }
    } catch (error) {
      // 如果mdfind命令失败，回退到递归搜索
      for (const dir of directories) {
        if (fs.existsSync(dir)) {
          try {
            this.searchDirectoryRecursively(dir, query, results);
          } catch (err) {
            console.error(`目录 ${dir} 搜索出错:`, err);
          }
        }
      }
    }
  }
  
  // 为文件创建FileInfo对象
  private createFileInfo(filePath: string, stat: fs.Stats): FileInfo {
    const fileName = path.basename(filePath);
    const fileExt = path.extname(filePath).toLowerCase();
    
    return {
      id: `file-${filePath}`,
      name: fileName,
      path: filePath,
      size: stat.size,
      mtime: stat.mtimeMs,
      extension: fileExt,
      isDirectory: stat.isDirectory(),
      lastOpened: Date.now()
    };
  }
  
  // 递归搜索目录中的文件
  private searchDirectoryRecursively(
    dirPath: string, 
    query: string, 
    results: FileInfo[], 
    depth = 0, 
    maxDepth = 2
  ): void {
    // 限制搜索深度
    if (depth > maxDepth || results.length >= 100) {
      return;
    }
    
    try {
      const entries = fs.readdirSync(dirPath);
      const lowerQuery = query.toLowerCase();
      
      for (const entry of entries) {
        if (this.isTemporaryFile(entry)) {
          continue;
        }
        
        const fullPath = path.join(dirPath, entry);
        
        try {
          const stat = fs.statSync(fullPath);
          
          // 检查文件名是否匹配查询
          if (entry.toLowerCase().includes(lowerQuery)) {
            const fileInfo = this.createFileInfo(
              fullPath, 
              stat
            );
            
            // 如果文件已在缓存中，使用缓存中的lastOpened
            if (this.fileCache.has(fullPath)) {
              const cachedFile = this.fileCache.get(fullPath);
              if (cachedFile) {
                fileInfo.lastOpened = cachedFile.lastOpened;
              }
            }
            
            results.push(fileInfo);
          }
          
          // 如果是目录，递归搜索
          if (stat.isDirectory()) {
            this.searchDirectoryRecursively(fullPath, query, results, depth + 1, maxDepth);
          }
        } catch (error) {
          // 忽略无法访问的文件或目录
          continue;
        }
      }
    } catch (error) {
      // 忽略无法访问的目录
    }
  }
  
  // 判断文件是否为临时文件或系统文件
  private isTemporaryFile(fileName: string): boolean {
    // 隐藏文件（以.开头）
    if (fileName.startsWith('.')) {
      return true;
    }
    
    // 临时文件，常见的临时文件后缀
    const tempExtensions = ['.tmp', '.temp', '.swp', '.bak', '.~'];
    const extension = path.extname(fileName).toLowerCase();
    if (tempExtensions.includes(extension)) {
      return true;
    }
    
    // Mac特有的系统文件
    if (fileName === '.DS_Store' || fileName === '.localized') {
      return true;
    }
    
    return false;
  }
  
  // Windows 特定的文件搜索方法
  private async searchWindowsFiles(query: string, directories: string[], results: FileInfo[]): Promise<void> {
    // 使用递归搜索代替
    for (const dir of directories) {
      if (fs.existsSync(dir)) {
        this.searchDirectoryRecursively(dir, query, results);
      }
    }
  }
  
  // Linux 特定的文件搜索方法
  private async searchLinuxFiles(query: string, directories: string[], results: FileInfo[]): Promise<void> {
    try {
      // 尝试使用find命令
      for (const dir of directories) {
        try {
          if (!fs.existsSync(dir)) continue;
          
          // 使用find和grep组合进行名称搜索
          const command = `find "${dir}" -type f -name "*${query}*" -not -path "*/\\.*" -not -path "*/node_modules/*" -not -path "*/\\.git/*" | head -n 100`;
          
          const { stdout } = await execPromise(command);
          
          // 解析结果
          const filePaths = stdout.split('\n').filter(Boolean);
          
          // 为每个文件创建FileInfo对象
          for (const filePath of filePaths) {
            try {
              const stat = fs.statSync(filePath);
              
              // 创建文件信息对象
              const fileInfo = this.createFileInfo(filePath, stat);
              
              // 如果文件已在缓存中，使用缓存中的lastOpened
              if (this.fileCache.has(filePath)) {
                const cachedFile = this.fileCache.get(filePath);
                if (cachedFile) {
                  fileInfo.lastOpened = cachedFile.lastOpened;
                }
              }
              
              results.push(fileInfo);
            } catch (error) {
              // 忽略无法访问的文件
              continue;
            }
          }
        } catch (error) {
          // 如果find命令失败，使用递归搜索
          this.searchDirectoryRecursively(dir, query, results);
        }
      }
    } catch (error) {
      // 如果所有方法都失败，使用递归搜索
      for (const dir of directories) {
        if (fs.existsSync(dir)) {
          this.searchDirectoryRecursively(dir, query, results);
        }
      }
    }
  }
}

// 导出单例实例
export const fileService = new FileManager();
export { FileManager }; 