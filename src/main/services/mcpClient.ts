import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
import { StreamableHTTPClientTransport } from '@modelcontextprotocol/sdk/client/streamableHttp.js';
import { Transport } from '@modelcontextprotocol/sdk/shared/transport.js';
import { mcpService } from './mcpService';
import { MCPServer } from '../../shared/types/mcp';

const sleep = (time: number) => new Promise<void>(resolve => setTimeout(() => resolve(), time))
export interface ConnectedClient {
  key: string;
  client: Client;
  isAido: boolean;
  cleanup: () => Promise<void>;
  name: string;
}

const createClient = async (key: string, server: MCPServer): Promise<{key: string, client: Client | undefined, transport: Transport | undefined }> => {

  let transport: Transport | null = null
  try {
    if (server.type === 'sse') {
      transport = new SSEClientTransport(new URL(server.baseUrl));
    } else if (server.type === 'stdio') {
      // 激活stdio类型的mcp server
      transport = await mcpService.activate(key, server);
    } else if (server.type === 'streamableHttp') {
      transport = new StreamableHTTPClientTransport(new URL(server.baseUrl));
    } else {
      throw new Error(`Invalid transport type: ${server.type}`)
    }
  } catch (error) {
    console.error(`Failed to create transport ${server.type || 'stdio'} to ${server.name}:`, error);
  }

  if (!transport) {
    console.warn(`Transport ${server.name} not available.`)
    return { key, transport: undefined, client: undefined }
  }

  const client = new Client({
    name: 'mcp-proxy-client',
    version: '1.0.0',
  }, {
    capabilities: {
      prompts: {},
      resources: { subscribe: true },
      tools: {}
    }
  });

  return { key, client, transport }
}

export const createClients = async (servers: Record<string, MCPServer>): Promise<ConnectedClient[]> => {
  const clients: ConnectedClient[] = [];

  // 并行连接所有servers
  const connectionPromises = Object.entries(servers).map(async ([key, server]) => {
    console.log(`Connecting to server: ${server.name}`);

    const waitFor = 2500;
    const retries = 3;
    let count = 0;
    let retry = true;

    while (retry) {
      // 每次重试都创建新的client和transport
      const result = await createClient(key, server);
      const client = result.client;
      const transport = result.transport;
      
      if (!client || !transport) {
        break;
      }

      try {
        await client.connect(transport);
        console.log(`Connected to server: ${server.name}`);

        // 连接成功后，将client注册到mcpService中
        (mcpService as any).clients = (mcpService as any).clients || {};
        (mcpService as any).clients[key] = client;

        return {
          key,
          client: client,
          name: server.name,
          isAido: server.isAido,
          cleanup: async () => {
            // 清理时也要从mcpService中移除
            if ((mcpService as any).clients && (mcpService as any).clients[key]) {
              delete (mcpService as any).clients[key];
            }
            await transport.close();
          }
        };

      } catch (error) {
        console.error(`Failed to connect to ${server.name}:`, error);
        count++;
        retry = (count < retries);
        
        if (retry) {
          // 清理失败的连接
          try {
            await client.close();
          } catch (cleanupError) {
            console.error(`Failed to close client:`, cleanupError);
          }
          try {
            await transport.close();
          } catch (cleanupError) {
            console.error(`Failed to close transport:`, cleanupError);
          }
          
          console.log(`Retry connection to ${server.name} in ${waitFor}ms (${count}/${retries})`);
          await sleep(waitFor);
        }
      }
    }

    return null; // 连接失败返回null
  });

  // 等待所有连接完成，过滤掉失败的连接
  const results = await Promise.allSettled(connectionPromises);
  
  results.forEach((result, index) => {
    if (result.status === 'fulfilled' && result.value) {
      clients.push(result.value); 
    } else {
      const serverEntries = Object.entries(servers);
      console.error(`Failed to connect to server: ${serverEntries[index][1].name}`);
    }
  });

  return clients;
};
