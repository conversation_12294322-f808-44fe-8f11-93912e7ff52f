import Store from 'electron-store';
import * as logging from '../logging';

class StoreService {
  private store: Store;

  constructor() {
    try {
      // 初始化 electron-store
      // 你可以通过传入 defaults 字段来设置初始化的默认值
      this.store = new Store<Record<string, unknown>>({
        defaults: {
        }
      });
      logging.info('Electron Store initialized at:', this.store.path);
    } catch (error) {
      logging.error('Failed to initialize electron-store:', error);
      // 如果初始化失败，可以提供一个备用方案或抛出错误
      // 这里我们允许 store 为 undefined，并在 get/set 中处理
      this.store = undefined as any; // 或者 new Store(); ？取决于错误处理策略
    }

  }

  /**
   * 获取存储的值
   * @param key 存储的键
   * @param defaultValue 如果键不存在时返回的默认值
   * @returns 存储的值或默认值
   */
  public get<T>(key: string, defaultValue?: T): T | undefined {
    if (!this.store) {
      logging.warn('Store not initialized, returning defaultValue for key:', key);
      return defaultValue;
    }
    try {
      return this.store.get(key, defaultValue) as T;
    } catch (error) {
      logging.error(`Error getting value for key "${key}":`, error);
      return defaultValue;
    }
  }

  /**
   * 设置存储的值
   * @param key 存储的键
   * @param value 要存储的值
   */
  public set<T>(key: string, value: T): void {
    if (!this.store) {
      logging.error('Store not initialized, cannot set value for key:', key);
      return;
    }
    try {
      this.store.set(key, value);
    } catch (error) {
      logging.error(`Error setting value for key "${key}":`, error);
    }
  }

  // 可以添加其他 electron-store 支持的方法，例如 delete, clear, has 等
  public delete(key: string): void {
    this.store?.delete(key);
  }

  // public clear(): void {
  //   this.store?.clear();
  // }
}

// 导出 StoreService 的单例实例
export const storeService = new StoreService();
