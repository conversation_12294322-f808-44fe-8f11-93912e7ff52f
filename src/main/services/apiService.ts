import { request } from '../../core/request/index';

// 根据UUID获取MCP技能列表
export const getAllProjectToolsList = async (uuid: string) => {
  return request.post(`/api/projectTools/getAllProjectToolsList?projectUUId=${uuid}`);
};

// 根据UUID获取MCP信息
export const findProjectByUUID = async (uuid: string) => {
  return request.post(`/api/projects/findProjectByUUID/${uuid}`);
};

// 验证token是否有效
export const verifyToken = async () => {
  return request.post('/api/jwt/verify');
};

// 获取mcp接入点
export const assignAgent = async () => {
  return request.get('/api/device/assignAgent', null);
};

