import { BrowserWindow } from 'electron';

// 通用缓存结构，按字段独立缓存
const fieldCache: Record<string, { value: any; time: number }> = {};
const CACHE_EXPIRE_TIME = 5 * 60 * 1000; // 5分钟缓存过期

/**
 * 通用：从渲染线程获取字段
 * @param field "token" | "computerId"
 */
async function getFieldFromRenderer(field: 'token' | 'computerId'): Promise<any> {
  try {
    const mainWindow = BrowserWindow.getAllWindows().find(win => !win.isDestroyed());
    if (!mainWindow) {
      console.warn('未找到可用的渲染窗口');
      return null;
    }

    if (field === 'token') {
      // 获取 localStorage 里的 token
      return await mainWindow.webContents.executeJavaScript(`
        (() => {
          try {
            const authData = localStorage.getItem('auth-storage');
            if (authData) {
              const parsed = JSON.parse(authData);
              const data = parsed.state || parsed;
              return data?.token || null;
            }
            return null;
          } catch (error) {
            console.error('获取token失败:', error);
            return null;
          }
        })()
      `);
    } else if (field === 'computerId') {
      // 获取 window.useSystemStore?.getState()?.systemInfo?.computerId
      return await mainWindow.webContents.executeJavaScript(`
        (() => {
          try {
            console.log('🔍 开始获取computerId...');
            console.log('window.useSystemStore 是否存在:', !!window.useSystemStore);

            const store = window.useSystemStore;
            if (store && typeof store.getState === 'function') {
              const state = store.getState();
              console.log('systemStore state:', JSON.stringify(state, null, 2));
              const computerId = state?.systemInfo?.computerId || null;
              console.log('获取到的computerId:', computerId);
              return computerId;
            } else {
              console.log('useSystemStore 不存在或不是函数');
              return null;
            }
          } catch (error) {
            console.error('获取computerId失败:', error);
            return null;
          }
        })()
      `);
    }
    return null;
  } catch (error) {
    console.error(`从渲染线程获取${field}失败:`, error);
    return null;
  }
}

/**
 * 通用：带缓存获取字段
 */
async function getFieldWithCache(field: 'token' | 'computerId'): Promise<any> {
  const now = Date.now();
  const cache = fieldCache[field];
  if (cache && (now - cache.time) < CACHE_EXPIRE_TIME) {
    return cache.value;
  }
  const value = await getFieldFromRenderer(field);
  fieldCache[field] = { value, time: now };
  return value;
}

/**
 * 清除某字段缓存
 */
export function clearFieldCache(field: 'token' | 'computerId'): void {
  delete fieldCache[field];
}

/**
 * 更新某字段缓存
 */
export function updateFieldCache(field: 'token' | 'computerId', value: any): void {
  fieldCache[field] = { value, time: Date.now() };
}

/**
 * 获取主进程 token
 */
export async function getMainProcessToken(): Promise<string | null> {
  return await getFieldWithCache('token');
}

/**
 * 获取主进程 computerId
 */
export async function getMainProcessComputerId(): Promise<string | null> {
  return await getFieldWithCache('computerId');
} 