import * as fs from 'fs';
import * as path from 'path';
import * as util from 'util';
import { exec } from 'child_process';
import { nativeImage, app } from 'electron';

const execPromise = util.promisify(exec);

class IconExtractor {
  // 提取macOS应用图标
  public async extractMacAppIcon(appPath: string): Promise<string | null> {
    if (!fs.existsSync(appPath)) {
      return null;
    }
    
    try {
      // 使用更可靠的方式：通过app.getFileIcon API获取图标
      const icon = await app.getFileIcon(appPath, { size: 'normal' });
      
      if (icon) {
        // 将图标调整到合适大小
        const resizedIcon = icon.resize({ width: 64, height: 64 });
        const dataUrl = resizedIcon.toDataURL();
        
        // 返回纯base64数据
        return dataUrl.split(',')[1];
      }
      
      // 如果无法获取图标，使用通用图标
      return this.getFallbackIconBase64();
    } catch (error) {
      console.error(`提取应用图标出错:`, error);
      return this.getFallbackIconBase64();
    }
  }
  
  // 获取通用应用图标的base64数据
  private getFallbackIconBase64(): string {
    // 内置的简单图标base64（已经去掉了data:URL前缀）
    return '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';
  }
  
  // 提取Windows应用图标
  public async extractWindowsAppIcon(appPath: string): Promise<string | null> {
    // 后续实现Windows应用图标提取逻辑
    return null;
  }
  
  // 提取Linux应用图标
  public async extractLinuxAppIcon(appPath: string): Promise<string | null> {
    // 后续实现Linux应用图标提取逻辑
    return null;
  }
  
  // 根据平台选择合适的图标提取方法
  public async extractAppIcon(appPath: string): Promise<string | null> {
    try {
      if (process.platform === 'darwin') {
        return await this.extractMacAppIcon(appPath);
      } else if (process.platform === 'win32') {
        return await this.extractWindowsAppIcon(appPath);
      } else if (process.platform === 'linux') {
        return await this.extractLinuxAppIcon(appPath);
      }
      
      return this.getFallbackIconBase64();
    } catch (error) {
      console.error('提取图标出错:', error);
      return this.getFallbackIconBase64();
    }
  }
}

// 导出单例实例
export const iconService = new IconExtractor(); 