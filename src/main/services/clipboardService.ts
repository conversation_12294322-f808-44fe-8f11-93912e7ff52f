import { clipboard, nativeImage, app, NativeImage } from 'electron';
import { EventEmitter } from 'events';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import * as crypto from 'crypto';
import { execSync, spawn } from 'child_process';
import { storeService } from './storeService';
import { dbService } from './dbService';
import type {
  ClipboardItem,
  ClipboardTextItem,
  ClipboardImageItem,
  ClipboardFileItem,
  ClipboardDirectoryItem
} from '../../shared/types/clipboard';
import { fileURLToPath } from 'url';
import { info, error as logError } from '../logging';
import { WindowManager } from '../windows/window-manager';
import { FocusManager } from '../utils/focusManager';
import { lookup as mimeLookup } from 'mime-types';
import { AppSettings } from '@shared/types/settings';
import { settingsService } from './settingsService';

// 存储键名（用于数据迁移）
const CLIPBOARD_HISTORY_KEY = 'clipboard-history';

export class ClipboardManager extends EventEmitter {
  private lastTextContent = ''; // 保留以保持向后兼容性
  private lastChangeTime = 0;
  private initialized = false;
  private pauseMonitoring = false; // 暂停监控标志
  private debugMode = false; // 调试模式标志
  private lastFormats: string[] = []; // 保留以保持向后兼容性
  private lastImage: NativeImage | null = null; // 保留以保持向后兼容性
  private isProcessingChange = false; // 🔥 防止重复处理的标志
  private pollingTimer: NodeJS.Timeout | null = null; // 轮询定时器
  private lastClipboardHash = ''; // 上次剪切板内容的哈希 - 主要的重复检测机制
  private settings: AppSettings = settingsService.getSettings();
  private migrated = false; // 数据迁移标志

  // 获取最大历史记录数量（从配置中读取）
  private get maxHistorySize(): number {
    return this.settings.clipboard?.maxHistoryCount || 100;
  }

  constructor() {
    super();
    this.init();
  }

  // 🔥 完全重写初始化方法，使用事件驱动
  public init() {
    if (this.initialized) {
      info('[ClipboardService] 已初始化，跳过重复初始化');
      return;
    }

    if (app.isReady()) {
      this.migrateDataIfNeeded();
      this.startClipboardListener(); // 🔥 启动事件监听器
      this.initialized = true;
    } else {
      app.on('ready', () => {
        if (!this.initialized) {
          this.migrateDataIfNeeded();
          this.startClipboardListener(); // 🔥 启动事件监听器
          this.initialized = true;
        }
      });
    }
  }

  /**
   * 数据迁移：从storeService迁移到数据库
   */
  private migrateDataIfNeeded(): void {
    if (this.migrated) return;
    
    try {
      const storedHistory = storeService.get<ClipboardItem[]>(CLIPBOARD_HISTORY_KEY, []);
      if (Array.isArray(storedHistory) && storedHistory.length > 0) {
        info(`开始迁移剪贴板历史记录到数据库，共 ${storedHistory.length} 条记录`);
        
        // 验证并迁移数据
        const validHistory = this.validateHistoryData(storedHistory);
        let migratedCount = 0;
        
        for (const item of validHistory) {
          try {
            // 检查是否已存在
            const existing = dbService.getClipboardItem(item.id);
            if (!existing) {
              dbService.saveClipboardItem(item);
              migratedCount++;
            }
          } catch (error) {
            logError(`迁移剪贴板记录失败 (ID: ${item.id}):`, error);
          }
        }
        
        info(`成功迁移 ${migratedCount} 条剪贴板历史记录到数据库`);
        
        // 清理旧的存储数据
        storeService.delete(CLIPBOARD_HISTORY_KEY);
        info('已清理旧的剪贴板存储数据');
      }
      
      this.migrated = true;
    } catch (error) {
      logError('剪贴板数据迁移失败:', error);
      this.migrated = true; // 即使失败也标记为已迁移，避免重复尝试
    }
  }

  /**
   * 验证历史记录数据的完整性
   */
  private validateHistoryData(history: any[]): ClipboardItem[] {
    if (!Array.isArray(history)) {
      return [];
    }

    return history.filter(item => {
      // 基本结构验证
      if (!item || typeof item !== 'object') {
        return false;
      }

      // 必需字段验证
      if (!item.id || !item.type || !item.timestamp || !Array.isArray(item.items)) {
        return false;
      }

      // 支持的类型验证
      const validTypes = ['text', 'image', 'file', 'directory', 'multiple'];
      if (!validTypes.includes(item.type)) {
        return false;
      }

      // items数组不能为空
      if (item.items.length === 0) {
        return false;
      }

      return true;
    });
  }


 

  // 🔥 新的轮询模式剪贴板监听方法
  private startClipboardListener() {
    try {
      info('[ClipboardService] 🚀 启动轮询模式的剪贴板监听器');
      
      // 启动轮询监听器
      this.startPolling();
      
      info('[ClipboardService] ✅ 剪贴板监听器启动成功');
    } catch (error) {
      logError('[ClipboardService] ❌ 启动剪贴板监听器失败:', error);
      // 如果轮询启动失败，尝试重新启动
      this.fallbackToPolling();
    }
  }

  // 🔥 系统级剪贴板文件检测方法 (无状态副作用)
  private async detectSystemClipboardFile(itemId: string, timestamp: number): Promise<ClipboardItem | null> {
    try {
      // 1. 使用pbpaste检查文本内容
      const typesResult = execSync('pbpaste', { encoding: 'utf8', timeout: 2000 }).trim();
      
      // 2. 尝试获取文件路径列表
      const filePathsResult = execSync('osascript -e "set theClipboard to the clipboard as «class furl»" -e "return theClipboard"', { encoding: 'utf8', timeout: 2000 }).trim();

      // 3. 检查是否有文件路径
      if (filePathsResult && filePathsResult !== '') {
        let filePath = filePathsResult;
        if (filePath.startsWith('file://')) {
          try { filePath = fileURLToPath(filePath); } catch (e) { filePath = filePath.replace('file://', ''); }
        } else if (filePath.includes('Macintosh HD:') || filePath.includes(':')) {
          if (filePath.startsWith('file ')) { filePath = filePath.substring(5); }
          if (filePath.startsWith('Macintosh HD:')) { filePath = '/' + filePath.substring(13).replace(/:/g, '/'); }
          else if (filePath.includes(':')) { filePath = '/' + filePath.replace(/:/g, '/'); }
        }

        if (fs.existsSync(filePath)) {
          return await this.createFileItem(filePath, itemId, timestamp);
        }
      }
      
      // 4. 进一步尝试：typesResult 可能本身就是文件完整路径
      if (typesResult && typesResult.length > 0) {
        let possiblePath = typesResult;
        if (possiblePath.startsWith('~')) {
          possiblePath = path.join(os.homedir(), possiblePath.slice(1));
        }
          if (fs.existsSync(possiblePath)) {
          return await this.createFileItem(possiblePath, itemId, timestamp);
        }
      }
      
      // 5. 如果还没找到，尝试检测文件名（包括无扩展名的文件）
      if (typesResult && typesResult.length > 0) {
        const hasFileExtension = ['.mp4', '.avi', '.mov', '.mkv', '.mp3', '.wav', '.pdf', '.doc', '.docx', '.jpg', '.png', '.gif'].some(ext => typesResult.toLowerCase().includes(ext));
        const couldBeFileName = hasFileExtension || this.couldBeFileName(typesResult);

        if (couldBeFileName) {
          const fileName = typesResult.includes('/') ? typesResult.split('/').pop() : typesResult;
          const fileExt = fileName?.includes('.') ? fileName.split('.').pop()?.toLowerCase() : 'unknown';
          
          const fileItem: ClipboardFileItem = { name: fileName || '未知文件', content: `[应用文件] ${fileName || typesResult}`, type: fileExt || 'unknown', size: 0 };
          return { id: itemId, type: 'file', timestamp: timestamp, items: [fileItem], count: 1 };
        }
      }
    } catch (sysError) {
      // 静默失败
    }
    return null;
  }



  // 🔥 回退到轮询模式（仅在轮询启动失败时使用）
  private fallbackToPolling() {
    info('[ClipboardService] ⚠️ 回退到轮询模式');
    setTimeout(() => {
      try {
        this.startPolling();
        info('[ClipboardService] ✅ 回退轮询启动成功');
      } catch (error) {
        logError('[ClipboardService] ❌ 回退轮询启动失败:', error);
      }
    }, 2000); // 2秒后重试
  }



  // 分析剪贴板内容 - 根据调用方传入的类型进行处理，避免重复检测
  private async analyzeClipboardContent(_detectedType: 'text' | 'image' | 'file'): Promise<void> {
    // 在真正处理之前记录当前前台应用，确保source准确
    await FocusManager.rememberCurrentFocus();
    if (this.pauseMonitoring) {
      return;
    }

    const formats = clipboard.availableFormats();
    const currentText = clipboard.readText();

    let itemToAdd: ClipboardItem | null = null;
    const now = Date.now();
    const itemId = crypto.randomUUID();

    if (_detectedType === 'image') {
      info(`[ClipboardService] 检测到图片格式: ${JSON.stringify(formats.filter(f => f.includes('image') || f.includes('png') || f.includes('jpeg')))}，正在处理...`);
      const image = clipboard.readImage();
      const imageSize = image.getSize();
      info(`[ClipboardService] readImage result - isEmpty: ${image.isEmpty()}, size: ${JSON.stringify(imageSize)}, width: ${imageSize.width}, height: ${imageSize.height}`);
      
      if (!image.isEmpty()) {
        const dataURL = image.toDataURL();
        const lastDataURL = this.lastImage ? this.lastImage.toDataURL() : '';
        const dataURLPreview = dataURL.substring(0, 50) + '...';
        const lastDataURLPreview = lastDataURL.substring(0, 50) + '...';
        
        info(`[ClipboardService] ✅ 成功读取图片数据`);
        info(`[ClipboardService] 当前图片数据: ${dataURLPreview}`);
        info(`[ClipboardService] 上次图片数据: ${lastDataURLPreview}`);
        info(`[ClipboardService] 图片是否相同: ${dataURL === lastDataURL}`);

        if (dataURL !== lastDataURL) {
          info(`[ClipboardService] 创建新的图片项目，大小: ${dataURL.length}, 尺寸: ${imageSize.width}x${imageSize.height}`);
          const imageItem: ClipboardImageItem = { 
            dataURL: dataURL, 
            size: dataURL.length,
            width: imageSize.width,
            height: imageSize.height,
            name: `图片_${new Date().toISOString().replace(/[:.]/g, '-')}.png`,
            contentType: formats.find(f => f.startsWith('image/')) || 'image/png'
          };
          itemToAdd = {
            id: itemId,
            type: 'image',
            timestamp: now,
            items: [imageItem],
            count: 1,
            source: FocusManager.getPreviousFocusedApp() || undefined,
            contentType: imageItem.contentType,
          };
          // 图片项目创建成功
          info(`[ClipboardService] 图片项目创建成功`);
        } else {
          info(`[ClipboardService] 图片与上次相同，跳过处理`);
        }
      } else {
        info(`[ClipboardService] 读取到的图片为空`);
        // 尝试其他格式的图片数据
        try {
          info(`[ClipboardService] 所有可用格式: ${JSON.stringify(formats)}`);
          
          // 尝试读取所有格式的内容
          for (const format of formats) {
            try {
              info(`[ClipboardService] 尝试读取格式: ${format}`);
              
              if (format.startsWith('image/') || format.includes('image')) {
                const buffer = clipboard.readBuffer(format);
                info(`[ClipboardService] 格式 ${format} 缓冲区大小: ${buffer ? buffer.length : 'null'}`);
                
                if (buffer && buffer.length > 0) {
                  info(`[ClipboardService] 从格式 ${format} 读取到 ${buffer.length} 字节数据`);
                  
                  // 输出前几个字节来检查数据类型
                  const preview = Array.from(buffer.slice(0, 16)).map(b => b.toString(16).padStart(2, '0')).join(' ');
                  info(`[ClipboardService] 数据预览: ${preview}`);
                  
                  // 尝试创建图片
                  const nativeImg = nativeImage.createFromBuffer(buffer);
                  info(`[ClipboardService] 从缓冲区创建图片 - isEmpty: ${nativeImg.isEmpty()}, size: ${nativeImg.getSize()}`);
                  
                  if (!nativeImg.isEmpty()) {
                    const dataURL = nativeImg.toDataURL();
                    const lastDataURL = this.lastImage ? this.lastImage.toDataURL() : '';
                    
                    if (dataURL !== lastDataURL) {
                      info(`[ClipboardService] 从缓冲区创建图片成功`);
                      const imageItem: ClipboardImageItem = { 
                        dataURL: dataURL, 
                        size: dataURL.length,
                        width: nativeImg.getSize().width,
                        height: nativeImg.getSize().height,
                        name: `图片_${new Date().toISOString().replace(/[:.]/g, '-')}.png`,
                        contentType: formats.find(f => f.startsWith('image/')) || 'image/png'
                      };
                      itemToAdd = {
                        id: itemId,
                        type: 'image',
                        timestamp: now,
                        items: [imageItem],
                        count: 1,
                        source: FocusManager.getPreviousFocusedApp() || undefined,
                        contentType: imageItem.contentType,
                      };
                      break;
                    }
                  }
                } else {
                  info(`[ClipboardService] 格式 ${format} 无数据`);
                }
              } else {
                // 非图片格式，也尝试读取看看是否包含图片数据
                try {
                  const content = clipboard.read(format);
                  if (content) {
                    const contentStr = content.toString();
                    info(`[ClipboardService] 格式 ${format} 内容长度: ${contentStr.length}, 预览: ${contentStr.substring(0, 100)}`);
                    
                    // 检查是否是base64图片数据
                    if (contentStr.startsWith('data:image/') || contentStr.includes('base64')) {
                      info(`[ClipboardService] 格式 ${format} 可能包含base64图片数据`);
                      try {
                        const nativeImg = nativeImage.createFromDataURL(contentStr);
                        if (!nativeImg.isEmpty()) {
                          info(`[ClipboardService] 从base64创建图片成功`);
                          const dataURL = nativeImg.toDataURL();
                          const lastDataURL = this.lastImage ? this.lastImage.toDataURL() : '';
                          
                          if (dataURL !== lastDataURL) {
                            const imageItem: ClipboardImageItem = { 
                              dataURL: dataURL, 
                              size: dataURL.length,
                              width: nativeImg.getSize().width,
                              height: nativeImg.getSize().height,
                              name: `图片_${new Date().toISOString().replace(/[:.]/g, '-')}.png`,
                              contentType: formats.find(f => f.startsWith('image/')) || 'image/png'
                            };
                            itemToAdd = {
                              id: itemId,
                              type: 'image',
                              timestamp: now,
                              items: [imageItem],
                              count: 1,
                              source: FocusManager.getPreviousFocusedApp() || undefined,
                              contentType: imageItem.contentType,
                            };
                            break;
                          }
                        }
                      } catch (e) {
                        info(`[ClipboardService] 从base64创建图片失败: ${e}`);
                      }
                    }
                  }
                } catch (e) {
                  info(`[ClipboardService] 读取格式 ${format} 失败: ${e.message}`);
                }
              }
            } catch (formatError) {
              info(`[ClipboardService] 处理格式 ${format} 时出错: ${formatError.message}`);
            }
          }
        } catch (bufferError) {
          info(`[ClipboardService] 尝试从缓冲区读取图片失败:`, bufferError);
        }
        
        // 最后尝试：使用系统命令读取剪贴板（macOS特有）
        if (process.platform === 'darwin' && !itemToAdd) {
          info(`[ClipboardService] 尝试使用 macOS 系统命令读取图片数据`);
          try {
            // 尝试多种pbpaste参数获取图片数据
            const commands = [
              'pbpaste -Prefer png',
              'pbpaste -Prefer tiff', 
              'pbpaste -Prefer public.png',
              'pbpaste -Prefer public.tiff',
              'pbpaste -plist'
            ];
            
            let result = '';
            let successfulCommand = '';
            
            for (const cmd of commands) {
              try {
                info(`[ClipboardService] 执行命令: ${cmd}`);
                const cmdResult = execSync(cmd, { encoding: null, timeout: 2000 });
                
                if (cmdResult && cmdResult.length > 0) {
                  info(`[ClipboardService] 命令 ${cmd} 成功，数据长度: ${cmdResult.length}`);
                  
                  // 检查是否是二进制图片数据
                  if (cmd.includes('-Prefer')) {
                                         // 直接尝试从二进制数据创建图片
                     try {
                       const nativeImg = nativeImage.createFromBuffer(cmdResult as unknown as Buffer);
                       if (!nativeImg.isEmpty()) {
                        info(`[ClipboardService] 从命令 ${cmd} 成功创建图片`);
                        const dataURL = nativeImg.toDataURL();
                        const lastDataURL = this.lastImage ? this.lastImage.toDataURL() : '';
                        
                        if (dataURL !== lastDataURL) {
                          const imageItem: ClipboardImageItem = { 
                            dataURL: dataURL, 
                            size: dataURL.length,
                            width: nativeImg.getSize().width,
                            height: nativeImg.getSize().height,
                            name: `图片_${new Date().toISOString().replace(/[:.]/g, '-')}.png`,
                            contentType: formats.find(f => f.startsWith('image/')) || 'image/png'
                          };
                                                     itemToAdd = {
                             id: itemId,
                             type: 'image',
                             timestamp: now,
                             items: [imageItem],
                             count: 1,
                             source: FocusManager.getPreviousFocusedApp() || undefined,
                             contentType: imageItem.contentType,
                           };
                           this.lastImage = nativeImg;
                           this.lastTextContent = '';
                           info(`[ClipboardService] 通过命令 ${cmd} 成功创建图片项目`);
                          break;
                        }
                      }
                    } catch (e) {
                      info(`[ClipboardService] 命令 ${cmd} 创建图片失败: ${e.message}`);
                    }
                  } else {
                    // plist格式，按原来的方式处理
                    result = cmdResult.toString();
                    successfulCommand = cmd;
                    break;
                  }
                } else {
                  info(`[ClipboardService] 命令 ${cmd} 无输出`);
                }
              } catch (cmdError) {
                info(`[ClipboardService] 命令 ${cmd} 执行失败: ${cmdError.message}`);
              }
            }
            
            // 如果通过上面的直接方法成功了，就不需要继续处理plist
            if (itemToAdd) {
              info(`[ClipboardService] 已通过系统命令成功创建图片项目`);
            } else if (result && result.length > 0) {
              info(`[ClipboardService] pbpaste 输出长度: ${result.length}`);
              info(`[ClipboardService] pbpaste 输出预览: ${result.substring(0, 300)}`);
              
              // 检查是否包含图片数据
              if (result.includes('<data>') || result.includes('image')) {
                info(`[ClipboardService] pbpaste 输出包含图片数据`);
                // 尝试提取base64数据
                const dataMatch = result.match(/<data>\s*(.*?)\s*<\/data>/s);
                if (dataMatch) {
                  const base64Data = dataMatch[1].replace(/\s/g, '');
                  info(`[ClipboardService] 提取到base64数据长度: ${base64Data.length}`);
                  
                  if (base64Data.length > 0) {
                    try {
                      const buffer = Buffer.from(base64Data, 'base64');
                      info(`[ClipboardService] 创建buffer成功，大小: ${buffer.length}`);
                      const nativeImg = nativeImage.createFromBuffer(buffer);
                      info(`[ClipboardService] 创建nativeImage - isEmpty: ${nativeImg.isEmpty()}`);
                      
                      if (!nativeImg.isEmpty()) {
                        info(`[ClipboardService] 从系统命令创建图片成功`);
                        const dataURL = nativeImg.toDataURL();
                        const lastDataURL = this.lastImage ? this.lastImage.toDataURL() : '';
                        
                        if (dataURL !== lastDataURL) {
                          const imageItem: ClipboardImageItem = { 
                            dataURL: dataURL, 
                            size: dataURL.length,
                            width: nativeImg.getSize().width,
                            height: nativeImg.getSize().height,
                            name: `图片_${new Date().toISOString().replace(/[:.]/g, '-')}.png`,
                            contentType: formats.find(f => f.startsWith('image/')) || 'image/png'
                          };
                          itemToAdd = {
                            id: itemId,
                            type: 'image',
                            timestamp: now,
                            items: [imageItem],
                            count: 1,
                            source: FocusManager.getPreviousFocusedApp() || undefined,
                            contentType: imageItem.contentType,
                          };
                          this.lastImage = nativeImg;
                          this.lastTextContent = '';
                          info(`[ClipboardService] 通过系统命令成功创建图片项目`);
                        } else {
                          info(`[ClipboardService] 图片与上次相同，跳过`);
                        }
                      } else {
                        info(`[ClipboardService] 从系统命令创建的图片为空`);
                      }
                    } catch (e) {
                      info(`[ClipboardService] 从系统命令数据创建图片失败: ${e.message}`);
                    }
                  } else {
                    info(`[ClipboardService] 提取到的base64数据为空`);
                  }
                } else {
                  info(`[ClipboardService] 未找到<data>标签`);
                }
              } else {
                info(`[ClipboardService] pbpaste 输出不包含图片数据`);
              }
            } else {
              info(`[ClipboardService] pbpaste 输出为空`);
            }
          } catch (sysError) {
            info(`[ClipboardService] 系统命令执行失败: ${sysError.message || sysError}`);
            // 尝试更简单的命令
            try {
              info(`[ClipboardService] 尝试使用 pbpaste 不带参数...`);
              const simpleResult = execSync('pbpaste', { encoding: 'utf8', timeout: 2000 });
              info(`[ClipboardService] pbpaste 简单命令结果长度: ${simpleResult ? simpleResult.length : 'null'}`);
              if (simpleResult && simpleResult.length > 0) {
                info(`[ClipboardService] pbpaste 简单输出预览: ${simpleResult.substring(0, 100)}`);
              }
            } catch (simpleError) {
              info(`[ClipboardService] 简单 pbpaste 命令也失败: ${simpleError.message}`);
            }
          }
        }
        
        // 🔥 最后手段：通过备用方案保存IM应用图片
        if (!itemToAdd && formats.includes('image/png')) {
          info(`[ClipboardService] ⚠️ image/png格式存在但readImage()返回空数据，可能是IM应用延迟加载图片，启用备用方案`);
          
          // 重新获取当前图片状态
          const currentImage = clipboard.readImage();
          
          info(`[ClipboardService] 检测到新的IM应用图片，尝试保存`);
          
          // 检测图片格式并保存
          let imageData = '';
          let detectedFormat = 'png';
          
          try {
            // 检查剪贴板中是否有GIF数据
            const availableFormats = clipboard.availableFormats();
            info(`[ClipboardService] 检测剪贴板格式: ${JSON.stringify(availableFormats)}`);
            
            if (availableFormats.includes('com.compuserve.gif') || 
                availableFormats.includes('public.gif') || 
                availableFormats.includes('image/gif')) {
              detectedFormat = 'gif';
              info('[ClipboardService] 检测到GIF格式');
            }
          } catch (error) {
            info('[ClipboardService] 格式检测失败:', error.message);
          }
          
          try {
            info(`[ClipboardService] 使用 Electron 内置的 clipboard.readBuffer 读取${detectedFormat}格式图片`);
            
            // 尝试使用 Electron 内置的 clipboard.readBuffer 读取图片数据
            let imageBuffer: Buffer | null = null;
            let actualMimeType = '';
            
            if (detectedFormat === 'gif') {
              // 对于GIF格式
              try {
                // 尝试多种GIF格式
                const gifFormats = ['public.gif', 'com.compuserve.gif', 'image/gif'];
                for (const format of gifFormats) {
                  try {
                    imageBuffer = clipboard.readBuffer(format);
                    if (imageBuffer && imageBuffer.length > 0) {
                      actualMimeType = 'image/gif';
                      info(`[ClipboardService] 通过 clipboard.readBuffer 成功读取GIF（格式：${format}），大小: ${imageBuffer.length} 字节`);
                      break;
                    }
                  } catch (e) {
                    info(`[ClipboardService] clipboard.readBuffer 格式 ${format} 失败: ${e.message}`);
                  }
                }
              } catch (e) {
                info(`[ClipboardService] clipboard.readBuffer 读取GIF失败: ${e.message}`);
              }
            } else {
              // 对于PNG格式
              try {
                // 尝试多种PNG格式
                const pngFormats = ['public.png', 'image/png'];
                for (const format of pngFormats) {
                  try {
                    imageBuffer = clipboard.readBuffer(format);
                    if (imageBuffer && imageBuffer.length > 0) {
                      actualMimeType = 'image/png';
                      info(`[ClipboardService] 通过 clipboard.readBuffer 成功读取PNG（格式：${format}），大小: ${imageBuffer.length} 字节`);
                      break;
                    }
                  } catch (e) {
                    info(`[ClipboardService] clipboard.readBuffer 格式 ${format} 失败: ${e.message}`);
                  }
                }
              } catch (e) {
                info(`[ClipboardService] clipboard.readBuffer 读取PNG失败: ${e.message}`);
              }
            }
            
            // 如果 clipboard.readBuffer 失败，回退到系统命令
            if (!imageBuffer && process.platform === 'darwin') {
              info(`[ClipboardService] 回退到系统命令获取图片数据`);
              
              const tempImagePath = path.join(os.tmpdir(), `clipboard_${Date.now()}.${detectedFormat}`);
              
              if (detectedFormat === 'gif') {
                // 对于GIF，使用pbpaste直接保存
                execSync(`pbpaste > "${tempImagePath}"`, { timeout: 3000 });
              } else {
                // 对于PNG，尝试使用pbpaste获取二进制数据
                const pngData = execSync('pbpaste -Prefer png', { encoding: null, timeout: 3000 });
                if (pngData && pngData.length > 0) {
                  fs.writeFileSync(tempImagePath, pngData);
                  info(`[ClipboardService] 使用 pbpaste 成功获取PNG数据，大小: ${pngData.length} 字节`);
                }
              }
              
              // 读取临时文件
              if (fs.existsSync(tempImagePath)) {
                const stats = fs.statSync(tempImagePath);
                if (stats.size > 100) {
                  imageBuffer = fs.readFileSync(tempImagePath);
                  actualMimeType = detectedFormat === 'gif' ? 'image/gif' : 'image/png';
                  info(`[ClipboardService] 从临时文件读取图片数据，大小: ${stats.size} 字节`);
                }
                // 清理临时文件
                fs.unlinkSync(tempImagePath);
              }
            }
            
            // 验证并转换图片数据
            if (imageBuffer && imageBuffer.length > 100) {
              // 验证文件格式
              const fileHeader = imageBuffer.slice(0, 16); // 读取更多字节用于调试
              let isValidImage = false;
              
              // 输出文件头的十六进制表示用于调试
              const headerHex = Array.from(fileHeader).map(b => b.toString(16).padStart(2, '0')).join(' ');
              info(`[ClipboardService] 读取到的文件头 (前16字节): ${headerHex}`);
              
              // PNG 标准文件头: 89 50 4E 47 0D 0A 1A 0A
              if (actualMimeType === 'image/gif' && fileHeader[0] === 0x47 && fileHeader[1] === 0x49 && fileHeader[2] === 0x46) {
                isValidImage = true;
                info(`[ClipboardService] ✅ GIF文件头验证通过`);
              } else if (actualMimeType === 'image/png' && fileHeader[0] === 0x89 && fileHeader[1] === 0x50 && fileHeader[2] === 0x4E && fileHeader[3] === 0x47) {
                isValidImage = true;
                info(`[ClipboardService] ✅ PNG文件头验证通过`);
              } else {
                // 🔥 尝试直接使用数据创建图片，不依赖文件头验证
                info(`[ClipboardService] ⚠️ 标准文件头验证失败，尝试直接使用数据创建图片`);
                try {
                  const testImage = nativeImage.createFromBuffer(imageBuffer);
                  if (!testImage.isEmpty()) {
                    info(`[ClipboardService] ✅ 虽然文件头验证失败，但数据可以创建有效图片`);
                    isValidImage = true;
                    // 使用实际检测到的格式
                    const testDataURL = testImage.toDataURL();
                    if (testDataURL.startsWith('data:image/png')) {
                      actualMimeType = 'image/png';
                    } else if (testDataURL.startsWith('data:image/gif')) {
                      actualMimeType = 'image/gif';
                    } else if (testDataURL.startsWith('data:image/jpeg')) {
                      actualMimeType = 'image/jpeg';
                    }
                    info(`[ClipboardService] 检测到的实际图片格式: ${actualMimeType}`);
                  } else {
                    info(`[ClipboardService] ❌ 数据无法创建有效图片`);
                  }
                } catch (testError) {
                  info(`[ClipboardService] ❌ 数据创建图片失败: ${testError.message}`);
                }
              }
              
              if (isValidImage) {
                imageData = `data:${actualMimeType};base64,${imageBuffer.toString('base64')}`;
                info(`[ClipboardService] 🎉 成功获取图片数据为${actualMimeType}，大小: ${imageBuffer.length} 字节`);
              } else {
                info(`[ClipboardService] ❌ 图片数据验证失败，无法使用`);
              }
            }
          } catch (error) {
            info('[ClipboardService] 图片获取失败:', error.message);
          }
          
          // 创建图片记录
          if (imageData) {
            // 成功获取到图片数据
            const imageItem: ClipboardImageItem = { 
              dataURL: imageData,
              size: imageData.length,
              width: image.getSize().width,
              height: image.getSize().height,
              name: `图片_${new Date().toISOString().replace(/[:.]/g, '-')}.${detectedFormat}`,
              contentType: formats.find(f => f.startsWith('image/')) || 'image/png'
            };
            itemToAdd = {
              id: itemId,
              type: 'image',
              timestamp: now,
              items: [imageItem],
              count: 1,
              source: FocusManager.getPreviousFocusedApp() || undefined,
              contentType: imageItem.contentType,
            };
            this.lastImage = nativeImage.createFromDataURL(imageData);
            this.lastTextContent = '';
            info(`[ClipboardService] 🎉 成功保存图片到历史: ${imageItem.name}`);
          } else {
            // 实在无法获取，创建占位符但标明无法使用
            const imageItem: ClipboardImageItem = { 
              dataURL: '',
              size: 0,
              width: image.getSize().width,
              height: image.getSize().height,
              name: `图片_${new Date().toISOString().replace(/[:.]/g, '-')} [无法恢复]`,
              contentType: formats.find(f => f.startsWith('image/')) || 'image/png'
            };
            itemToAdd = {
              id: itemId,
              type: 'image',
              timestamp: now,
              items: [imageItem],
              count: 1,
              source: FocusManager.getPreviousFocusedApp() || undefined,
              contentType: imageItem.contentType,
            };
            this.lastImage = currentImage;
            this.lastTextContent = '';
            info(`[ClipboardService] ⚠️ 无法保存图片，创建不可用占位符: ${imageItem.name}`);
          }
        }
      }
    } else if (_detectedType === 'file') {
      // macOS: public.file-url
      const fileUrl = clipboard.read('public.file-url');
      const filePath = fileURLToPath(fileUrl.toString()).trim();
      if (filePath && fs.existsSync(filePath)) {
        itemToAdd = await this.createFileItem(filePath, itemId, now);
        this.lastTextContent = filePath;
        this.lastImage = null;
                } else {
        // Linux / 跨平台: text/uri-list
        if (formats.includes('text/uri-list')) {
          let uriRaw = currentText.trim();
          if (!uriRaw) {
            try {
              uriRaw = clipboard.read('text/uri-list').toString();
            } catch { /* ignore */ }
          }

          if (uriRaw) {
            const uris = uriRaw.split('\n').filter(u => u && !u.startsWith('#'));
                if (uris.length > 0) {
              const firstUri = uris[0].trim();
              let firstPath = firstUri;
              if (firstUri.startsWith('file://')) {
                try { firstPath = fileURLToPath(firstUri); } catch { firstPath = firstUri.replace('file://', ''); }
              }
              if (fs.existsSync(firstPath)) {
                itemToAdd = await this.createFileItem(firstPath, itemId, now);
                this.lastTextContent = firstPath;
                this.lastImage = null;
              }
            }
          }
        }

        // Windows: CF_HDROP
        if (!itemToAdd && formats.includes('CF_HDROP')) {
          try {
      const buffer = clipboard.readBuffer('CF_HDROP');
            let pathStr = '';
            for (let i = 0; i < buffer.length - 2; i += 2) {
        const charCode = buffer.readUInt16LE(i);
              if (charCode === 0) break;
              pathStr += String.fromCharCode(charCode);
            }
            const winPath = pathStr.trim();
            if (winPath && fs.existsSync(winPath)) {
              itemToAdd = await this.createFileItem(winPath, itemId, now);
              this.lastTextContent = winPath;
        this.lastImage = null;
      }
          } catch {
            /* ignore */
          }
        }
      }
    } else if (_detectedType === 'text') {
      const text = currentText.trim(); // 使用已获取的currentText
      if (text) {
        if (text.startsWith('file:///')) {
          try {
            const filePath = fileURLToPath(text);
            if (fs.existsSync(filePath)) {
              itemToAdd = await this.createFileItem(filePath, itemId, now);
            }
          } catch (e) {
            logError('Error parsing file url', e);
          }
        }
        
        if (!itemToAdd) {
          const textItem: ClipboardTextItem = {
            content: text,
            characters: text.length,
            words: text.trim().split(/\s+/).filter(Boolean).length,
            contentType: 'text/plain'
          };
          itemToAdd = {
            id: itemId,
            type: 'text',
            timestamp: now,
            items: [textItem],
            count: 1,
            source: FocusManager.getPreviousFocusedApp() || undefined,
            contentType: textItem.contentType,
          };
          this.lastTextContent = text;
        }
      }
    }
    
    info(`[ClipboardService] 🔥 分析完成，itemToAdd: ${itemToAdd ? itemToAdd.type : 'null'}`);
    this.addHistoryItem(itemToAdd);
  }

  private async createFileItem(filePath: string, itemId: string, timestamp: number): Promise<ClipboardItem | null> {
    try {
      const stats = await fs.promises.stat(filePath);
      if (stats.isFile()) {
        // 使用 file-type 精准检测
        let detectedMime: string | undefined;
        let detectedExt: string | undefined;
        try {
          const { fileTypeFromFile } = await import('file-type');
          const result = await fileTypeFromFile(filePath);
          if (result) {
            detectedMime = result.mime;
            detectedExt = result.ext;
          }
        } catch (e) {
          // 忽略检测错误，回退扩展名
        }

        const extFromPath = path.extname(filePath).toLowerCase().slice(1);
        const finalExt = detectedExt || extFromPath || 'unknown';
        const finalMime = detectedMime || (mimeLookup(finalExt) as string) || 'application/octet-stream';

        const fileItem: ClipboardFileItem = {
          name: path.basename(filePath),
          content: filePath,
          path: filePath,
          type: finalExt,
          size: stats.size,
          thumbnail: await this.generateThumbnail(filePath),
          contentType: finalMime,
        };
        return { id: itemId, type: 'file', timestamp, items: [fileItem], count: 1, source: FocusManager.getPreviousFocusedApp() || undefined, contentType: fileItem.contentType };
      } else if (stats.isDirectory()) {
        const dirItem: ClipboardDirectoryItem = {
          name: path.basename(filePath),
          content: filePath,
          path: filePath,
          type: 'directory',
          contentType: 'inode/directory'
        };
        return { id: itemId, type: 'directory', timestamp, items: [dirItem], count: 1, source: FocusManager.getPreviousFocusedApp() || undefined, contentType: dirItem.contentType };
      }
    } catch (e) {
      logError(`[ClipboardService] Failed to stat file ${filePath}:`, e);
    }
    return null;
  }

  // 生成文件缩略图
  private async generateThumbnail(filePath: string): Promise<string | undefined> {
    try {
      const ext = path.extname(filePath).toLowerCase();
      
      // 图片文件直接生成缩略图
      if (['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp'].includes(ext)) {
        try {
          const image = nativeImage.createFromPath(filePath);
          if (!image.isEmpty()) {
            // 调整大小为缩略图
            const resized = image.resize({ width: 64, height: 64 });
            return resized.toDataURL();
          }
        } catch (thumbError) {
          info(`[ClipboardService] Thumbnail generation failed for ${filePath}, skipping`);
        }
      }
      
      // 其他文件类型返回文件类型图标
      return undefined;
    } catch (e) {
      info(`[ClipboardService] Thumbnail generation error for ${filePath}, continuing without thumbnail`);
      return undefined;
    }
  }

  private addHistoryItem(item: ClipboardItem | null): void {
    if (item) {
      try {
        // 检查重复项
        const duplicate = dbService.findDuplicateClipboardItem(
          dbService.generateContentHash(item)
        );
        
        if (duplicate) {
          // 删除重复项
          dbService.deleteClipboardItem(duplicate.id);
          info(`删除重复的剪贴板记录: ${duplicate.id}`);
        }
        
        // 保存到数据库
        dbService.saveClipboardItem(item);
        
        // 对于非图片类型，清除lastImage
        if (item.type !== 'image') {
          this.lastImage = null;
        }
        
        // 发出事件通知剪贴板变化（传递空数组，因为现在使用分页加载）
        this.emit('changed', []);
        
        // 安全地发送到渲染进程
        try {
          const mainWindow = WindowManager.getMainWindow()?.getWindow();
          if (mainWindow && !mainWindow.isDestroyed()) {
            mainWindow.webContents.send('clipboard-change', []);
          }
        } catch (error) {
          info(`发送到渲染进程失败: ${error.message}`);
        }
      } catch (error) {
        logError('保存剪贴板记录到数据库失败:', error);
        // 即使数据库保存失败，也要发出变化事件
        this.emit('changed', []);
      }
    }
  }


  /**
   * 将项目写入剪贴板（更新使用统计）
   */
  async writeItemToClipboard(item: ClipboardItem): Promise<boolean> {
    try {
      // 暂停监控以避免触发新的历史记录
      this.pauseClipboardMonitoring();

      // 根据项目类型写入剪贴板（同步操作）
      if (item.type === 'text' && item.items.length > 0) {
        const textItem = item.items[0] as ClipboardTextItem;
        clipboard.writeText(textItem.content);
      } else if (item.type === 'image' && item.items.length > 0) {
        const imageItem = item.items[0] as ClipboardImageItem;
        if (imageItem.dataURL) {
          const image = nativeImage.createFromDataURL(imageItem.dataURL);
          clipboard.writeImage(image);
        } else {
          // 检查是否是最新的占位符
          try {
            const current = this.getCurrent();
            const isLatestItem = current && current.id === item.id;
            if (isLatestItem) {
              // 最新的微信占位符，保持剪贴板不变（图片数据仍在系统剪贴板中）
              console.log('[ClipboardService] 最新微信占位符图片，保持原剪贴板内容');
            } else {
              // 历史的微信占位符，无法恢复，粘贴提示文本
              console.log('[ClipboardService] 历史微信占位符图片，粘贴提示文本');
              clipboard.writeText(`${imageItem.content || '图片'} (历史记录，无法粘贴原图)`);
            }
          } catch (error) {
            // 获取当前项目失败，默认作为历史项目处理
            console.log('[ClipboardService] 历史微信占位符图片，粘贴提示文本');
            clipboard.writeText(`${imageItem.content || '图片'} (历史记录，无法粘贴原图)`);
          }
        }
      } else if (item.type === 'file' || item.type === 'directory' || item.type === 'multiple') {
        // 文件写入剪贴板：根据平台使用不同的格式
        const paths = item.items.map(i => 
          (i as ClipboardFileItem | ClipboardDirectoryItem).content
        );
        
        if (process.platform === 'darwin') {
          // macOS: 使用正确的文件格式写入剪贴板
          if (paths.length === 1) {
            const filePath = paths[0];
            info(`写入单个文件到剪贴板: ${filePath}`);
            
            // 🔥 检查是否是沙盒应用的图片文件
            if (this.isSandboxPath(filePath) && this.isImageFile(filePath)) {
              info(`检测到沙盒图片文件，尝试读取图片数据: ${filePath}`);
              const success = await this.handleSandboxImageFile(filePath);
              if (success) {
                info(`成功处理沙盒图片文件`);
              } else {
                info(`沙盒图片文件处理失败，回退到提示文本`);
                clipboard.writeText(`${path.basename(filePath)} (来自企业微信，无法在其他应用中粘贴原图)`);
              }
              // 🔥 沙盒图片文件处理完成，不执行后续的文件处理逻辑
            } else {
              // 检查文件是否存在
              if (fs.existsSync(filePath)) {
                try {
                  // 使用系统命令写入文件到剪贴板（更可靠的方式）
                  const fileUrl = `file://${filePath}`;
                  info(`使用系统命令写入文件: ${fileUrl}`);
                  
                  // 使用osascript写入文件引用
                  const script = `
                    set theFile to POSIX file "${filePath}"
                    set the clipboard to theFile
                  `;
                  execSync(`osascript -e '${script}'`, { timeout: 3000 });
                  info(`成功使用osascript写入文件`);
                } catch (error) {
                  info(`osascript写入文件失败，使用文本回退: ${error.message}`);
                  clipboard.writeText(filePath);
                }
              } else {
                info(`文件不存在，写入路径文本: ${filePath}`);
                clipboard.writeText(filePath);
              }
            }
          } else {
            // 多个文件，检查是否包含沙盒图片
            const sandboxImagePaths = paths.filter(p => this.isSandboxPath(p) && this.isImageFile(p));
            if (sandboxImagePaths.length > 0) {
              info(`检测到多个文件中包含沙盒图片，写入提示文本`);
              clipboard.writeText(`选中的文件包含来自企业微信的图片，无法在其他应用中粘贴`);
            } else {
              // 多个文件，使用文本格式作为回退
              info(`写入多个文件: ${paths.length}个`);
              clipboard.writeText(paths.join('\n'));
            }
          }
        } else if (process.platform === 'win32') {
          // Windows: 使用 CF_HDROP 格式
          // 这里需要构造 CF_HDROP 格式的 buffer
          clipboard.writeText(paths.join('\n')); // 临时使用文本格式
        } else {
          // Linux/其他: 使用 text/uri-list 格式
          const uriList = paths.map(p => `file://${p}`).join('\n');
          clipboard.writeText(uriList);
        }
      }

      // 更新使用统计
      try {
        dbService.updateClipboardUsage(item.id);
      } catch (error) {
        logError('更新剪贴板使用统计失败:', error);
      }

      // 完全异步恢复监控，不阻塞任何操作
      setImmediate(() => {
        setTimeout(() => {
          this.resumeClipboardMonitoring();
        }, 30);
      });

      return true;
    } catch (error) {
      console.error('写入剪贴板失败:', error);
      this.resumeClipboardMonitoring();
      return false;
    }
  }

  // 获取所有历史记录（支持分页）
  getHistory(options?: {
    page?: number;
    limit?: number;
    type?: string;
    favorite?: boolean;
    search?: string;
  }): ClipboardItem[] {
    try {
      // 总是从数据库获取数据，默认获取前maxHistorySize条
      const defaultOptions = {
        page: 1,
        limit: this.maxHistorySize,
        ...options
      };
      const result = dbService.getClipboardHistory(defaultOptions);
      return result.items;
    } catch (error) {
      logError('从数据库获取剪贴板历史失败:', error);
      return [];
    }
  }

  /**
   * 获取分页的剪贴板历史记录
   */
  getHistoryPaginated(options: {
    page?: number;
    limit?: number;
    type?: string;
    favorite?: boolean;
    search?: string;
  } = {}): {
    items: ClipboardItem[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  } {
    try {
      return dbService.getClipboardHistory(options);
    } catch (error) {
      logError('获取分页剪贴板历史失败:', error);
      // 返回空数据
      const limit = options.limit || 50;
      const page = options.page || 1;
      
      return {
        items: [],
        total: 0,
        page,
        limit,
        totalPages: 0
      };
    }
  }

  // 获取当前剪贴板内容（最新的一条记录）
  getCurrent(): ClipboardItem | null {
    try {
      const result = dbService.getClipboardHistory({
        page: 1,
        limit: 1
      });
      return result.items[0] || null;
    } catch (error) {
      logError('获取当前剪贴板内容失败:', error);
      return null;
    }
  }

  // 清空历史记录（使用数据库）
  clearHistory(): void {
    try {
      const deletedCount = dbService.clearClipboardHistory();
      info(`已清空 ${deletedCount} 条剪贴板历史记录`);
    } catch (error) {
      logError('清空剪贴板历史失败:', error);
    }
    
    // 发出变更事件，让 clipboard-handler 处理通知渲染进程
    this.emit('changed', []);
  }

  /**
   * 删除指定的剪贴板记录
   */
  deleteHistoryItem(id: string): boolean {
    try {
      const success = dbService.deleteClipboardItem(id);
      if (success) {
        // 发出变更事件
        this.emit('changed', []);
      }
      return success;
    } catch (error) {
      logError('删除剪贴板记录失败:', error);
      return false;
    }
  }

  /**
   * 设置收藏状态
   */
  setHistoryItemFavorite(id: string, favorite: boolean): boolean {
    try {
      const success = dbService.setClipboardFavorite(id, favorite);
      if (success) {
        // 发出变更事件
        this.emit('changed', []);
      }
      return success;
    } catch (error) {
      logError('设置剪贴板收藏状态失败:', error);
      return false;
    }
  }

  /**
   * 更新备注
   */
  updateHistoryItemMemo(id: string, memo: string): boolean {
    try {
      const success = dbService.updateClipboardMemo(id, memo);
      if (success) {
        // 发出变更事件
        this.emit('changed', []);
      }
      return success;
    } catch (error) {
      logError('更新剪贴板备注失败:', error);
      return false;
    }
  }

  // 暂停剪贴板监控（用于粘贴操作期间）
  pauseClipboardMonitoring(): void {
    this.pauseMonitoring = true;
    this.stopPolling(); // 暂停时停止轮询以节省性能
    info('[ClipboardService] ⏸️ 暂停剪切板监控');
  }

  // 恢复剪贴板监控
  resumeClipboardMonitoring(): void {
    this.pauseMonitoring = false;
    if (this.initialized) {
      this.startPolling(); // 恢复时重新开始轮询
      info('[ClipboardService] ▶️ 恢复剪切板监控');
    }
  }

  // 销毁服务，清理资源
  destroy(): void {
    info('[ClipboardService] 🧹 销毁剪切板服务');
    this.stopPolling();
    this.removeAllListeners();
    this.initialized = false;
  }

  /**
   * 检测文件路径是否在应用沙盒中
   */
  private isSandboxPath(filePath: string): boolean {
    if (!filePath) return false;
    
    // 常见的沙盒应用路径模式
    const sandboxPatterns = [
      // macOS 沙盒容器路径
      '/Library/Containers/',
      // 企业微信
      'com.tencent.WeWorkMac',
      // 微信
      'com.tencent.xinWeChat',
      // QQ
      'com.tencent.qq',
      // 其他可能的沙盒应用
      '/Library/Group Containers/',
    ];
    
    return sandboxPatterns.some(pattern => filePath.includes(pattern));
  }

  /**
   * 检测是否是图片文件
   */
  private isImageFile(filePath: string): boolean {
    if (!filePath) return false;
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.svg'];
    const ext = path.extname(filePath).toLowerCase();
    return imageExtensions.includes(ext);
  }

  /**
   * 从沙盒文件路径中读取图片数据并写入剪贴板
   * 注意：此方法会暂时停止剪贴板监控以避免重复记录
   */
  private async handleSandboxImageFile(filePath: string): Promise<boolean> {
    try {
      info(`[ClipboardService] 处理沙盒图片文件: ${filePath}`);
      
      // 🔥 在写入图片数据前，记录当前的剪贴板哈希值，避免重复检测
      const currentHash = this.lastClipboardHash;
      
      // 首先尝试直接读取文件
      if (fs.existsSync(filePath)) {
        try {
          const imageData = fs.readFileSync(filePath);
          const image = nativeImage.createFromBuffer(imageData);
          
          if (!image.isEmpty()) {
            // 🔥 暂时增加监控暂停时间，确保写入操作不被检测
            const originalPauseState = this.pauseMonitoring;
            this.pauseMonitoring = true;
            
            clipboard.writeImage(image);
            
            // 🔥 更新哈希值为新的图片数据哈希，防止重复检测
            const imageBuffer = image.toPNG();
            this.lastClipboardHash = crypto.createHash('md5').update(imageBuffer).digest('hex');
            
            // 延迟恢复监控状态
            setTimeout(() => {
              this.pauseMonitoring = originalPauseState;
            }, 100);
            
            info(`[ClipboardService] 成功从沙盒文件读取图片并写入剪贴板: ${path.basename(filePath)}`);
            return true;
          }
        } catch (readError) {
          info(`[ClipboardService] 直接读取沙盒文件失败: ${readError.message}`);
        }
      }
      
      // 如果直接读取失败，尝试通过系统剪贴板获取图片数据
      try {
        let image: NativeImage;
        try {
          image = clipboard.readImage();
        } catch (error) {
          image = nativeImage.createEmpty();
        }
        
        if (!image.isEmpty()) {
          // 图片数据已经在剪贴板中，不需要重新写入，只更新哈希
          const imageBuffer = image.toPNG();
          this.lastClipboardHash = crypto.createHash('md5').update(imageBuffer).digest('hex');
          
          info(`[ClipboardService] 使用剪贴板中的图片数据: ${path.basename(filePath)}`);
          return true;
        }
        
        // 最后尝试使用系统命令获取图片数据（macOS）
        if (process.platform === 'darwin') {
          const pngData = execSync('pbpaste -Prefer png', { encoding: null, timeout: 3000 }) as unknown as Buffer;
          if (pngData && pngData.length > 0) {
            const systemImage = nativeImage.createFromBuffer(pngData);
            if (!systemImage.isEmpty()) {
              // 🔥 同样需要暂停监控
              const originalPauseState = this.pauseMonitoring;
              this.pauseMonitoring = true;
              
              clipboard.writeImage(systemImage);
              
              // 更新哈希值
              this.lastClipboardHash = crypto.createHash('md5').update(pngData).digest('hex');
              
              setTimeout(() => {
                this.pauseMonitoring = originalPauseState;
              }, 100);
              
              info(`[ClipboardService] 通过系统命令获取图片数据: ${path.basename(filePath)}`);
              return true;
            }
          }
        }
      } catch (systemError) {
        info(`[ClipboardService] 系统剪贴板图片获取失败: ${systemError.message}`);
      }
      
      return false;
    } catch (error) {
      logError(`[ClipboardService] 处理沙盒图片文件失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 判断字符串是否可能是文件名
   */
  private couldBeFileName(str: string): boolean {
    if (!str || str.length === 0) return false;
    
    // 长度过长的不太可能是文件名
    if (str.length > 200) return false;
    
    // 包含换行符的不是文件名
    if (str.includes('\n') || str.includes('\r')) return false;
    
    // 以URL协议开头的不是文件名
    if (/^[a-zA-Z]+:\/\//.test(str)) return false;
    
    // 包含HTML标签的不是文件名
    if (/<[^>]+>/.test(str)) return false;
    
    // 包含多个空格的通常不是文件名
    if (str.split(' ').length > 3) return false;
    
    // 如果有扩展名，使用 mime-types 库判断是否为已知类型
    if (str.includes('.')) {
      const ext = path.extname(str).toLowerCase();
      const mime = mimeLookup(ext);
      if (mime) return true;
      // 若库中未识别，但扩展名长度 1-6 且仅包含字母数字，也认为是文件名
      const extBody = ext.replace('.', '');
      return extBody.length > 0 && extBody.length <= 6 && /^[a-z0-9]+$/i.test(extBody);
    }
    
    // 没有扩展名的情况：
    // 1. 只包含字母、数字、下划线、连字符、中文字符、空格
    const fileNamePattern = /^[a-zA-Z0-9_\-\u4e00-\u9fff\s]+$/;
    if (!fileNamePattern.test(str)) return false;
    
    // 2. 不是明显的句子（包含常见句子特征）
    const sentencePattern = /[.!?，。！？;；:：]/;
    if (sentencePattern.test(str)) return false;
    
    // 3. 长度合理
    if (str.length < 1 || str.length > 50) return false;
    
    // 4. 不是纯数字或明显的时间格式
    if (/^\d+$/.test(str) || /^\d{4}-\d{2}-\d{2}/.test(str)) return false;
    
    return true;
  }

  /**
   * 快速粘贴方法
   */
  async performInstantPaste(): Promise<boolean> {
    try {
      const { spawn } = require('child_process');
      const platform = process.platform;
      
      return new Promise<boolean>((resolve) => {
        let command: string;
        let args: string[];
        
        if (platform === 'darwin') {
          // macOS - 使用AppleScript
          command = 'osascript';
          args = ['-e', 'tell application "System Events" to keystroke "v" using command down'];
        } else if (platform === 'win32') {
          // Windows - 使用PowerShell
          command = 'powershell';
          args = ['-Command', 'Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait("^v")'];
        } else {
          // Linux - 使用xdotool
          command = 'xdotool';
          args = ['key', 'ctrl+v'];
        }
        
        const child = spawn(command, args, {
          stdio: 'ignore', // 不捕获输出，提升性能
          detached: false
        });
        
        const timeout = setTimeout(() => {
          child.kill('SIGTERM');
          resolve(false);
        }, 1000); // 减少超时时间到1秒
        
        child.on('close', (code: number | null) => {
          clearTimeout(timeout);
          resolve(code === 0);
        });
        
        child.on('error', () => {
          clearTimeout(timeout);
          resolve(false);
        });
      });
    } catch (error) {
      console.error('快速粘贴失败:', error);
      return false;
    }
  }

  // 实现轮询机制
  private startPolling() {
    this.stopPolling(); // 确保没有重复的定时器
    
    const checkInterval = this.settings.clipboard?.checkInterval || 500;
    info(`[ClipboardService] 🔄 开始轮询剪切板，检查间隔: ${checkInterval}ms`);
    
    this.pollingTimer = setInterval(() => {
      this.checkClipboard();
    }, checkInterval);
  }

  // 重新实现的剪贴板检查方法：
  // 1. 仅负责检测剪贴板内容是否发生变化
  // 2. 能识别文本 / 图片 / 文件三大类复制操作
  // 3. 检测到变化后，将类型传递给 analyzeClipboardContent 进行后续处理
  private async checkClipboard() {
    if (this.pauseMonitoring || this.isProcessingChange) {
      return;
    }

    try {
      this.isProcessingChange = true;
      
      // 读取基础信息 - 在这里处理 JP2 错误
      let formats: string[] = [];
      try {
        formats = clipboard.availableFormats();
      } catch (error) {
        // 静默处理 JP2 颜色空间错误
        // console.warn('获取剪贴板格式失败（可能是 JP2 颜色空间问题）:', error.message);
        return;
      }

      let detectedType: 'text' | 'image' | 'file' | null = null;
      let currentHash = '';
      
      // info('checkClipboard formats:', formats);

      // 🔥 按优先级检测：文件 > 图片文件 > 图片 > 文本
      
      // ==================== 1. 文件检测（最高优先级）====================
      const hasFileFormat = formats.includes('CF_HDROP') || formats.includes('public.file-url');
      if (hasFileFormat) {
        // info('[checkClipboard] 检测到文件格式，开始文件检测');
        try {
          let filePath = '';
          
          if (formats.includes('public.file-url')) {
            // macOS 文件
            const fileUrl = clipboard.read('public.file-url').toString();
            filePath = fileURLToPath(fileUrl).trim();
          } else if (formats.includes('CF_HDROP')) {
            // Windows 文件
            const buffer = clipboard.readBuffer('CF_HDROP');
            for (let i = 0; i < buffer.length - 2; i += 2) {
              const charCode = buffer.readUInt16LE(i);
              if (charCode === 0) break;
              filePath += String.fromCharCode(charCode);
            }
            filePath = filePath.trim();
          }
          
          if (filePath && fs.existsSync(filePath)) {
            currentHash = crypto.createHash('md5').update(filePath).digest('hex');
            detectedType = 'file';
            // info('[checkClipboard] 文件检测成功:', filePath);
          }
        } catch (error) {
          // info('[checkClipboard] 文件检测失败:', error.message);    
        }
      }
      
      // ==================== 2. 图片文件检测（text/uri-list 中的图片）====================
      else if (formats.includes('text/uri-list')) {
        // info('[checkClipboard] 检测到 text/uri-list 格式，开始图片文件检测');
        try {
          const text = clipboard.readText();
          let uriRaw = text;
          
          if (!uriRaw) {
            try {
              uriRaw = clipboard.read('text/uri-list').toString();
            } catch { /* ignore */ }
          }
          
          // info('[checkClipboard] uriRaw 内容:', uriRaw ? `"${uriRaw}"` : 'null/empty');
          
          if (uriRaw) {
            const paths = uriRaw
              .split('\n')
              .filter(l => l && !l.startsWith('#'))
              .map(u => {
                if (u.startsWith('file://')) {
                  try { return fileURLToPath(u); } catch { return u.replace('file://', ''); }
                }
                return u;
              })
              .filter(Boolean);
            
            // info('[checkClipboard] 解析出的路径:', paths);  
            
            if (paths.length > 0) {
              const hashInput = paths.join('|');
              
              // 检查是否与上次相同
              if (hashInput === this.lastTextContent) {
                // info('[checkClipboard] 图片文件路径与上次相同，跳过');
                return;
              }
              
              currentHash = crypto.createHash('md5').update(hashInput).digest('hex');
              detectedType = 'file';
              // info('[checkClipboard] 图片文件检测成功');
            } else {
              // info('[checkClipboard] 解析出的路径为空，尝试系统级检测');
              // 路径为空，尝试系统级检测
              try {
                const tmpItem = await this.detectSystemClipboardFile(crypto.randomUUID(), Date.now());
                if (tmpItem && tmpItem.items && tmpItem.items.length > 0) {
                  const first = tmpItem.items[0] as any;
                  if (first.content) {
                    currentHash = crypto.createHash('md5').update(first.content).digest('hex');
                    detectedType = 'file';
                    // info('[checkClipboard] 系统级图片文件检测成功');
                  }
                }
              } catch { /* ignore */ }
              
              // 最后兜底：使用格式生成稳定哈希
              if (!detectedType) {
                const hashInput = JSON.stringify({ fmt: formats });
                currentHash = crypto.createHash('md5').update(hashInput).digest('hex');
                detectedType = 'file';
                // info('[checkClipboard] 使用格式生成文件哈希');
              }
            }
          } else {
            // info('[checkClipboard] uriRaw 为空，这可能是微信图片复制，尝试系统级检测');
            // 🔥 修复关键问题：即使 uriRaw 为空，也要尝试检测（微信图片复制的情况）
            try {
              const tmpItem = await this.detectSystemClipboardFile(crypto.randomUUID(), Date.now());
              if (tmpItem && tmpItem.items && tmpItem.items.length > 0) {
                const first = tmpItem.items[0] as any;
                if (first.content) {
                  currentHash = crypto.createHash('md5').update(first.content).digest('hex');
                  detectedType = 'file';
                  // info('[checkClipboard] 微信图片系统级检测成功');
                }
              }
            } catch (sysError) {
              // info('[checkClipboard] 系统级检测失败:', sysError.message);
            }
            
            // 🔥 针对微信图片复制的特殊处理：尝试作为图片检测
            if (!detectedType) {
              // info('[checkClipboard] 系统级检测失败，尝试作为图片处理');
              try {
                let image: NativeImage;
                try {
                  image = clipboard.readImage();
                } catch (error) {
                  // console.warn('读取剪贴板图片失败:', error.message);
                  image = nativeImage.createEmpty();
                }
                
                let buffer: Buffer | null = null;
                
                if (!image.isEmpty()) {
                  buffer = image.toPNG();
                  // info('[checkClipboard] 通过 readImage 获取微信图片数据成功');
                } else {
                                     // 尝试通过 pbpaste 获取图片数据（macOS 微信专用）
                   if (process.platform === 'darwin') {
                     try {
                      //  info('[checkClipboard] 尝试使用 pbpaste 获取微信图片');
                       const pngData = execSync('pbpaste -Prefer png', { encoding: null, timeout: 3000 }) as unknown as Buffer;
                       if (pngData && pngData.length > 0) {
                         buffer = pngData;
                        //  info(`[checkClipboard] pbpaste 成功获取微信图片，大小: ${buffer.length}`);
                       }
                     } catch (pbError) {
                      //  info('[checkClipboard] pbpaste 获取微信图片失败:', pbError.message);
                     }
                   }
                }
                
                if (buffer && buffer.length > 0) {
                  currentHash = crypto.createHash('md5').update(buffer).digest('hex');
                  detectedType = 'image';
                  // info('[checkClipboard] 微信图片检测成功，作为图片处理');
                }
              } catch (imgError) {
                // info('[checkClipboard] 图片检测失败:', imgError.message);
              }
            }
            
            // 最后兜底：生成唯一标识以确保能记录到历史
            if (!detectedType) {
              const hashInput = JSON.stringify({ fmt: formats, timestamp: Date.now() });
              currentHash = crypto.createHash('md5').update(hashInput).digest('hex');
              detectedType = 'image'; // 假设是图片
              // info('[checkClipboard] 微信复制使用时间戳兜底哈希');
            }
          }
        } catch (error) {
          // info('[checkClipboard] 图片文件检测失败:', error.message);
        }
      }
      
      // ==================== 3. 图片检测 ====================
      else if (formats.some(f => f.startsWith('image/') || ['public.png', 'public.jpeg', 'public.jpg', 'public.tiff', 'public.gif'].includes(f))) {
        // info('[checkClipboard] 检测到图片格式，开始图片检测');
        try {
          let image: NativeImage;
          try {
            image = clipboard.readImage();
          } catch (error) {
            // 处理图片读取时的 JP2 错误
            // console.warn('读取剪贴板图片失败（可能是 JP2 颜色空间问题）:', error.message);
            image = nativeImage.createEmpty();
          }
          
          let buffer: Buffer | null = null;
          
          if (!image.isEmpty()) {
            buffer = image.toPNG();
            // info('[checkClipboard] 通过 readImage 获取图片数据成功');
          } else {
            // IM 应用的图片可能需要直接读取 buffer
            try {
              for (const format of formats.filter(f => f.startsWith('image/') || f.includes('png') || f.includes('jpeg'))) {
                try {
                  buffer = clipboard.readBuffer(format);
                  if (buffer && buffer.length > 0) {
                    // info(`[checkClipboard] 通过 ${format} 格式获取图片数据成功`);
                    break;
                  }
                } catch { /* ignore */ }
              }
            } catch { /* ignore */ }
          }
          
          if (buffer && buffer.length > 0) {
            currentHash = crypto.createHash('md5').update(buffer).digest('hex');
            detectedType = 'image';
            // info('[checkClipboard] 图片检测成功');
          }
        } catch (error) {
          // info('[checkClipboard] 图片检测失败:', error.message);
        }
      }
      
      // ==================== 4. 文本检测（最低优先级）====================
      else {
        // info('[checkClipboard] 开始文本检测');
        const text = clipboard.readText();
        const plainText = text.trim();
        
        if (plainText) {
          currentHash = crypto.createHash('md5').update(plainText).digest('hex');
          detectedType = 'text';
          // info('[checkClipboard] 文本检测成功');
        }
      }

      // 未能识别任何有效内容，或哈希为空，直接结束
      if (!detectedType || !currentHash) {
        // info('[checkClipboard] 未检测到有效内容');
        return;
      }

      // 与上一次处理的剪贴板内容比较，避免重复
      if (this.lastClipboardHash === currentHash) {
        // info('[checkClipboard] 内容哈希与上次相同，跳过处理');
        return;
      }
      
      // 记录并调用分析逻辑
      // info(`[ClipboardService] 检测到新复制内容，类型: ${detectedType}, 哈希: ${currentHash.substring(0, 8)}...`);
      this.lastClipboardHash = currentHash;
      await this.analyzeClipboardContent(detectedType);
      
    } catch (error) {
      logError('[ClipboardService] checkClipboard error:', error);
    } finally {
      this.isProcessingChange = false;
    }
  }

  // 停止轮询机制
  private stopPolling() {
    if (this.pollingTimer) {
      clearInterval(this.pollingTimer);
      this.pollingTimer = null;
      info('[ClipboardService] ⏹️ 停止轮询剪切板');
    }
  }

  /**
   * 获取选中文本（通过模拟复制操作）
   * 用于全局文本选择翻译功能
   */
  async getSelectedText(): Promise<string | null> {
    try {
      // 保存当前剪贴板内容
      const originalText = clipboard.readText();
      info(`[ClipboardService] 原始剪贴板内容: ${originalText?.substring(0, 50)}...`);

      // 暂停剪贴板监控，避免干扰
      const originalPauseState = this.pauseMonitoring;
      this.pauseMonitoring = true;

      // 尝试多次复制操作，提高成功率
      let selectedText = '';
      let copySuccess = false;

      for (let attempt = 1; attempt <= 3; attempt++) {
        info(`[ClipboardService] 尝试第 ${attempt} 次复制操作`);

        // 模拟 Ctrl+C 复制选中内容
        const success = await this.simulateCopy();

        if (!success) {
          info(`[ClipboardService] 第 ${attempt} 次复制命令执行失败`);
          continue;
        }

        // 等待复制操作完成，逐渐增加等待时间
        const waitTime = 100 + (attempt - 1) * 100; // 100ms, 200ms, 300ms
        await new Promise(resolve => setTimeout(resolve, waitTime));

        // 获取复制的文本
        selectedText = clipboard.readText();
        info(`[ClipboardService] 第 ${attempt} 次获取到文本: ${selectedText?.substring(0, 50)}...`);

        // 检查是否获取到新的文本
        if (selectedText && selectedText !== originalText && selectedText.trim()) {
          copySuccess = true;
          break;
        }

        // 如果是最后一次尝试，等待更长时间
        if (attempt === 3) {
          await new Promise(resolve => setTimeout(resolve, 500));
          selectedText = clipboard.readText();
          if (selectedText && selectedText !== originalText && selectedText.trim()) {
            copySuccess = true;
          }
        }
      }

      // 恢复原始剪贴板内容
      if (originalText !== selectedText) {
        clipboard.writeText(originalText);
        info(`[ClipboardService] 已恢复原始剪贴板内容`);
      }

      // 恢复监控状态
      setTimeout(() => {
        this.pauseMonitoring = originalPauseState;
      }, 300);

      if (copySuccess) {
        info(`[ClipboardService] 获取选中文本成功: ${selectedText.substring(0, 50)}...`);
        return selectedText.trim();
      } else {
        info(`[ClipboardService] 未检测到选中文本或文本未发生变化`);
        return null;
      }

    } catch (error) {
      logError('获取选中文本失败:', error);
      this.pauseMonitoring = false; // 确保恢复监控
      return null;
    }
  }

  /**
   * 模拟复制操作（Ctrl+C）
   * 改进版本，对不同应用有更好的兼容性
   */
  private async simulateCopy(): Promise<boolean> {
    const platform = process.platform;

    return new Promise<boolean>((resolve) => {
      let command: string;
      let args: string[];

      if (platform === 'darwin') {
        // macOS - 使用更兼容的AppleScript方法
        command = 'osascript';
        args = ['-e', `
          tell application "System Events"
            -- 确保前台应用处于活动状态
            delay 0.1
            -- 发送复制快捷键
            keystroke "c" using command down
            -- 等待一小段时间确保复制完成
            delay 0.1
          end tell
        `];
      } else if (platform === 'win32') {
        // Windows - 使用改进的PowerShell方法
        command = 'powershell';
        args = ['-Command', `
          Add-Type -AssemblyName System.Windows.Forms;
          Start-Sleep -Milliseconds 50;
          [System.Windows.Forms.SendKeys]::SendWait("^c");
          Start-Sleep -Milliseconds 50;
        `];
      } else {
        // Linux - 使用xdotool，添加延迟
        command = 'bash';
        args = ['-c', 'sleep 0.05 && xdotool key ctrl+c && sleep 0.05'];
      }

      info(`[ClipboardService] 执行复制命令: ${command} ${args.join(' ')}`);

      const child = spawn(command, args, {
        stdio: 'pipe',
        detached: false
      });

      let output = '';
      let errorOutput = '';

      if (child.stdout) {
        child.stdout.on('data', (data) => {
          output += data.toString();
        });
      }

      if (child.stderr) {
        child.stderr.on('data', (data) => {
          errorOutput += data.toString();
        });
      }

      const timeout = setTimeout(() => {
        child.kill('SIGTERM');
        info(`[ClipboardService] 复制命令超时`);
        resolve(false);
      }, 2000); // 增加超时时间到2秒

      child.on('close', (code) => {
        clearTimeout(timeout);
        if (code === 0) {
          info(`[ClipboardService] 复制命令执行成功`);
        } else {
          info(`[ClipboardService] 复制命令执行失败，退出码: ${code}`);
          if (errorOutput) {
            info(`[ClipboardService] 错误输出: ${errorOutput}`);
          }
        }
        resolve(code === 0);
      });

      child.on('error', (error) => {
        clearTimeout(timeout);
        logError(`[ClipboardService] 复制命令执行错误:`, error);
        resolve(false);
      });
    });
  }

}

// 导出单例实例，确保全局只有一个 ClipboardManager
const globalAny = global as any;
if (!globalAny.__clipboardService__) {
  globalAny.__clipboardService__ = new ClipboardManager();
}
export const clipboardService: ClipboardManager = globalAny.__clipboardService__; 