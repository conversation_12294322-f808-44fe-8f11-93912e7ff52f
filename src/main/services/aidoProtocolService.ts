import { URL } from 'url'
import * as logging from '../logging'
import { MCPServer, MCPType } from '../../shared/types/mcp'
import { mcpService } from './mcpService'
import { findProjectByUUID, verifyToken, getAllProjectToolsList } from './apiService'
import { AUTH, MCP } from '../../shared/ipc'
import { WindowManager } from '../windows/window-manager'
import { mainCrossWindow } from './cross-window-communication'
import { refreshTokenApi } from '../../core/request/index'
import { getMainProcessToken } from './rendererFieldService'
import { dbService } from './dbService'
import { baseURL } from '../../core/request/index'
// 任务队列，用于存储因未登录而中断的操作
const taskQueue: (() => Promise<any>)[] = [];

// 防重复调用机制：记录正在处理的请求
const processingRequests = new Map<string, Promise<any>>();

// 监听认证成功事件，处理队列中的任务
// 使用新的统一事件系统，可以监听来自主进程内部和窗口的认证成功事件
mainCrossWindow.onUnified('auth:success', async (data, source) => {
  logging.info(`认证成功，开始处理任务队列... (来源: ${source})`);
  while (taskQueue.length > 0) {
    const task = taskQueue.shift();
    if (task) {
      try {
        await task();
      } catch (error) {
        logging.error('执行队列任务失败:', error);
      }
    }
  }
}, {
  fromMain: true,     // 监听主进程内部的认证事件
  fromWindows: true   // 也监听来自窗口的认证事件
});

/**
 * 处理 aido:// 协议请求
 * @param url 协议URL，格式：aido://server?id=xxx
 */
export async function handleAidoProtocol(url: string): Promise<any> {
  try {
    logging.info(`处理 Aido 协议请求: ${url}`)

    // 解析URL参数
    const parsedUrl = new URL(url)
    const action = parsedUrl.hostname // server
    const id = parsedUrl.searchParams.get('id')

    if (!id) {
      throw new Error('缺少必要的参数: id')
    }

    // 防重复调用：检查是否正在处理相同的请求
    const requestKey = `${action}:${id}`;

    // 如果已经在处理中，等待现有请求完成
    if (processingRequests.has(requestKey)) {
      logging.warn(`请求 ${requestKey} 正在处理中，等待现有请求完成...`);
      return await processingRequests.get(requestKey);
    }

    // 立即创建并存储Promise，防止竞态条件
    const processingPromise = handleRequestInternal(action, id);
    processingRequests.set(requestKey, processingPromise);

    return await processingPromise;
  } catch (error) {
    logging.error('处理 Aido 协议失败:', error)
    throw error
  }
}

// 内部处理函数
async function handleRequestInternal(action: string, id: string): Promise<any> {
  const requestKey = `${action}:${id}`;
  try {
    logging.info(`Aido 协议参数 - action: ${action}, id: ${id}`)

    // 目前只处理 server 动作，从Aido安装的增加标识
    if (action === 'server') {
      const result = await handleServerAction(id)
      return result
    } else {
      throw new Error(`不支持的操作: ${action}`)
    }
  } finally {
    // 处理完成后移除标记
    processingRequests.delete(requestKey);
  }
}

async function checkUserLoginStatus() {
  const token = await getMainProcessToken()
  if (!token) {
    return {
      valid: false,
      type: 'notoken'
    }
  }
  const res = await verifyToken()
  console.log('verifyToken结果', res)
  return {
    valid: res.data.valid,
    type: 'verify'
  }
}

/**
 * 处理服务器添加动作
 * @param projectId 项目ID (UUID)
 */
async function handleServerAction(projectId: string): Promise<any> {
  try {
    logging.info(`开始处理服务器添加请求: ${projectId}`)
    
    const apiData = await findProjectByUUID(projectId)

    if (apiData.data.callMethod.includes('online')) {
      logging.info('检测到在线模式，需要检查用户登录状态...')
      const loginStatus = await checkUserLoginStatus()
      console.log('登录状态', loginStatus)
      if (!loginStatus.valid) {
        // 将当前操作加入队列
        taskQueue.push(() => handleServerAction(projectId));
        logging.info(`任务已加入队列，当前队列长度: ${taskQueue.length}`);

        if (loginStatus.type === 'notoken') {
          // 1. 无token，提示用户登录
          logging.info('用户无Token，需要显示主窗口和登录弹框...');
          triggerLoginDialog();
          return { success: false, message: '需要登录，操作已加入队列等待。' };
        } else if (loginStatus.type === 'verify') {
          // 2. token失效，尝试刷新
          logging.info('用户Token已失效，尝试自动刷新...');
          try {
            await refreshTokenApi(); // 统一刷新逻辑，参数暂不传递
            logging.info('Token刷新成功，将自动重试任务。');
            mainCrossWindow.emitInMain('auth:success'); // 触发事件以执行队列
            return { success: false, message: 'Token已刷新，正在自动重试...' };
          } catch (e) {
            logging.error('Token刷新失败，需要用户重新登录:', e);
            triggerLoginDialog(); // 刷新失败，还是得弹登录框
            return { success: false, message: 'Token刷新失败，需要重新登录。' };
          }
        }
      }
    }
    
    // 将API数据转换为MCPServer格式
    const mcpServer = parseAidoMCPServer(apiData.data)
    
    // 保存到数据库
    const saveSuccess = await mcpService.addMcpServerToDb(projectId, mcpServer, true)
    
    if (saveSuccess) {
      logging.info(`MCP服务器已成功保存到数据库: ${projectId}`)
      
      // 只加载新添加的服务器，而不是重新加载所有服务器
      const loadSuccess = await mcpService.loadSingleServer(projectId, mcpServer)
      
      if (!loadSuccess) {
        logging.warn(`MCP服务器保存成功但加载失败: ${projectId}，可能需要重启应用`);
        // 不抛出错误，因为服务器已经保存到数据库，下次启动时会加载
      }
      
      // 通知渲染进程刷新MCP技能缓存
      const mainWindow = WindowManager.getMainWindow()?.getWindow()
      if (mainWindow) {
        mainWindow.webContents.send(MCP.TOOLS_UPDATED)
      }
      
      return {
        success: true,
        message: `MCP服务器 "${mcpServer.name}" 已成功添加${loadSuccess ? '并启动' : '（需重启应用生效）'}`,
        projectId,
        serverConfig: mcpServer
      }
    } else {
      throw new Error('保存MCP服务器到数据库失败')
    }
  } catch (error) {
    logging.error('处理服务器添加失败:', error)
    throw error
  }
}

/**
 * 触发登录弹窗显示的辅助函数
 */
async function triggerLoginDialog() {
  WindowManager.showMainWindow()
  const mainWindow = WindowManager.getMainWindow()?.getWindow()
  if (mainWindow) {
    if (mainWindow.webContents.isLoading()) {
      mainWindow.webContents.once('did-finish-load', () => {
        setTimeout(() => mainWindow.webContents.send(AUTH.OPEN_LOGIN_DIALOG), 200);
      });
    } else {
      mainWindow.webContents.send(AUTH.OPEN_LOGIN_DIALOG);
    }
  } else {
    logging.error('无法获取主窗口，无法发送登录弹框打开事件');
  }
}

/**
 * 将API数据转换为MCPServer格式
 * @param projectData API返回的项目数据
 */
function parseAidoMCPServer(projectData: any): MCPServer {
  // 根据callMethod确定MCP类型
  let mcpType: MCPType = 'stdio' // 默认
  let baseUrl = ""
  if (projectData.callMethod.includes('online')) {
    mcpType = 'streamableHttp'
    baseUrl += `${baseURL}${projectData.streamableHttpUrl?projectData.streamableHttpUrl:projectData.baseUrl}`
  }
  
  const mcpServer: MCPServer = {
    name: projectData.name,
    type: mcpType,
    isActive: true, // 导入的服务器默认激活
    command: projectData.command || undefined,
    description: projectData.description || undefined,
    args: projectData.args && projectData.args.length > 0 ? projectData.args : undefined,
    env: projectData.env && Object.keys(projectData.env).length > 0 ? projectData.env : undefined,
    baseUrl: baseUrl,
    headers: projectData.headers && Object.keys(projectData.headers).length > 0 ? projectData.headers : undefined,
    logoUrl: projectData.logoUrl || undefined,
    provider: projectData.provider || projectData.authorName || undefined,
    providerUrl: projectData.providerUrl || projectData.github || undefined,
    tags: projectData.tags || undefined,
    isAido: true,
  }
  
  // 移除undefined字段以保持配置清洁
  Object.keys(mcpServer).forEach(key => {
    if (mcpServer[key as keyof MCPServer] === undefined) {
      delete mcpServer[key as keyof MCPServer]
    }
  })
  
  return mcpServer
}

/**
 * 从项目数据添加MCP服务器（用于MCP技能下载功能）
 * @param projectData API返回的项目数据
 */
export async function addServerFromProject(projectData: any): Promise<{success: boolean, message?: string}> {
  try {
    logging.info(`开始从项目数据添加MCP服务器: ${projectData.name}`)

    // 将API数据转换为MCPServer格式
    const mcpServer = parseAidoMCPServer(projectData)

    // 使用项目UUID作为服务器ID
    const projectId = projectData.uuid || projectData.id

    // 保存到数据库（这会自动触发技能列表获取和保存）
    const saveSuccess = await mcpService.addMcpServerToDb(projectId, mcpServer, true)

    if (saveSuccess) {
      logging.info(`MCP服务器已成功保存到数据库: ${projectId}`)

      // 只加载新添加的服务器
      const loadSuccess = await mcpService.loadSingleServer(projectId, mcpServer)

      if (!loadSuccess) {
        logging.warn(`MCP服务器保存成功但加载失败: ${projectId}，可能需要重启应用`);
      }

      // 通知渲染进程刷新MCP技能缓存
      const mainWindow = WindowManager.getMainWindow()?.getWindow()
      if (mainWindow) {
        mainWindow.webContents.send(MCP.TOOLS_UPDATED)
      }

      return {
        success: true,
        message: `MCP服务器 "${mcpServer.name}" 已成功添加${loadSuccess ? '并启动' : '（需重启应用生效）'}`
      }
    } else {
      throw new Error('保存MCP服务器到数据库失败')
    }
  } catch (error) {
    logging.error('从项目数据添加MCP服务器失败:', error)
    return {
      success: false,
      message: `添加MCP服务器失败: ${error.message}`
    }
  }
}

/**
 * 重新激活本地MCP服务器并刷新技能列表
 * @param projectId 项目ID
 * @param serverData 本地服务器数据
 */
export async function reactivateLocalServer(projectId: string, serverData: any): Promise<{success: boolean, message?: string}> {
  try {
    logging.info(`开始重新激活本地MCP服务器: ${projectId}`)

    // 重新加载服务器
    const loadSuccess = await mcpService.loadSingleServer(projectId, serverData)

    if (loadSuccess) {
      // 重新获取和保存技能列表（确保技能信息是最新的）
      try {
        const toolsRes = await getAllProjectToolsList(projectId)
        if (toolsRes && toolsRes.data && Array.isArray(toolsRes.data)) {
          dbService.saveMcpServerTools(projectId, toolsRes.data, serverData.logoUrl)
          logging.info(`已刷新MCP服务器技能列表: ${projectId}`)
        }
      } catch (error) {
        logging.warn(`刷新技能列表失败，但服务器已激活: ${projectId}`, error)
      }

      // 通知渲染进程刷新MCP技能缓存
      const mainWindow = WindowManager.getMainWindow()?.getWindow()
      if (mainWindow) {
        mainWindow.webContents.send(MCP.TOOLS_UPDATED)
      }

      return {
        success: true,
        message: `MCP服务器 "${serverData.name}" 已成功重新激活`
      }
    } else {
      throw new Error('重新激活MCP服务器失败')
    }
  } catch (error) {
    logging.error('重新激活本地MCP服务器失败:', error)
    return {
      success: false,
      message: `重新激活MCP服务器失败: ${error.message}`
    }
  }
}