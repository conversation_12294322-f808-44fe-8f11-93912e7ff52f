import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import {
  CallToolRequestSchema,
  GetPromptRequestSchema,
  ListPromptsRequestSchema,
  ListResourcesRequestSchema,
  ListToolsRequestSchema,
  ReadResourceRequestSchema,
  Tool,
  ListToolsResultSchema,
  ListPromptsResultSchema,
  ListResourcesResultSchema,
  ReadResourceResultSchema,
  ListResourceTemplatesRequestSchema,
  ListResourceTemplatesResultSchema,
  ResourceTemplate,
  CompatibilityCallToolResultSchema,
  GetPromptResultSchema
} from "@modelcontextprotocol/sdk/types.js";
import { createClients, ConnectedClient } from './mcpClient.js';
import { MCPServer } from '../../shared/types/mcp';
import { z } from 'zod';
import * as eventsource from 'eventsource';
import { mainCrossWindow } from './cross-window-communication';
import { NotificationData } from '@shared/types/notification';
import { WindowType, WINDOW } from '../../shared/ipc';
import * as logging from '../logging';
import { WindowManager } from '../windows/window-manager';

global.EventSource = eventsource.EventSource

// 存储待处理的技能调用回调
const pendingCallbacks = new Map<string, {
  resolve: (result: any) => void;
  reject: (error: any) => void;
  request: any;
  isProcessing?: boolean; // 添加处理状态标记
}>();

export const createServer = async (servers: Record<string, MCPServer>) => {
  // Load configuration and connect to servers

  const connectedClients = await createClients(servers);
  console.log(`Connected to ${connectedClients.length} servers`);

  // Maps to track which client owns which resource
  const toolToClientMap = new Map<string, ConnectedClient>();
  const resourceToClientMap = new Map<string, ConnectedClient>();
  const promptToClientMap = new Map<string, ConnectedClient>();

  // 辅助函数：创建单个客户端
  const createSingleClient = async (key: string, server: MCPServer): Promise<ConnectedClient | null> => {
    try {
      const singleServerMap = { [key]: server };
      const singleClientArray = await createClients(singleServerMap);
      return singleClientArray.length > 0 ? singleClientArray[0] : null;
    } catch (error) {
      logging.error(`创建单个客户端失败 ${key}:`, error);
      return null;
    }
  };

  const server = new Server(
    {
      name: "mcp-proxy-server",
      version: "1.0.0",
    },
    {
      capabilities: {
        prompts: {},
        resources: { subscribe: true },
        tools: {},
      },
    },
  );

  // 动态添加单个服务器的方法
  (server as any).addSingleServer = async (key: string, serverConfig: MCPServer) => {
    try {
      logging.info(`代理服务器开始添加单个服务器: ${key} (${serverConfig.name})`);
      
      // 检查是否已存在
      const existingClient = connectedClients.find(client => client.key === key);
      if (existingClient) {
        logging.warn(`服务器 ${key} 已存在，先移除旧连接`);
        await (server as any).removeSingleServer(key);
      }
      
      // 创建新的客户端连接
      const newClient = await createSingleClient(key, serverConfig);
      if (newClient) {
        connectedClients.push(newClient);
        logging.info(`当前连接的服务器数量: ${connectedClients.length}`);

        // 立即更新技能映射，添加新服务器的技能
        try {
          const result = await newClient.client.request(
            { method: 'tools/list', params: {} },
            ListToolsResultSchema
          );

          if ((result as any).tools) {
            (result as any).tools.forEach((tool: any) => {
              const toolKey = newClient.key + '--' + tool.name;
              toolToClientMap.set(toolKey, newClient);
              logging.info(`📝 添加新技能映射: ${toolKey}`);
            });
          }
        } catch (error) {
          logging.error(`获取新服务器 ${key} 的技能列表失败:`, error);
        }
      } else {
        throw new Error(`无法创建客户端连接: ${key}`);
      }
    } catch (error) {
      logging.error(`添加服务器失败 ${key}:`, error);
      throw error;
    }
  };

  // 动态移除单个服务器的方法
  (server as any).removeSingleServer = async (key: string) => {
    try {
      logging.info(`代理服务器开始移除服务器: ${key}`);
      
      const clientIndex = connectedClients.findIndex(client => client.key === key);
      if (clientIndex === -1) {
        logging.warn(`服务器 ${key} 不存在`);
        return;
      }
      
      const clientToRemove = connectedClients[clientIndex];
      
      // 清理客户端连接
      await clientToRemove.cleanup();
      
      // 从数组中移除
      connectedClients.splice(clientIndex, 1);
      
      // 清理相关映射
      const keysToDelete: string[] = [];
      
      toolToClientMap.forEach((client, toolName) => {
        if (client.key === key) {
          keysToDelete.push(toolName);
        }
      });
      keysToDelete.forEach(k => toolToClientMap.delete(k));
      
      keysToDelete.length = 0;
      resourceToClientMap.forEach((client, resourceUri) => {
        if (client.key === key) {
          keysToDelete.push(resourceUri);
        }
      });
      keysToDelete.forEach(k => resourceToClientMap.delete(k));
      
      keysToDelete.length = 0;
      promptToClientMap.forEach((client, promptName) => {
        if (client.key === key) {
          keysToDelete.push(promptName);
        }
      });
      keysToDelete.forEach(k => promptToClientMap.delete(k));
      
      logging.info(`成功移除服务器: ${key}，当前连接数量: ${connectedClients.length}`);
    } catch (error) {
      logging.error(`移除服务器失败 ${key}:`, error);
      throw error;
    }
  };

  // 在createServer函数内部添加
  // 注册共享服务器实例的方法（不创建新实例）
  (server as any).registerSharedServer = async (key: string, sharedClient: ConnectedClient) => {
    try {
      // 检查是否已存在
      const existingIndex = connectedClients.findIndex(client => client.key === key);
      if (existingIndex !== -1) {
        logging.warn(`服务器 ${key} 已存在，替换为共享实例`);
        connectedClients[existingIndex] = sharedClient;
      } else {
        connectedClients.push(sharedClient);
      }
      
      logging.info(`当前连接的服务器数量: ${connectedClients.length}`);
    } catch (error) {
      logging.error(`注册共享服务器失败 ${key}:`, error);
      throw error;
    }
  };

  // 注销共享服务器的方法（不清理实例，只是移除引用）
  (server as any).unregisterSharedServer = async (key: string) => {
    try {
      logging.info(`代理服务器注销共享服务器: ${key}`);
      
      const clientIndex = connectedClients.findIndex(client => client.key === key);
      if (clientIndex !== -1) {
        connectedClients.splice(clientIndex, 1);
        logging.info(`服务器 ${key} 已从代理中注销，当前连接数量: ${connectedClients.length}`);
      } else {
        logging.warn(`服务器 ${key} 不存在于代理中`);
      }
    } catch (error) {
      logging.error(`注销共享服务器失败 ${key}:`, error);
      throw error;
    }
  };

  // 注册共享服务器实例的方法（不创建新实例）
  (server as any).registerSharedServer = async (key: string, sharedClient: ConnectedClient) => {
    try {
      // 检查是否已存在
      const existingIndex = connectedClients.findIndex(client => client.key === key);
      if (existingIndex !== -1) {
        logging.warn(`服务器 ${key} 已存在，替换为共享实例`);
        connectedClients[existingIndex] = sharedClient;
      } else {
        connectedClients.push(sharedClient);
      }

      // 立即更新技能映射
      try {
        const result = await sharedClient.client.request(
          { method: 'tools/list', params: {} },
          ListToolsResultSchema
        );

        if ((result as any).tools) {
          (result as any).tools.forEach((tool: any) => {
            const toolKey = sharedClient.key + '--' + tool.name;
            toolToClientMap.set(toolKey, sharedClient);
          });
        }
      } catch (error) {
        logging.error(`获取共享服务器 ${key} 的技能列表失败:`, error);
      }

      logging.info(`当前连接的服务器数量: ${connectedClients.length}`);
    } catch (error) {
      logging.error(`注册共享服务器失败 ${key}:`, error);
      throw error;
    }
  };

  // 注销共享服务器的方法（不清理实例，只是移除引用）
  (server as any).unregisterSharedServer = async (key: string) => {
    try {
      logging.info(`代理服务器注销共享服务器: ${key}`);

      const clientIndex = connectedClients.findIndex(client => client.key === key);
      if (clientIndex !== -1) {
        connectedClients.splice(clientIndex, 1);
        logging.info(`服务器 ${key} 已从代理中注销，当前连接数量: ${connectedClients.length}`);
      } else {
        logging.warn(`服务器 ${key} 不存在于代理中`);
      }
    } catch (error) {
      logging.error(`注销共享服务器失败 ${key}:`, error);
      throw error;
    }
  };

  // List Tools Handler
  server.setRequestHandler(ListToolsRequestSchema, async (request) => {
    const allTools: Tool[] = [];
    toolToClientMap.clear();

    // 并行请求所有客户端的技能，并设置超时
    const toolPromises = connectedClients.map(async (connectedClient) => {
      try {
        // 为每个客户端设置2秒超时
        const timeout = 2000;
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error(`Timeout fetching tools from ${connectedClient.name}`)), timeout);
        });

        const result = await Promise.race([
          connectedClient.client.request(
            {
              method: 'tools/list',
              params: {
                _meta: request.params?._meta
              }
            },
            ListToolsResultSchema
          ),
          timeoutPromise
        ]);

        if ((result as any).tools) {
          const toolsWithSource = (result as any).tools.map((tool: Tool) => {
            toolToClientMap.set(connectedClient.key + '--' + tool.name, connectedClient);
            if(!connectedClient.isAido){//不是aido的mcp，也就是说第三方mcp时，才返回
              return {
                ...tool,
                name: `${connectedClient.key}--${tool.name}`, //把名字覆盖为mcp--技能名
              };
            }
            // 当 isAido 为 true 时，显式返回 null 或者不返回任何内容
            return null;
          }).filter((tool: Tool) => tool !== null && tool !== undefined); // 过滤掉 null 和 undefined
          return toolsWithSource;
        }
        return [];
      } catch (error) {
        console.error(`Error fetching tools from ${connectedClient.key}:`, error);
        return []; // 返回空数组而不是中断整个流程
      }
    });

    // 等待所有技能请求完成
    const toolResults = await Promise.allSettled(toolPromises);
    
    // 收集所有成功的技能
    toolResults.forEach(result => {
      // 根据 PromiseSettledResult 类型检查状态和值
      if (result.status === 'fulfilled' && result.value && Array.isArray(result.value)) {
        allTools.push(...result.value);
      }
    });

    return { tools: allTools };
  });

  // Call Tool Handler
  server.setRequestHandler(CallToolRequestSchema, async (request) => {
    const { name, arguments: args } = request.params;
    const clientForTool = toolToClientMap.get(name);

    if (!clientForTool) {
      // 技能不存在，尝试触发下载流程
      return await handleMissingTool(name, args, request);
    }

    try {
      // 根据客户端key查找服务器配置
      const serverConfig = Object.values(servers).find((_, index) => Object.keys(servers)[index] === clientForTool.key) || 
                          { name: clientForTool.name }; // fallback
    
      console.log('Forwarding tool call:', serverConfig);
      
      // 检查当前是否在AI对话模式下
      // 通过IPC事件检查当前应用模式
      let isInAiChatMode = false;
      try {
        // 发送检查AI对话模式的事件
        const { ipcMain } = require('electron');
        // 这里可以通过其他方式检测，比如检查当前窗口状态
        // 暂时设置为true，实际实现中需要根据具体情况调整
        isInAiChatMode = true;
      } catch (error) {
        console.log('无法检测AI对话模式状态:', error);
      }
      
      // 如果在AI对话模式下，发送工具调用数据到ai-chat页面
      if (isInAiChatMode) {
        console.log('检测到AI对话模式，发送工具调用数据到ai-chat页面');
        
        // 构建工具调用数据
        const toolCallData = {
          toolName: name,
          toolArgs: args || {},
          serverName: serverConfig.name,
          status: 'calling',
          timestamp: Date.now()
        };
        
        // 发送工具调用开始事件到主窗口
        mainCrossWindow.emit('ai-chat:tool-call-start', toolCallData, [WindowType.MAIN]);
      }
      
      // 使用新的跨窗口通信服务发送技能调用通知
      const notification: NotificationData = {
        id: Math.random().toString(), // 临时ID
        type: 'info',
        title: '技能调用',
        detail: `使用技能: ${serverConfig.name}`,
        duration: 3000
      };

      // 使用统一的跨窗口通信服务，发送到悬浮球和宠物窗口
      mainCrossWindow.emit('notification:show', notification, [
        WindowType.FLOATING_BALL,
        WindowType.PET
      ]);

      // Use the correct schema for tool calls
      const result = await clientForTool.client.request(
        {
          method: 'tools/call',
          params: {
            name: name.split('--')[1],
            arguments: args || {},
            _meta: {
              progressToken: request.params._meta?.progressToken
            }
          }
        },
        CompatibilityCallToolResultSchema
      );
      
      // 如果在AI对话模式下，发送工具调用完成事件
      if (isInAiChatMode) {
        console.log('工具调用完成，发送结果到ai-chat页面');
        
        // 构建工具调用结果数据
        const toolCallResult = {
          toolName: name,
          toolArgs: args || {},
          serverName: serverConfig.name,
          result: result,
          status: 'completed',
          timestamp: Date.now()
        };
        
        // 发送工具调用完成事件到主窗口
        mainCrossWindow.emit('ai-chat:tool-call-complete', toolCallResult, [WindowType.MAIN]);
      }
      
      return result;
    } catch (error) {
      console.error(`Error calling tool through ${clientForTool.name}:`, error);
      
      // 如果在AI对话模式下，发送工具调用错误事件
      try {
        let isInAiChatMode = false;
        try {
          mainCrossWindow.emit('check:ai-chat-mode', {}, [WindowType.MAIN]);
          isInAiChatMode = true;
        } catch (checkError) {
          console.log('无法检测AI对话模式状态:', checkError);
        }
        
        if (isInAiChatMode) {
          console.log('工具调用失败，发送错误信息到ai-chat页面');
          
          // 构建工具调用错误数据
          const toolCallError = {
            toolName: name,
            toolArgs: args || {},
            serverName: clientForTool.name,
            error: error.message || '工具调用失败',
            status: 'error',
            timestamp: Date.now()
          };
          
          // 发送工具调用错误事件到主窗口
          mainCrossWindow.emit('ai-chat:tool-call-error', toolCallError, [WindowType.MAIN]);
        }
      } catch (emitError) {
        console.error('发送工具调用错误事件失败:', emitError);
      }
      
      throw error;
    }
  });

  // Get Prompt Handler
  server.setRequestHandler(GetPromptRequestSchema, async (request) => {
    const { name } = request.params;
    const clientForPrompt = promptToClientMap.get(name);

    if (!clientForPrompt) {
      throw new Error(`Unknown prompt: ${name}`);
    }

    try {
      console.log('Forwarding prompt request:', name);

      // Match the exact structure from the example code
      const response = await clientForPrompt.client.request(
        {
          method: 'prompts/get' as const,
          params: {
            name: name.split('--')[1],
            arguments: request.params.arguments || {},
            _meta: request.params._meta || {
              progressToken: undefined
            }
          }
        },
        GetPromptResultSchema
      );

      console.log('Prompt result:', response);
      return response;
    } catch (error) {
      console.error(`Error getting prompt from ${clientForPrompt.name}:`, error);
      throw error;
    }
  });

  // List Prompts Handler
  server.setRequestHandler(ListPromptsRequestSchema, async (request) => {
    const allPrompts: z.infer<typeof ListPromptsResultSchema>['prompts'] = [];
    promptToClientMap.clear();

    for (const connectedClient of connectedClients) {
      try {
        const result = await connectedClient.client.request(
          {
            method: 'prompts/list' as const,
            params: {
              cursor: request.params?.cursor,
              _meta: request.params?._meta || {
                progressToken: undefined
              }
            }
          },
          ListPromptsResultSchema
        );

        if (result.prompts) {
          const promptsWithSource = result.prompts.map(prompt => {
            promptToClientMap.set(connectedClient.key + '--' + prompt.name, connectedClient);
            return {
              ...prompt,
              name: `${connectedClient.key}--${prompt.name}`,
              description: `${prompt.description || ''}`
            };
          });
          allPrompts.push(...promptsWithSource);
        }
      } catch (error) {
        console.error(`Error fetching prompts from ${connectedClient.name}:`, error);
      }
    }

    return {
      prompts: allPrompts,
      nextCursor: request.params?.cursor
    };
  });

  // List Resources Handler
  server.setRequestHandler(ListResourcesRequestSchema, async (request) => {
    const allResources: z.infer<typeof ListResourcesResultSchema>['resources'] = [];
    resourceToClientMap.clear();

    for (const connectedClient of connectedClients) {
      try {
        const result = await connectedClient.client.request(
          {
            method: 'resources/list',
            params: {
              cursor: request.params?.cursor,
              _meta: request.params?._meta
            }
          },
          ListResourcesResultSchema
        );

        if (result.resources) {
          const resourcesWithSource = result.resources.map(resource => {
            resourceToClientMap.set(connectedClient.key + '--' + resource.uri, connectedClient);
            return {
              ...resource,
              name: `${connectedClient.key}--${resource.name || ''}`
            };
          });
          allResources.push(...resourcesWithSource);
        }
      } catch (error) {
        console.error(`Error fetching resources from ${connectedClient.name}:`, error);
      }
    }

    return {
      resources: allResources,
      nextCursor: undefined
    };
  });

  // Read Resource Handler
  server.setRequestHandler(ReadResourceRequestSchema, async (request) => {
    const { uri } = request.params;
    const clientForResource = resourceToClientMap.get(uri);

    if (!clientForResource) {
      throw new Error(`Unknown resource: ${uri}`);
    }

    try {
      return await clientForResource.client.request(
        {
          method: 'resources/read',
          params: {
            uri,
            _meta: request.params._meta
          }
        },
        ReadResourceResultSchema
      );
    } catch (error) {
      console.error(`Error reading resource from ${clientForResource.name}:`, error);
      throw error;
    }
  });

  // List Resource Templates Handler
  server.setRequestHandler(ListResourceTemplatesRequestSchema, async (request) => {
    const allTemplates: ResourceTemplate[] = [];

    for (const connectedClient of connectedClients) {
      try {
        const result = await connectedClient.client.request(
          {
            method: 'resources/templates/list' as const,
            params: {
              cursor: request.params?.cursor,
              _meta: request.params?._meta || {
                progressToken: undefined
              }
            }
          },
          ListResourceTemplatesResultSchema
        );

        if (result.resourceTemplates) {
          const templatesWithSource = result.resourceTemplates.map(template => ({
            ...template,
            name: `${connectedClient.key}--${template.name || ''}`,
            description: template.description ? `${template.description}` : undefined
          }));
          allTemplates.push(...templatesWithSource);
        }
      } catch (error) {
        console.error(`Error fetching resource templates from ${connectedClient.name}:`, error);
      }
    }

    return {
      resourceTemplates: allTemplates,
      nextCursor: request.params?.cursor
    };
  });

  const cleanup = async () => {
    await Promise.all(connectedClients.map(({ cleanup }) => cleanup()));
  };

  return { server, cleanup };
};

/**
 * 处理缺失的技能，触发下载流程
 */
async function handleMissingTool(toolName: string, toolArgs: any, request: any): Promise<any> {
  // 从技能名称中提取projectUUId
  let projectUUId: string | null = null;
  if (toolName.includes('--')) {
    projectUUId = toolName.split('--')[0];
  }

  // 生成回调ID
  const callbackId = `tool-download-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

  logging.info(`技能 ${toolName} 不存在，触发下载流程，回调ID: ${callbackId}`);

  // 创建Promise来等待下载完成
  return new Promise((resolve, reject) => {
    // 存储回调
    pendingCallbacks.set(callbackId, {
      resolve,
      reject,
      request
    });

    // 异步执行下载流程
    (async () => {
      // 1. 显示主窗口
      WindowManager.showMainWindow();

      // 2. 等待一小段时间确保窗口显示完成
      await new Promise(resolve => setTimeout(resolve, 200));

      // 3. 发送切换到AI聊天的事件
      const mainWindow = WindowManager.getMainWindow();
      if (mainWindow && !mainWindow.getWindow().isDestroyed()) {
        mainWindow.getWindow().webContents.send(WINDOW.SWITCH_TO_AI_CHAT);
      }

      // 4. 再等待一小段时间确保AI聊天模式加载完成
      await new Promise(resolve => setTimeout(resolve, 300));

      // 5. 发送技能下载请求事件，包含特殊标识表示这是自动触发的
      mainCrossWindow.emit('mcp:tool-download-request', {
        toolName,
        toolArgs,
        callbackId,
        projectUUId,
        autoTriggered: true // 标识这是自动触发的下载请求
      });
    })().catch(error => {
      // 如果异步操作出错，清理回调并拒绝Promise
      pendingCallbacks.delete(callbackId);
      reject(error);
    });

    // 设置超时（5分钟）
    setTimeout(() => {
      if (pendingCallbacks.has(callbackId)) {
        pendingCallbacks.delete(callbackId);
        // 使用reject来阻止AI重试其他技能
        reject(new Error(`MCP技能 ${toolName} 下载超时（5分钟）。请检查网络连接或稍后重试。`));
      }
    }, 5 * 60 * 1000);
  });
}

// 监听MCP技能下载完成事件
mainCrossWindow.on('mcp:tool-download-completed', async (data: {
  toolName?: string;
  originalToolName?: string;
  success: boolean;
  callbackId: string;
  serverInfo?: any;
  skipActivation?: boolean;
}) => {
  console.log('🎉 收到MCP技能下载完成事件:');
  logging.info('收到MCP技能下载完成事件:');

  // 修复技能名称丢失问题
  const toolName = data.toolName || data.originalToolName;
  const { success, callbackId } = data;

  console.log('🔍 提取的技能名称:', toolName);

  console.log('🔍 查找待处理的回调:', callbackId);
  console.log('📋 当前待处理的回调列表:', Array.from(pendingCallbacks.keys()));

  const pendingCallback = pendingCallbacks.get(callbackId);

  if (pendingCallback) {
    console.log('✅ 找到待处理的回调，开始处理');

    if (success) {
      // 下载成功，直接返回成功消息
      console.log(`✅ MCP技能 ${toolName} 下载完成，返回成功消息`);
      pendingCallbacks.delete(callbackId);
      pendingCallback.resolve({
        content: [{
          type: 'text',
          text: `MCP技能 "${toolName}" 安装完成！请重新发送您的请求。`
        }],
        isError: false
      });

    } else {
      // 下载失败，删除回调并返回错误
      pendingCallbacks.delete(callbackId);
      pendingCallback.resolve({
        content: [{
          type: 'text',
          text: `MCP技能 ${toolName} 下载失败或被用户取消。请稍后重试或联系管理员。`
        }],
        isError: true
      });
    }
  } else {
    console.log('❌ 未找到对应的回调:', callbackId);
    console.log('📋 当前待处理的回调列表:', Array.from(pendingCallbacks.keys()));
  }
});

// 监听MCP服务器下载完成事件
mainCrossWindow.on('mcp:server-download-completed', (data: {
  success: boolean;
  callbackId: string;
  originalToolName?: string;
  serverInfo?: any;
}) => {
  logging.info('收到MCP服务器下载完成事件:', data.callbackId);

  const callback = pendingCallbacks.get(data.callbackId);
  if (!callback) {
    logging.warn('未找到对应的回调:', data.callbackId);
    return;
  }

  // 检查是否已经在处理中，防止重复处理
  if (callback.isProcessing) {
    logging.warn('回调已在处理中，跳过重复处理:', data.callbackId);
    return;
  }

  // 标记为处理中，但不立即删除回调
  callback.isProcessing = true;
  logging.info('开始处理回调:', data.callbackId);

  if (data.success) {
    // MCP服务器安装成功，重新加载MCP服务以识别新技能
    logging.info('MCP服务器安装成功，重新加载MCP服务...');

    // 如果是跳过激活的情况（服务器已经在运行），立即执行
    const skipActivation = (data as any).skipActivation;
    const delay = skipActivation ? 0 : 3000;
    console.log(`⏱️ ${skipActivation ? '服务器已运行，立即重新加载' : '等待3秒确保服务器完全激活'}`);

    // 等待一小段时间确保服务器完全激活
    setTimeout(async () => {
      try {
        // 不需要重新加载MCP服务，因为 addServerFromProject 已经调用了 loadSingleServer
        // 只需要标记处理完成，等待 mcp:tool-download-completed 事件
        logging.info('MCP服务器安装完成，等待技能下载完成事件');

        // 标记处理完成，等待 mcp:tool-download-completed 事件来最终处理回调
        callback.isProcessing = false;

      } catch (error) {
        logging.error('处理MCP服务器安装完成时出错:', error);
        // 如果出错，直接处理回调
        pendingCallbacks.delete(data.callbackId);
        callback.resolve({
          content: [{
            type: 'text',
            text: `MCP服务器安装完成，但处理时出现错误。请重新发送您的请求。`
          }],
          isError: false
        });
      }
    }, delay);
  } else {
    // 用户取消或下载失败
    callback.reject(new Error(`MCP服务器下载已取消。用户选择不安装 ${data.originalToolName?.split('--')[0] || 'MCP'} 服务器，因此相关的所有技能都不可用。`));
  }
});






