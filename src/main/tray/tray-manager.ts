import { Tray, Menu, app, nativeImage } from 'electron';
import path from 'path';
import { APP_INFO, isDevelopment } from '../common';
import { WindowManager } from '../windows/window-manager';

/**
 * 托盘管理器
 * 负责托盘图标的创建和管理
 */
export class TrayManager {
  private static tray: Tray | null = null;
  
  /**
   * 初始化托盘图标
   */
  public static init(): void {
    if (this.tray) return;
    
    try {
      // 创建托盘图标
      const iconPath = path.join(app.getAppPath(), isDevelopment 
        ? 'src/assets/icons/icon.png' 
        : 'assets/icons/icon.png');
      
      const trayIcon = nativeImage.createFromPath(iconPath).resize({ width: 16, height: 16 });
      this.tray = new Tray(trayIcon);
      
      // 设置菜单
      this.setMenu();
      
      // 设置工具提示
      this.tray.setToolTip(APP_INFO.name);
      
      // 双击打开应用
      this.tray.on('double-click', () => {
        WindowManager.showMainWindow();
      });
      
      console.log('托盘初始化成功');
    } catch (error) {
      console.error('初始化托盘失败:', error);
    }
  }
  
  /**
   * 设置托盘菜单
   */
  private static setMenu(): void {
    if (!this.tray) return;
    
    const contextMenu = Menu.buildFromTemplate([
      { 
        label: '显示', 
        click: () => WindowManager.showMainWindow() 
      },
      { 
        label: '隐藏', 
        click: () => WindowManager.hideMainWindow() 
      },
      { type: 'separator' },
      { 
        label: '退出', 
        click: () => {
          // 强制退出应用，确保所有进程都被终止
          TrayManager.forceQuit();
        }
      }
    ]);
    
    this.tray.setContextMenu(contextMenu);
  }
  
  /**
   * 强制退出应用
   */
  public static forceQuit(): void {
    console.log('强制退出应用...');
    
    // 立即清理托盘资源
    this.cleanup();
    
    // 关闭所有窗口
    WindowManager.closeAllWindows();
    
    // 强制退出应用
    app.exit(0);
  }
  
  /**
   * 清理托盘资源
   */
  public static cleanup(): void {
    if (this.tray) {
      this.tray.destroy();
      this.tray = null;
    }
  }
} 