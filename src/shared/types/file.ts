// src/types/file.ts
export interface FileInfo {
  id: string; // 来自 main/services/fileService.ts
  name: string; // 通用
  path: string; // 通用
  size: number; // 通用
  mtime: number; // 使用 main/services/fileService.ts 的 mtime (修改时间)
  extension: string | null; // 来自 main/services/fileService.ts, 允许 null 以兼容文件夹
  isDirectory: boolean; // 来自 renderer/types/app.ts
  // 保留可选字段
  icon?: string;
  lastOpened?: number;
  // 'type' 字段可以被 isDirectory 和 extension 替代，暂不包含以简化
} 