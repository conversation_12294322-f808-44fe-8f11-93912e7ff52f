/**
 * MCP工具接口定义
 * 用于主进程和渲染进程共享MCP工具的数据结构
 */
export interface MCPTool {
  id: string;
  c_name?: string;
  description?: string;
  descriptionChinese?: string;
  fullName: string;
  inputSchema?: any;
  name?: string;
  outputSchema?: any;
  points?: number;
  projectUUId?: string;
  regex?: string;
  logoUrl?: string;
  is_single_call?: number;
  supportedExtensions?: string[];
  multiFileType?: number;
  keywords?: string;
  platform?: string;
}