export interface MCPServer {
  name: string; // 名称
  type: MCPType; // 类型
  isActive: boolean; // 是否激活
  command?: string; // 命令
  description?: string; // 描述
  args?: string[]; // 参数
  env?: Record<string, string>; // 环境变量
  baseUrl?: string; // 在线请求的url
  headers?: Record<string, string>; // 请求头
  logoUrl?: string; // 图标
  provider?: string; // 提供者
  providerUrl?: string; // 提供者url
  tags?: string[]; // 标签
  isAido: boolean; // 是否是aido商城的mcp
}

export type MCPType = 'stdio' | 'sse' | 'streamableHttp' | 'inMemory';
export type MCPArgType = 'string' | 'list' | 'number';
export type MCPEnvType = 'string' | 'number';
export type MCPArgParameter = { [key: string]: MCPArgType };
export type MCPEnvParameter = { [key: string]: MCPEnvType };

export interface MCPServerParameter {
  name: string;
  type: MCPArgType | MCPEnvType;
  description: string;
}

export interface MCPConfig {
  servers: Record<string, MCPServer>; // 以 key 为索引的对象结构
  updated?: number;
}
