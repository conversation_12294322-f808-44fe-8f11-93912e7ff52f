/**
 * 跨窗口通信类型定义
 * 提供完整的类型安全支持
 */

import { WindowType, CrossWindowEventListener } from '../ipc';

// ================= 事件类型定义 =================

/**
 * 预定义的跨窗口事件类型
 * 可以根据项目需要扩展
 */
export interface CrossWindowEvents {
  // 用户相关事件
  'user:login': { userId: string; username: string; token: string };
  'user:logout': { userId: string };
  'user:profile-updated': { userId: string; profile: any };

  // 设置相关事件
  'settings:theme-changed': { theme: 'light' | 'dark' | 'auto' };
  'settings:language-changed': { language: string };
  'settings:updated': { section: string; settings: any };

  // 剪贴板相关事件
  'clipboard:item-added': { item: any };
  'clipboard:item-selected': { item: any };
  'clipboard:history-cleared': {};

  // 窗口相关事件
  'window:focus-changed': { windowType: WindowType; focused: boolean };
  'window:size-changed': { windowType: WindowType; width: number; height: number };
  'window:position-changed': { windowType: WindowType; x: number; y: number };

  // 应用状态事件
  'app:ready': {};
  'app:before-quit': {};
  'app:error': { error: string; details?: any };

  // 数据同步事件
  'data:sync-required': { dataType: string };
  'data:sync-completed': { dataType: string; success: boolean };

  // 通知事件
  'notification:show': { title: string; message: string; type: 'info' | 'success' | 'warning' | 'error' };
  'notification:action': { notificationId: string; action: string };

  // MCP技能下载相关事件
  'mcp:tool-download-request': {
    toolName: string;
    toolArgs: any;
    callbackId: string;
    message: string;
  };
  'mcp:tool-download-completed': {
    toolName: string;
    success: boolean;
    callbackId: string;
  };

  // 自定义事件（允许任意数据）
  [eventName: string]: any;
}

// ================= 工具类型 =================

/**
 * 获取事件数据类型
 */
export type EventData<T extends keyof CrossWindowEvents> = CrossWindowEvents[T];

/**
 * 类型安全的事件监听器
 */
export type TypedEventListener<T extends keyof CrossWindowEvents> = CrossWindowEventListener<EventData<T>>;

/**
 * 事件发送选项
 */
export interface EmitOptions {
  /** 目标窗口，不指定则广播到所有窗口 */
  target?: WindowType | WindowType[];
  /** 是否等待确认 */
  waitForAck?: boolean;
  /** 超时时间（毫秒） */
  timeout?: number;
  /** 事件优先级 */
  priority?: 'low' | 'normal' | 'high';
}

/**
 * 事件监听选项
 */
export interface ListenOptions {
  /** 是否只监听一次 */
  once?: boolean;
  /** 事件过滤器 */
  filter?: (data: any, sourceWindow: WindowType) => boolean;
  /** 监听器优先级 */
  priority?: 'low' | 'normal' | 'high';
}

// ================= 高级 API 类型 =================

/**
 * 增强的跨窗口通信 API
 */
export interface EnhancedCrossWindowAPI {
  // 基础 API
  emit<T extends keyof CrossWindowEvents>(
    eventName: T,
    data: EventData<T>,
    options?: EmitOptions
  ): Promise<boolean>;

  on<T extends keyof CrossWindowEvents>(
    eventName: T,
    listener: TypedEventListener<T>,
    options?: ListenOptions
  ): () => void;

  off<T extends keyof CrossWindowEvents>(
    eventName: T,
    listener?: TypedEventListener<T>
  ): void;

  // 高级 API
  once<T extends keyof CrossWindowEvents>(
    eventName: T,
    listener: TypedEventListener<T>
  ): () => void;

  broadcast<T extends keyof CrossWindowEvents>(
    eventName: T,
    data: EventData<T>
  ): Promise<boolean>;

  // 请求-响应模式
  request<TReq extends keyof CrossWindowEvents, TRes = any>(
    eventName: TReq,
    data: EventData<TReq>,
    target: WindowType,
    timeout?: number
  ): Promise<TRes>;

  respond<T extends keyof CrossWindowEvents>(
    eventName: T,
    handler: (data: EventData<T>, sourceWindow: WindowType) => any | Promise<any>
  ): () => void;

  // 窗口管理
  getCurrentWindowType(): WindowType;
  getActiveWindows(): Promise<WindowType[]>;
  isWindowActive(windowType: WindowType): Promise<boolean>;

  // 事件统计
  getEventStats(): Promise<{
    sent: number;
    received: number;
    listeners: number;
    errors: number;
  }>;
}

// ================= 错误类型 =================

/**
 * 跨窗口通信错误
 */
export class CrossWindowError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'CrossWindowError';
  }
}

/**
 * 错误代码枚举
 */
export enum CrossWindowErrorCode {
  WINDOW_NOT_FOUND = 'WINDOW_NOT_FOUND',
  TIMEOUT = 'TIMEOUT',
  INVALID_DATA = 'INVALID_DATA',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
}

// ================= 配置类型 =================

/**
 * 跨窗口通信配置
 */
export interface CrossWindowConfig {
  /** 默认超时时间（毫秒） */
  defaultTimeout: number;
  /** 是否启用调试日志 */
  enableDebugLog: boolean;
  /** 最大监听器数量 */
  maxListeners: number;
  /** 事件队列大小 */
  eventQueueSize: number;
  /** 是否启用事件统计 */
  enableStats: boolean;
}

// ================= 事件元数据 =================

/**
 * 事件元数据
 */
export interface EventMetadata {
  /** 事件 ID */
  id: string;
  /** 事件名称 */
  name: string;
  /** 发送时间戳 */
  timestamp: number;
  /** 源窗口 */
  sourceWindow: WindowType;
  /** 目标窗口 */
  targetWindow?: WindowType | WindowType[];
  /** 事件大小（字节） */
  size: number;
  /** 是否为广播事件 */
  isBroadcast: boolean;
}

/**
 * 事件处理结果
 */
export interface EventResult {
  /** 是否成功 */
  success: boolean;
  /** 错误信息 */
  error?: string;
  /** 处理时间（毫秒） */
  duration: number;
  /** 响应数据 */
  response?: any;
}
