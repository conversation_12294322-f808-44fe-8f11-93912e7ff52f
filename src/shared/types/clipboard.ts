export interface ClipboardBase {
  /** 来源应用程序，例如 WeChat、Chrome */
  source?: string;
  /** MIME 类型或通用内容类型，例如 text/plain, image/png */
  contentType?: string;
}

export interface ClipboardFileItem extends ClipboardBase {
  name: string;
  content: string; // 文件完整路径
  type: string; // 文件扩展名
  size: number; // 字节数
  path?: string; // 与 content 相同，但语义化字段
  thumbnail?: string; // 缩略图 base64 数据URL
}

export interface ClipboardImageItem extends ClipboardBase {
  name?: string;
  content?: string;
  size?: number;
  dataURL?: string;
  width?: number;
  height?: number;
}

export interface ClipboardTextItem extends ClipboardBase {
  content: string;
  characters?: number;
  words?: number;
}

export interface ClipboardDirectoryItem extends ClipboardBase {
  name: string;
  content: string;
  type: 'directory';
  path?: string;
}

export type ClipboardItemContent = 
  | ClipboardTextItem 
  | ClipboardImageItem 
  | ClipboardFileItem
  | ClipboardDirectoryItem;

export interface ClipboardItem {
  id: string;
  type: 'text' | 'image' | 'file' | 'directory' | 'multiple'; // 剪贴板类型
  items: ClipboardItemContent[];
  timestamp: number;
  count: number;
  /** 来源应用程序 */
  source?: string;
  /** 项目级别的内容类型 */
  contentType?: string;
}