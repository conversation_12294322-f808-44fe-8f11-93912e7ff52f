/**
 * 系统快捷键检查模块
 * 提供跨平台的系统快捷键检查功能
 */

export interface SystemShortcutChecker {
  isSystemShortcut: (shortcut: string) => boolean;
  getSystemShortcuts: () => string[];
  getPlatform: () => 'mac' | 'windows' | 'linux';
}

/**
 * macOS 系统快捷键列表
 */
const MAC_SYSTEM_SHORTCUTS = [
  // 基础编辑快捷键
  'Cmd+C', 'Cmd+V', 'Cmd+X', 'Cmd+Z', 'Cmd+Shift+Z', 'Cmd+Y',
  'Cmd+A', 'Cmd+S', 'Cmd+O', 'Cmd+N', 'Cmd+P', 'Cmd+F', 'Cmd+G', 'Cmd+Shift+G',
  
  // 窗口和应用管理
  'Cmd+Q', 'Cmd+W', 'Cmd+M', 'Cmd+H', 'Cmd+Option+H',
  'Cmd+Tab', 'Cmd+Shift+Tab', 'Cmd+`',
  
  // 全屏和显示
  'Ctrl+Cmd+F', 'Cmd+Ctrl+F', 'F11',
  // 注意：Cmd+Ctrl+F 是 macOS 的全屏快捷键
  
  // Spotlight 和搜索
  'Cmd+Space', 'Cmd+Option+Space',
  
  // 系统功能
  'Cmd+Option+Esc', 'Cmd+Shift+3', 'Cmd+Shift+4', 'Cmd+Shift+5',
  'Cmd+Ctrl+Q', 'Cmd+Option+Ctrl+Eject',
  
  // Mission Control 和 Spaces
  'Ctrl+Up', 'Ctrl+Down', 'Ctrl+Left', 'Ctrl+Right',
  'F3', 'F4',
  
  // 音量和亮度
  'F1', 'F2', 'F10', 'F11', 'F12',
  
  // 辅助功能
  'Cmd+Option+F5', 'Cmd+F5',
  
  // Dock
  'Cmd+Option+D',
  
  // 强制退出
  'Cmd+Option+Shift+Esc',
  
  // 锁屏
  'Cmd+Ctrl+Q',
  
  // 显示桌面
  'F11',
  
  // 查找
  'Cmd+E', 'Cmd+J',
  
  // Safari 和浏览器常用
  'Cmd+R', 'Cmd+Shift+R', 'Cmd+T', 'Cmd+Shift+T',
  'Cmd+L', 'Cmd+D', 'Cmd+B', 'Cmd+Shift+B',
  
  // 开发者工具
  'Cmd+Option+I', 'Cmd+Option+J', 'Cmd+Option+C',
  
  // 系统偏好设置
  'Cmd+,',
  
  // 最小化所有窗口
  'Cmd+Option+M',
  
  // 隐藏其他应用
  'Cmd+Option+H',
  
  // 退出所有应用
  'Cmd+Option+Q',
];

/**
 * Windows 系统快捷键列表
 */
const WINDOWS_SYSTEM_SHORTCUTS = [
  // 基础编辑快捷键
  'Ctrl+C', 'Ctrl+V', 'Ctrl+X', 'Ctrl+Z', 'Ctrl+Y',
  'Ctrl+A', 'Ctrl+S', 'Ctrl+O', 'Ctrl+N', 'Ctrl+P', 'Ctrl+F', 'Ctrl+H',
  
  // 窗口和应用管理
  'Alt+F4', 'Alt+Tab', 'Alt+Shift+Tab', 'Ctrl+Shift+Esc',
  'Ctrl+Alt+Del', 'Win+L', 'Win+D', 'Win+M',
  
  // Windows 键组合
  'Win+E', 'Win+R', 'Win+S', 'Win+I', 'Win+A', 'Win+X',
  'Win+Tab', 'Win+Ctrl+D', 'Win+Ctrl+Left', 'Win+Ctrl+Right',
  'Win+Left', 'Win+Right', 'Win+Up', 'Win+Down',
  
  // 全屏
  'F11', 'Alt+Enter',
  
  // 截图
  'Win+Shift+S', 'PrtScn', 'Alt+PrtScn',
  
  // 任务管理器
  'Ctrl+Shift+Esc',
  
  // 虚拟桌面
  'Win+Ctrl+D', 'Win+Ctrl+F4',
  
  // 系统音量
  'Win+Plus', 'Win+Minus',
  
  // 通知中心
  'Win+A',
  
  // 设置
  'Win+I',
  
  // 搜索
  'Win+S', 'Ctrl+Shift+F',
  
  // 运行对话框
  'Win+R',
  
  // 系统信息
  'Win+Pause',
  
  // 文件资源管理器
  'Win+E',
  
  // 投影
  'Win+P',
  
  // 连接
  'Win+K',
  
  // 游戏栏
  'Win+G',
  
  // 语音输入
  'Win+H',
  
  // 剪贴板历史
  'Win+V',
  
  // 表情符号面板
  'Win+.',
];

/**
 * Linux 系统快捷键列表
 */
const LINUX_SYSTEM_SHORTCUTS = [
  // 基础编辑快捷键
  'Ctrl+C', 'Ctrl+V', 'Ctrl+X', 'Ctrl+Z', 'Ctrl+Y',
  'Ctrl+A', 'Ctrl+S', 'Ctrl+O', 'Ctrl+N', 'Ctrl+P', 'Ctrl+F',
  
  // 窗口管理
  'Alt+F4', 'Alt+Tab', 'Alt+Shift+Tab',
  'Super+Left', 'Super+Right', 'Super+Up', 'Super+Down',
  
  // 工作区切换
  'Ctrl+Alt+Left', 'Ctrl+Alt+Right', 'Ctrl+Alt+Up', 'Ctrl+Alt+Down',
  
  // 全屏
  'F11',
  
  // 截图
  'PrtScn', 'Alt+PrtScn', 'Shift+PrtScn',
  
  // 终端
  'Ctrl+Alt+T',
  
  // 锁屏
  'Ctrl+Alt+L', 'Super+L',
  
  // 系统监视器
  'Ctrl+Shift+Esc',
  
  // 运行对话框
  'Alt+F2',
  
  // 显示应用
  'Super+A', 'Super+S',
  
  // 文件管理器
  'Super+E',
  
  // 注销
  'Ctrl+Alt+Del',
  
  // 重启
  'Ctrl+Alt+Del',
  
  // 显示桌面
  'Super+D',
  
  // 最小化窗口
  'Super+H',
  
  // 最大化窗口
  'Super+Up',
  
  // 恢复窗口
  'Super+Down',
];

/**
 * 检测当前平台
 */
function detectPlatform(): 'mac' | 'windows' | 'linux' {
  if (typeof process !== 'undefined' && process.platform) {
    // Node.js 环境（主进程）
    switch (process.platform) {
      case 'darwin':
        return 'mac';
      case 'win32':
        return 'windows';
      case 'linux':
        return 'linux';
      default:
        return 'linux';
    }
  } else if (typeof navigator !== 'undefined') {
    // 浏览器环境（渲染进程）
    const platform = navigator.platform.toLowerCase();
    if (platform.includes('mac')) {
      return 'mac';
    } else if (platform.includes('win')) {
      return 'windows';
    } else {
      return 'linux';
    }
  }
  
  return 'linux'; // 默认
}

/**
 * 创建系统快捷键检查器
 */
export function createSystemShortcutChecker(): SystemShortcutChecker {
  const platform = detectPlatform();
  
  const getSystemShortcuts = (): string[] => {
    switch (platform) {
      case 'mac':
        return [...MAC_SYSTEM_SHORTCUTS];
      case 'windows':
        return [...WINDOWS_SYSTEM_SHORTCUTS];
      case 'linux':
        return [...LINUX_SYSTEM_SHORTCUTS];
      default:
        return [...LINUX_SYSTEM_SHORTCUTS];
    }
  };
  
  const isSystemShortcut = (shortcut: string): boolean => {
    const systemShortcuts = getSystemShortcuts();
    return systemShortcuts.includes(shortcut);
  };
  
  const getPlatform = (): 'mac' | 'windows' | 'linux' => {
    return platform;
  };
  
  return {
    isSystemShortcut,
    getSystemShortcuts,
    getPlatform,
  };
}

// 导出默认实例
export const systemShortcutChecker = createSystemShortcutChecker();
