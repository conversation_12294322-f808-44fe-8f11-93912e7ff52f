@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5.9% 10%;

    --success: 142 71.8% 29.2%;
    --success-foreground: 0 0% 98%;
    --info: 217 91.2% 59.8%;
    --info-foreground: 0 0% 98%;
    --warning: 38 92.7% 50.2%;
    --warning-foreground: 0 0% 98%;
    --active: 217 91.2% 59.8%;
    --active-foreground: 0 0% 98%;

    --radius: 0.5rem;

    --transition-fastest: 100ms;
    --transition-faster: 150ms;
    --transition-fast: 200ms;
    --transition-normal: 300ms;
    --transition-slow: 500ms;
    --transition-slower: 700ms;
    --transition-slowest: 1000ms;
  }
  
  /* 设置根元素高度并禁止滚动条 */
  html, body, #root {
    height: 100%;
    overflow: hidden;
  }
 
  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 90%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
} 

/* 应用主体样式 */
.app-body {
  @apply bg-background text-foreground;
}

/* 悬浮球全局样式 */
.floating-ball {
  position: fixed;
  bottom: 40px;
  right: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  transition: all 0.3s ease;
}

.floating-ball img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
}

/* 防止拖拽图片 */
.floating-ball img {
  -webkit-user-drag: none;
  user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}

/* 颜色显示组件样式 */
.colorDisplayWrap {
  padding: 10px;

}

.colorDisplay {
  background-color: #f0f0f0;
  border-radius: 4px;
  padding: 10px;
}

/* 音频播放器动画效果 */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.4;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-2px);
  }
}

/* 声波动画 */
@keyframes soundwave {
  0%, 100% {
    height: 8px;
    opacity: 0.5;
  }
  50% {
    height: 16px;
    opacity: 1;
  }
}

@keyframes soundwave-alt {
  0%, 100% {
    height: 12px;
    opacity: 0.7;
  }
  50% {
    height: 20px;
    opacity: 1;
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite linear;
}

.animate-pulse-glow {
  animation: pulse-glow 2s infinite ease-in-out;
}

.animate-float {
  animation: float 3s infinite ease-in-out;
}

.animate-soundwave {
  animation: soundwave 0.8s infinite ease-in-out;
}

.animate-soundwave-alt {
  animation: soundwave-alt 0.8s infinite ease-in-out;
}
