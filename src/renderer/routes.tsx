import React from 'react';
import { Routes, Route } from 'react-router-dom';

// 导入页面组件
import App from './App';
import ClipboardHistory from './pages/clipboard-history';
import FileSearchView from './pages/file-search/FileSearchView';
import FloatingBall from './pages/floating-ball';
import SettingsPage from './pages/settings';
import PetPage from './pages/pet';
import ScreenCapture from './pages/screen-capture';

const AppRoutes: React.FC = () => {
  return (
    <Routes>
      {/* 主界面使用App组件 */}
      <Route path="/" element={<App />} />
      <Route path="/clipboard-history" element={<ClipboardHistory />} />
      {/* <Route path="/file-search" element={<FileSearchView />} /> */}
      <Route path="/floating-ball" element={<FloatingBall />} />
      <Route path="/settings" element={<SettingsPage />} />
      <Route path="/pet" element={<PetPage />} />
      <Route path="/screen-capture" element={<ScreenCapture />} />
    </Routes>
  );
};

export default AppRoutes;