/* AI聊天相关样式 */
@import './ai-chat-vars.css';

/* Loading动画样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px 0;
}

.loading-dots {
  display: flex;
  align-items: center;
  gap: 8px;
}

.loading-dots span {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--bubble-border, #ccc);
  animation: dot-pulse 1.5s infinite ease-in-out;
}

.loading-dots span:nth-child(1) {
  animation-delay: 0s;
}

.loading-dots span:nth-child(2) {
  animation-delay: 0.3s;
}

.loading-dots span:nth-child(3) {
  animation-delay: 0.6s;
}

@keyframes dot-pulse {
  0%, 100% {
    transform: scale(0.7);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* 代码块样式 */
.markdown-content pre.hljs {
  @apply relative rounded-md my-4 overflow-hidden;
  background-color: var(--code-bg);
  border: 1px solid var(--code-border);
  border-radius: 6px;
  margin-top: 1rem;
  margin-bottom: 1rem;
  padding-top: 40px;
}

.markdown-content pre.hljs code {
  @apply font-mono text-sm block p-4 overflow-x-auto;
}

/* 确保暗色模式下的代码文本颜色 */
.dark .markdown-content pre.hljs {
  color-scheme: dark;
}

.dark .hljs {
  color: #e6e6e6;
}

/* Markdown内容基本样式 */
.markdown-content {
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: break-word;
  line-height: 1.6;
  max-width: 100%;
}

.markdown-content p {
  @apply mb-2;
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: break-word;
}

/* 内联代码样式 */
.markdown-content :not(pre) > code {
  @apply bg-gray-100 dark:bg-gray-800 px-1.5 py-0.5 rounded text-sm;
}

/* 代码块顶部控制栏 */
.code-block-header {
  @apply absolute top-0 left-0 w-full flex justify-between items-center px-3 py-2 border-b;
  background-color: var(--code-header-bg);
  border-color: var(--code-border);
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  height: 32px;
  z-index: 10;
}

/* 语言标签 */
.code-lang-label {
  @apply text-xs font-medium;
  color: var(--code-text);
}

/* 复制按钮 */
.code-copy-btn {
  @apply text-xs cursor-pointer px-2 py-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700;
  color: var(--code-text);
  transition: background-color 0.2s;
}

.ant-bubble-end .ant-bubble-content {
  background-color: #95ec68 !important;
  color: hsl(240 10% 3.9%) !important;
}

/* Ant-design-x Bubble 样式覆盖 */
.ant-bubble-content {
  border: 1px solid var(--bubble-border) !important;
  background-color: var(--bubble-bg) !important;
  color: hsl(var(--foreground)) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.dark .ant-bubble-content.ant-bubble-content-filled {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* 用户和助手气泡区分 */
.ant-bubble[data-placement="end"] .ant-bubble-content {
  background-color: var(--bubble-user-bg) !important;
}

/* 助手气泡样式 */
.ant-bubble[data-placement="start"] .ant-bubble-content {
  background-color: var(--bubble-assistant-bg) !important;
} 

.ant-typography {
  color: hsl(var(--foreground)) !important;
}

/* 标题元素的特殊样式 */
.ant-typography :is(h1, h2, h3, h4, h5, h6) {
  color: hsl(var(--foreground)) !important;
  font-weight: 600;
}

.ant-typography h1 { font-size: 1.8em; }
.ant-typography h2 { font-size: 1.6em; }
.ant-typography h3 { font-size: 1.4em; }
.ant-typography h4 { font-size: 1.2em; }
.ant-typography h5 { font-size: 1.1em; }
.ant-typography h6 { font-size: 1em; }

/* Reasoning 相关样式 */
.chat-item-reasoningcontent-container {
  @apply border rounded-xl transition-all duration-300 ease-in-out;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  box-shadow: 
    0 1px 3px rgba(0, 0, 0, 0.05),
    0 1px 2px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  pointer-events: auto;
  min-height: fit-content;
  max-width: 100%;
  display: inline-block;
  position: relative;
  overflow: hidden;
}

.chat-item-reasoningcontent-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6 0%, #8b5cf6 50%, #06b6d4 100%);
  border-radius: 12px 12px 0 0;
}

.dark .chat-item-reasoningcontent-container {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  border: 1px solid #475569;
  box-shadow: 
    0 1px 3px rgba(0, 0, 0, 0.3),
    0 1px 2px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.dark .chat-item-reasoningcontent-container::before {
  background: linear-gradient(90deg, #60a5fa 0%, #a78bfa 50%, #22d3ee 100%);
}

.chat-item-reasoningcontent-title-container {
  @apply transition-all duration-300 ease-in-out;
  pointer-events: auto;
  position: relative;
  z-index: 10;
  min-height: 52px;
  display: flex;
  align-items: center;
  white-space: nowrap;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px 12px 0 0;
  margin-top: 2px; /* 为顶部渐变条留出空间 */
}

.chat-item-reasoningcontent-title-container:hover {
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dark .chat-item-reasoningcontent-title-container {
  background: rgba(0, 0, 0, 0.1);
}

.dark .chat-item-reasoningcontent-title-container:hover {
  background: rgba(0, 0, 0, 0.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.reasoning_content_title {
  @apply text-sm font-semibold;
  color: hsl(var(--foreground)) !important;
  pointer-events: none;
  white-space: nowrap;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  letter-spacing: 0.025em;
}

.dark .reasoning_content_title {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.chat-item-reasoningcontent-title {
  @apply flex items-center;
  pointer-events: none;
  gap: 8px;
}

.chat-item-reasoningcontent-title .lucide {
  color: #3b82f6;
  filter: drop-shadow(0 1px 2px rgba(59, 130, 246, 0.3));
  transition: all 0.2s ease;
}

.dark .chat-item-reasoningcontent-title .lucide {
  color: #60a5fa;
  filter: drop-shadow(0 1px 2px rgba(96, 165, 250, 0.3));
}

.chat-item-reasoningcontent-title-container:hover .lucide {
  transform: scale(1.1);
  filter: drop-shadow(0 2px 4px rgba(59, 130, 246, 0.4));
}

.dark .chat-item-reasoningcontent-title-container:hover .lucide {
  filter: drop-shadow(0 2px 4px rgba(96, 165, 250, 0.4));
}

/* 文件路径容器样式 */
.markdown-content .file-path-container {
  display: inline-flex !important;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 1em;
  transition: background-color 0.2s ease;
}

.dark .markdown-content .file-path-container {
  background-color: var(--file-path-bg, #2a2a2a);
  border-color: var(--file-path-border, #404040);
}

/* 文件路径按钮样式 */
.file-path-btn {
  display: inline-flex !important;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
  cursor: pointer;
  opacity: 0.6;
  transition: opacity 0.2s ease, transform 0.1s ease;
  border-radius: 2px;
  position: relative;
  /* 彻底禁用title属性的tooltip显示 */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.file-path-btn:hover {
  opacity: 1 !important;
  transform: scale(1.1);
  background-color: rgba(0, 0, 0, 0.1) !important;
}

.dark .file-path-btn:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.file-path-btn:active {
  transform: scale(0.95);
}

/* 文件路径按钮图标样式 */
.file-path-btn svg {
  width: 12px;
  height: 12px;
  stroke: currentColor;
  fill: none;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

/* 文件路径代码样式 */
.file-path-container .file-path-text {
  margin: 0 !important;
  background: none !important;
  padding: 0 !important;
  border: none !important;
  font-size: inherit;
  color: var(--file-path-text, #333);
  user-select: all;
  font-family: inherit;
  display: inline;
}

.dark .file-path-container .file-path-text {
  color: var(--file-path-text, #e0e0e0);
}

/* 确保文件路径容器在各种上下文中的显示 */
.markdown-content p .file-path-container,
.markdown-content div .file-path-container,
.markdown-content span .file-path-container {
  vertical-align: baseline;
  margin: 0 2px;
  position: relative;
}

/* 工具提示样式优化 - 智能定位防止超出容器 */
.file-path-btn[title]:hover::after {
  /* 禁用CSS tooltip，使用JavaScript动态创建 */
  display: none;
}

/* 针对右侧按钮（目录按钮）防止超出右边界 */
.file-location-btn[title]:hover::after {
  /* 禁用CSS tooltip，使用JavaScript动态创建 */
  display: none;
}

.dark .file-path-btn[title]:hover::after {
  /* 禁用CSS tooltip，使用JavaScript动态创建 */
  display: none;
}

/* 自定义tooltip样式 */
.custom-tooltip {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

/* 确保自定义tooltip在所有主题下都正确显示 */
.dark .custom-tooltip {
  background-color: rgba(255, 255, 255, 0.9) !important;
  color: black !important;
  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1) !important;
}

/* 禁用所有文件路径相关元素的默认tooltip */
.file-path-container * {
  pointer-events: auto;
}

.file-path-container *[title] {
  title: none !important;
}

/* 禁用所有链接的默认属性 */
.file-path-container *[title] {
  text-decoration: none !important;
}

/* 自定义滚动条样式 */
.custom-scrollbar {
  /* Firefox */
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

/* WebKit 浏览器 (Chrome, Safari, Edge) */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 暗色模式下的滚动条样式 */
.dark .custom-scrollbar {
  /* Firefox */
  scrollbar-color: #475569 #1e293b;
}

.dark .custom-scrollbar::-webkit-scrollbar-track {
  background: #1e293b;
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background: #475569;
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Reasoning 内容区域样式 */
.chat-item-reasoningcontent-container .markdown-content {
  padding: 16px 20px 20px 20px;
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(5px);
  border-radius: 0 0 12px 12px;
  position: relative;
}

.dark .chat-item-reasoningcontent-container .markdown-content {
  background: rgba(0, 0, 0, 0.2);
}

/* Reasoning 展开/收起动画优化 */
.chat-item-reasoningcontent-container[style*="height: auto"] {
  animation: reasoning-expand 0.3s ease-out;
}

@keyframes reasoning-expand {
  from {
    opacity: 0.8;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* MCP下载消息样式 - 隐藏外层边框 */
.mcp-download-message .ant-bubble {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
}

.mcp-download-message .ant-bubble-content {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
}

