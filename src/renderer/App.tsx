import React, { useEffect } from 'react';
import { ThemeProvider, useTheme } from './components/theme-provider';
import HomePage from './pages/home';
import { useWinStore } from './stores/WinStore';

// 导出FilePathContext供其他组件使用
export { FilePathContext } from './pages/home';

// 内部应用组件，用于监听主题变化
const AppContent: React.FC = () => {
  const { setTheme } = useTheme();
  // 窗口状态管理
  const { initStore } = useWinStore();

  // 窗口管理相关的初始化逻辑
  useEffect(() => {
    // 初始化窗口store
    initStore();

    // 监听窗口状态变化
    const handleWindowStateChange = () => {
      console.log('🪟 窗口状态发生变化，重新初始化store');
      initStore();
    };

    // 监听窗口大小变化
    const handleResize = () => {
      console.log('🪟 窗口大小变化');
    };

    // 监听窗口焦点变化
    const handleFocus = () => {
      console.log('🪟 窗口获得焦点');
    };

    const handleBlur = () => {
      console.log('🪟 窗口失去焦点');
    };

    // 添加窗口事件监听器
    window.addEventListener('resize', handleResize);
    window.addEventListener('focus', handleFocus);
    window.addEventListener('blur', handleBlur);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('blur', handleBlur);
    };
  }, [initStore]);

  // 监听主题变化事件
  useEffect(() => {
    if (!window.electron?.system?.onThemeChanged) return;

    const handleThemeChange = (theme: string) => {
      console.log(`🎨 主应用窗口收到主题变化事件: ${theme}`);
      setTheme(theme as 'light' | 'dark' | 'system');
    };

    const removeListener = window.electron.system.onThemeChanged(handleThemeChange);

    return () => {
      removeListener();
    };
  }, [setTheme]);

  // 监听系统级事件
  useEffect(() => {
    // 监听系统睡眠/唤醒事件
    const handleSystemSleep = () => {
      console.log('💤 系统进入睡眠状态');
    };

    const handleSystemWake = () => {
      console.log('⏰ 系统从睡眠状态唤醒');
    };

    // 如果有系统睡眠/唤醒监听API，在这里添加
    // const removeSleepListener = window.electron.system?.onSleep?.(handleSystemSleep);
    // const removeWakeListener = window.electron.system?.onWake?.(handleSystemWake);

    return () => {
      // removeSleepListener?.();
      // removeWakeListener?.();
    };
  }, []);

  // 监听跨窗口通信事件（应用级别的）
  useEffect(() => {
    if (!window.electron?.crossWindow) return;

    // 监听应用级别的跨窗口事件
    const handleAppLevelEvent = (data: any) => {
      console.log('🔄 收到应用级别的跨窗口事件:', data);
    };

    // 监听窗口切换事件
    const handleWindowSwitch = (data: any) => {
      console.log('🪟 窗口切换事件:', data);
    };

    const unsubscribeAppEvent = window.electron.crossWindow.on('app:level-event', handleAppLevelEvent);
    const unsubscribeWindowSwitch = window.electron.crossWindow.on('window:switch', handleWindowSwitch);

    return () => {
      unsubscribeAppEvent();
      unsubscribeWindowSwitch();
    };
  }, []);


  // 处理应用生命周期事件
  useEffect(() => {
    // 应用即将退出时的清理工作
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      console.log('🔌 应用即将退出，清理资源...');
      
      // 清理WebSocket连接
      if (window.electron?.crossWindow) {
        try {
          // 通知所有窗口应用即将退出
          window.electron.crossWindow.emit('app:before-quit', {});
        } catch (error) {
          console.error('通知其他窗口退出失败:', error);
        }
      }

      // 清理本地存储
      try {
        // 保存重要状态到localStorage
        const appState = {
          timestamp: Date.now(),
          cleanExit: true
        };
        localStorage.setItem('app-exit-state', JSON.stringify(appState));
      } catch (error) {
        console.error('保存退出状态失败:', error);
      }
    };

    // 应用完全加载后的初始化
    const handleLoad = () => {
      console.log('🚀 应用完全加载完成');
      
      // 检查上次是否正常退出
      try {
        const exitState = localStorage.getItem('app-exit-state');
        if (exitState) {
          const state = JSON.parse(exitState);
          if (!state.cleanExit) {
            console.warn('⚠️ 检测到应用异常退出');
            // 可以在这里处理异常退出的恢复逻辑
          }
        }
      } catch (error) {
        console.error('检查退出状态失败:', error);
      }
    };

    // 处理未捕获的错误
    const handleError = (event: ErrorEvent) => {
      console.error('🚨 未捕获的错误:', event.error);
      // 可以在这里添加错误报告逻辑
    };

    // 处理未捕获的Promise拒绝
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error('🚨 未处理的Promise拒绝:', event.reason);
      // 可以在这里添加错误报告逻辑
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('load', handleLoad);
    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('load', handleLoad);
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  return <HomePage />;
};

const App: React.FC = () => {
  return (
    <ThemeProvider defaultTheme="system" storageKey="vite-ui-theme">
      <AppContent />
    </ThemeProvider>
  );
};

export default App;
