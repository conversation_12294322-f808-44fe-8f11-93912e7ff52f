import Debug from 'debug';
import NextChatService from './NextChatService';
import { IChatContext, IChatRequestMessage, IChatRequestPayload } from '../../types/llm';
import { IServiceProvider, ProviderType } from '../providers/types';
import IChat<PERSON>eader, { ITool, IReadResult } from '../readers/IChatReader';
import { IOpenAITool, IAnthropicTool, IGoogleTool, IMCPTool } from '../../types/llm';
import { useSystemStore } from '../../stores/systemStore';

const debug = Debug('aido:llm:services:WebSocketChatService');

// WebSocket 消息接口 - 发送消息
interface WebSocketMessage {
  type: 'listen';
  id?: string;
  state: 'detect' | 'detecting' | 'detect_done' | 'detect_error' | 'start' | 'end';
  text?: string;
  source?: 'text' | 'image' | 'audio' | 'video' | 'file';
  mode?: 'manual' | 'auto';
  is_text?: boolean;
}

// Hello消息接口
interface HelloMessage {
  type: 'hello';
  version: number;
  transport: 'websocket';
  features: {mcp: boolean};
  audio_params: {
    format: 'opus';
    sample_rate: number;
    channels: number;
    frame_duration: number;
  };
}

// Hello响应接口
interface HelloResponse {
  type: 'hello';
  transport: 'websocket';
  audio_params: {
    format: 'opus';
    sample_rate: number;
    channels: number;
    frame_duration: number;
  };
}

// WebSocket 响应接口 - 接收的消息类型
interface WebSocketResponse {
  type: 'tts' | 'llm' | 'error' | 'stt' | 'hello' | 'mcp';
  state?: 'sentence_start' | 'sentence_end' | 'stop' | 'start';
  session_id: string;
  text?: string;
  emotion?: 'surprised' | 'thinking' | 'happy' | 'sad' | 'angry' | 'neutral' | 'loving' | 'laughing';
  error?: string;
  payload?: any; // MCP消息的payload
}

// 内部聊天响应接口
interface ChatResponse {
  content?: string;
  reasoning?: string;
  tool_calls?: any[];
  finished?: boolean;
  error?: any;
  usage?: {
    input_tokens?: number;
    output_tokens?: number;
  };
  emotion?: string;
  tts_text?: string;
  audio_data?: ArrayBuffer;
}

// WebSocket 连接管理器
class WebSocketManager {
  public ws: WebSocket | null = null;
  public readonly url: string;
  private isConnecting: boolean = false;
  private messageQueue: WebSocketMessage[] = [];
  private messageHandlers: Map<string, (data: any) => void> = new Map();
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 5;
  private reconnectDelay: number = 1000;
  private helloCompleted: boolean = false;
  private helloPromise: Promise<void> | null = null;
  private session_id: string = '';
  public disableAutoReconnect: boolean = false; // 新增：外部可控的禁止自动重连标志

  constructor(url: string) {
    this.url = url;
    // 确保新实例的状态是干净的
    this.isConnecting = false;
    this.reconnectAttempts = 0;
    this.helloCompleted = false;
    this.helloPromise = null;
    this.session_id = '';
    connectionCounter++;
    console.log('🔧 创建新的WebSocketManager实例，URL:', url, '时间戳:', Date.now(), '连接计数:', connectionCounter);
  }

  async connect(): Promise<void> {
    if (this.ws && this.ws.readyState === WebSocket.OPEN && this.helloCompleted) {
      console.log('🔍 WebSocket已连接且hello已完成，跳过连接过程');
      return;
    }

    if (this.isConnecting) {
      console.log('🔍 WebSocket正在连接中，等待连接完成');
      return new Promise((resolve, reject) => {
        const checkConnection = () => {
          if (this.ws && this.ws.readyState === WebSocket.OPEN && this.helloCompleted) {
            console.log('🔍 WebSocket连接完成');
            resolve();
          } else if (!this.isConnecting) {
            console.log('🔍 WebSocket连接失败');
            reject(new Error('连接失败'));
          } else {
            setTimeout(checkConnection, 100);
          }
        };
        checkConnection();
      });
    }

    console.log('🔌 开始建立WebSocket连接，URL:', this.url);
    this.isConnecting = true;
    this.helloCompleted = false;

    return new Promise((resolve, reject) => {
      try {
        console.log('🔌 开始建立WebSocket连接到:', this.url);
        
        // 创建WebSocket连接
        this.ws = new WebSocket(this.url);

        // 关键：设置二进制数据类型为ArrayBuffer
        this.ws.binaryType = 'arraybuffer';
        console.log('🔧 WebSocket binaryType设置为:', this.ws.binaryType);

        this.ws.onopen = async () => {
          console.log('✅ WebSocket连接已建立，准备发送hello消息');
          debug('WebSocket连接已建立');
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          
          // 发送hello消息
          try {
            await this.sendHello();
            this.helloCompleted = true;
            console.log('✅ Hello消息发送成功，连接准备就绪');
            
            // 发送队列中的消息
            while (this.messageQueue.length > 0) {
              const message = this.messageQueue.shift();
              if (message) {
                this.send(message);
              }
            }
            
            resolve();
          } catch (error) {
            console.error('❌ Hello消息发送失败:', error);
            reject(error);
          }
        };

        this.ws.onmessage = (event) => {
          try {
            // 处理二进制音频数据
            if (event.data instanceof ArrayBuffer) {
              console.log('🎵 收到二进制音频数据:', event.data.byteLength, 'bytes', '当前处理器数量:', this.messageHandlers.size);
              // 将音频数据传递给当前活跃的处理器
              this.messageHandlers.forEach((handler, messageId) => {
                console.log('🎵 传递音频数据给处理器:', messageId);
                handler({ audio_data: event.data });
              });
              return;
            }

            // 处理Blob数据（可能的音频格式）
            if (event.data instanceof Blob) {
              console.log('🎵 收到Blob音频数据:', event.data.size, 'bytes, type:', event.data.type, '当前处理器数量:', this.messageHandlers.size);
              // 将Blob转换为ArrayBuffer
              event.data.arrayBuffer().then(buffer => {
                console.log('🎵 Blob转换为ArrayBuffer:', buffer.byteLength, 'bytes');
                this.messageHandlers.forEach((handler, messageId) => {
                  console.log('🎵 传递Blob音频数据给处理器:', messageId);
                  handler({ audio_data: buffer });
                });
              }).catch(error => {
                console.error('❌ Blob转ArrayBuffer失败:', error);
              });
              return;
            }

            // 处理JSON消息
            const response: WebSocketResponse = JSON.parse(event.data);
            console.log('📨 收到WebSocket JSON消息:', response);
            
            // 处理hello响应
            if (response.type === 'hello') {
              console.log('✅ 收到hello响应:', response);
              if (this.helloPromise) {
                this.helloPromise = null;
              }
              this.session_id = response.session_id;
              return;
            }
            
            // 将服务器响应转换为内部格式
            const chatResponse: ChatResponse = this.convertServerResponse(response);
            
            // 传递给所有活跃的处理器（因为没有明确的消息ID关联）
            this.messageHandlers.forEach(handler => {
              handler(chatResponse);
            });
          } catch (error) {
            console.error('❌ 解析WebSocket消息失败:', error, 'event.data:', event.data);
          }
        };

        this.ws.onclose = (event) => {
          console.log('🔌 WebSocket连接关闭:', event.code, event.reason);
          debug('WebSocket连接关闭:', event.code, event.reason);
          this.isConnecting = false;
          this.helloCompleted = false;
          this.ws = null;

          // 新增：判断是否禁止自动重连
          if (this.disableAutoReconnect) {
            console.log('🚫 已禁止WebSocket自动重连');
            return;
          }

          // 如果不是正常关闭，尝试重连
          if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
            console.log('🔄 尝试重新连接WebSocket');
            this.scheduleReconnect();
          } else {
            console.log('🔌 WebSocket连接正常关闭，不进行重连');
          }
        };

        this.ws.onerror = (error) => {
          console.log('❌ WebSocket连接错误:', error);
          debug('WebSocket连接错误:', error);
          this.isConnecting = false;
          this.helloCompleted = false;
          
          if (this.reconnectAttempts === 0) {
            console.log('❌ WebSocket连接失败，拒绝Promise');
            reject(new Error('WebSocket连接失败'));
          }
        };

      } catch (error) {
        this.isConnecting = false;
        this.helloCompleted = false;
        reject(error);
      }
    });
  }

  // 发送hello消息
  private async sendHello(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
        reject(new Error('WebSocket未连接'));
        return;
      }

      const helloMessage: HelloMessage = {
        type: 'hello',
        version: 1,
        transport: 'websocket',
        features: {mcp: true},
        audio_params: {
          format: 'opus',
          sample_rate: 16000,
          channels: 1,
          frame_duration: 60
        }
      };

      console.log('📤 发送hello消息:', helloMessage);
      
      // 设置超时
      const timeout = setTimeout(() => {
        reject(new Error('Hello消息响应超时'));
      }, 10000); // 10秒超时

      // 临时监听hello响应
      const originalOnMessage = this.ws.onmessage;
      this.ws.onmessage = (event) => {
        try {
          const response: WebSocketResponse = JSON.parse(event.data);
          if (response.type === 'hello') {
            console.log('✅ 收到hello响应:', response);
            this.session_id = response.session_id;
            clearTimeout(timeout);
            this.ws!.onmessage = originalOnMessage; // 恢复原始消息处理
            resolve();
          } else {
            // 其他消息交给原始处理器
            if (originalOnMessage) {
              originalOnMessage.call(this.ws, event);
            }
          }
        } catch (error) {
          console.error('❌ 解析hello响应失败:', error);
          clearTimeout(timeout);
          this.ws!.onmessage = originalOnMessage;
          reject(error);
        }
      };

      // 发送hello消息
      this.ws.send(JSON.stringify(helloMessage));
    });
  }

  private scheduleReconnect(): void {
    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    debug(`${delay}ms后尝试第${this.reconnectAttempts}次重连`);
    
    setTimeout(() => {
      this.connect().catch(error => {
        debug('重连失败:', error);
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
          debug('已达到最大重连次数，停止重连');
        }
      });
    }, delay);
  }

  send(message: WebSocketMessage): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN && this.helloCompleted) {
      this.ws.send(JSON.stringify({
        ...message,
        // session_id: this.session_id
      }));
      debug('发送WebSocket消息:', message);
      console.log('📤 成功发送WebSocket消息:', message);
    } else {
      // 连接未建立或hello未完成，加入队列
      this.messageQueue.push(message);
      debug('WebSocket未连接或hello未完成，消息加入队列');
      console.log('⚠️ WebSocket未连接或hello未完成，消息加入队列。连接状态:', {
        wsExists: !!this.ws,
        readyState: this.ws?.readyState,
        isConnecting: this.isConnecting,
        helloCompleted: this.helloCompleted
      });
    }
  }

  registerHandler(messageId: string, handler: (data: any) => void): void {
    // 只注册当前消息的处理器，不清空其他处理器
    this.messageHandlers.set(messageId, handler);
    console.log('🔧 注册消息处理器:', messageId, '当前处理器数量:', this.messageHandlers.size);
  }

  unregisterHandler(messageId: string): void {
    this.messageHandlers.delete(messageId);
  }

  getActiveHandlersCount(): number {
    return this.messageHandlers.size;
  }

  close(): void {
    console.log('🔌 关闭WebSocket连接');
    if (this.ws) {
      this.ws.close(1000, 'Normal closure');
      this.ws = null;
    }
    this.isConnecting = false; // 重置连接状态
    this.helloCompleted = false; // 重置hello状态
    this.messageHandlers.clear();
    this.messageQueue = [];
    this.reconnectAttempts = 0; // 重置重连次数
    console.log('🔌 WebSocket连接已关闭，状态已重置，连接计数:', connectionCounter);
  }

  get isConnected(): boolean {
    const connected = this.ws !== null && this.ws.readyState === WebSocket.OPEN && this.helloCompleted;
    console.log('🔍 WebSocket连接状态检查:', {
      wsExists: !!this.ws,
      readyState: this.ws?.readyState,
      helloCompleted: this.helloCompleted,
      isConnected: connected,
      url: this.url
    });
    return connected;
  }

  get isHelloCompleted(): boolean {
    return this.helloCompleted;
  }

  get isConnectingState(): boolean {
    return this.isConnecting;
  }

  // 将服务器响应转换为内部聊天响应格式
  private convertServerResponse(response: WebSocketResponse): ChatResponse {
    console.log('🔄 convertServerResponse - 输入:', response);
    const chatResponse: ChatResponse = {};

    switch (response.type) {
      case 'hello':
        // Hello消息由sendHello方法单独处理，这里不需要处理
        console.log('👋 Hello消息处理 - 由sendHello方法处理');
        break;
        
      case 'llm':
        // LLM文本内容和表情
        chatResponse.content = response.text || '';
        chatResponse.emotion = response.emotion;
        console.log('📝 LLM消息处理 - content:', chatResponse.content, 'emotion:', chatResponse.emotion);
        break;
        
      case 'tts':
        // TTS文本和状态 - 只在特定状态下处理文本，避免重复
        console.log('🔊 TTS消息处理 - text:', response.text, 'state:', response.state, 'emotion:', response.emotion);
        
        // 只在句子结束或停止状态时处理文本内容，避免start和end都处理导致重复
        if (response.text && response.text.trim() && 
            (response.state === 'sentence_end' || response.state === 'stop')) {
          chatResponse.tts_text = response.text;
          console.log('✅ 设置TTS文本 (state:', response.state, '):', chatResponse.tts_text);
        } else if (response.text && response.text.trim()) {
          console.log('⏭️ 跳过TTS文本 (state:', response.state, ')，避免重复渲染');
        } else {
          console.log('❌ TTS消息没有有效文本内容');
        }
        
        // 传递情感状态（如果有）
        if (response.emotion) {
          chatResponse.emotion = response.emotion;
          console.log('😊 设置情感状态:', chatResponse.emotion);
        }
        
        if (response.state === 'stop') {
          chatResponse.finished = true;
          console.log('🛑 TTS停止，标记为完成');
        }
        break;

      case 'stt':
        // STT (Speech-to-Text) 消息，通常是用户输入的确认
        console.log('🎤 STT消息处理 - text:', response.text);
        // STT消息通常不需要显示在UI中，只是确认收到了用户输入
        break;
        
      case 'mcp':
        // MCP (Model Context Protocol) 工具调用响应
        console.log('🔧 收到MCP响应，payload:', response.payload);
        // MCP响应不需要在WebSocketChatService中处理
        // 它们通过跨窗口事件系统处理
        // 这里只是确保不会显示"未知消息类型"警告
        break;

      case 'error':
        // 错误处理
        chatResponse.error = response.error || '未知错误';
        chatResponse.finished = true;
        console.log('❌ 错误消息:', chatResponse.error);
        break;

      default:
        console.warn('⚠️ 未知的消息类型:', response.type, response);
        break;
    }

    console.log('🔄 convertServerResponse - 输出:', chatResponse);
    return chatResponse;
  }
}

// 获取WebSocket URL的函数
const getWebSocketUrl = (): string => {
  const deviceId = localStorage.getItem('deviceId');
  const baseUrl = localStorage.getItem('otaSocketUrl');
  // const baseUrl = "wss://www.mcpcn.cc/xiaozhi1/v1/";
  // const baseUrl = "ws://192.168.100.21:8000/xiaozi/v1";
  console.log('🔧 构建WebSocket URL，基础URL:', baseUrl);
  console.log('🔧 构建WebSocket URL，设备ID:', deviceId);
  
  if (!baseUrl) {
    throw new Error('WebSocket URL未配置');
  }
  
  // 构建WebSocket URL并添加认证参数
  const url = new URL(baseUrl);
  
  // 添加认证参数（通过URL参数传递，因为WebSocket API不支持自定义headers）
  url.searchParams.set('device-id', deviceId);
  url.searchParams.set('client-id', deviceId);
  url.searchParams.set('is-aido', '1');
  
  // 添加协议版本
  url.searchParams.set('protocol-version', '1');
  
  console.log('🔧 构建WebSocket URL，基础URL:', baseUrl, '设备ID:', deviceId);
  return url.toString();
};

// 创建全局WebSocket管理器实例
let wsManager: WebSocketManager | null = null;
let isConnecting = false; // 全局连接锁
let isForceDisconnected = false; // 新增：强制断开标志
let connectionCounter = 0; // 新增：连接计数器，用于调试

// 获取或创建WebSocket管理器实例
const getWebSocketManager = (type: string = 'text'): WebSocketManager => {
  const currentUrl = getWebSocketUrl();
  
  console.log('🔍 getWebSocketManager检查:', {
    wsManagerExists: !!wsManager,
    currentUrl,
    wsManagerUrl: wsManager?.url,
    isConnected: wsManager?.isConnected,
    isConnecting,
    isForceDisconnected
  });
  
  // 如果被强制断开，需要重新创建连接
  if (isForceDisconnected) {
    console.log('🌐 检测到强制断开状态，重新创建WebSocket管理器');
    if (wsManager) {
      wsManager.close(); // 确保完全关闭
      wsManager = null;
    }
    isForceDisconnected = false; // 重置强制断开标志
  }
  
  // 修复：更保守的连接检查逻辑
  // 只有在WebSocket实际断开且无法恢复时才重新创建
  if (wsManager && wsManager.ws) {
    const readyState = wsManager.ws.readyState;
    if (readyState === WebSocket.CLOSED || readyState === WebSocket.CLOSING) {
      console.log('🌐 检测到WebSocket已断开或正在关闭，重新创建WebSocket管理器');
      wsManager.close(); // 确保完全关闭
      wsManager = null;
    } else if (readyState === WebSocket.OPEN && !wsManager.isHelloCompleted) {
      console.log('🌐 WebSocket已连接但Hello未完成，等待Hello完成');
      // 不重新创建，让现有连接完成Hello过程
    }
  }
  
  // 简化逻辑：只在必要时重新创建
  if (!wsManager || wsManager.url !== currentUrl) {
    console.log('🌐 创建新的WebSocket管理器');
    if (wsManager) {
      wsManager.close(); // 关闭旧连接
    }
    wsManager = new WebSocketManager(currentUrl);
  } else {
    console.log('🌐 复用现有的WebSocket管理器');
  }
  return wsManager;
};

// 新增：全局设置WebSocket禁止自动重连
const setWebSocketDisableAutoReconnect = (disable: boolean) => {
  if (wsManager) {
    wsManager.disableAutoReconnect = disable;
  }
};

// 新增：强制断开WebSocket连接
const forceDisconnectWebSocket = (): void => {
  console.log('🔌 强制断开WebSocket连接');
  if (wsManager) {
    wsManager.close();
    wsManager = null;
  }
  isConnecting = false;
  isForceDisconnected = true; // 设置强制断开标志
  console.log('🔌 WebSocket连接已强制断开，下次需要重新建立连接，连接计数:', connectionCounter);
};

// WebSocket 聊天读取器 - 模拟流式读取器的接口
class WebSocketChatReader implements IChatReader {
  private messageId: string;
  private completed: boolean = false;

  constructor(messageId: string) {
    this.messageId = messageId;
  }

  async read(options: {
    onError: (error: any) => void;
    onProgress: (content: string, reasoning?: string, emotion?: string, audioData?: ArrayBuffer) => void;
    onToolCalls: (toolName: string, toolArgs?: any) => void;
  }): Promise<any> {
    return new Promise((resolve) => {
      let fullContent = '';
      let fullReasoning = '';
      let toolCall: any = null;
      let usage: any = null;

      const handler = (data: ChatResponse) => {
        console.log('📨 WebSocketChatReader处理数据:', data);
        
        if (data.error) {
          console.log('❌ 处理错误消息:', data.error);
          options.onError(data.error);
          // 获取WebSocket管理器实例
          const wsManager = getWebSocketManager();
          wsManager.unregisterHandler(this.messageId);
          resolve({ content: fullContent, reasoning: fullReasoning, tool: toolCall, ...usage });
          return;
        }

        // 处理LLM文本内容
        if (data.content && data.content.trim()) {
          console.log('📝 处理LLM内容:', data.content);
          fullContent += data.content;
          options.onProgress(data.content, undefined, data.emotion);
        }

        // 处理TTS文本 - 只有当确实有文本内容时才显示
        if (data.tts_text && data.tts_text.trim()) {
          console.log('🔊 处理TTS文本:', data.tts_text);
          // 将TTS文本作为内容显示
          fullContent += data.tts_text;
          options.onProgress(data.tts_text, undefined, data.emotion);
        }

        // 处理音频数据
        if (data.audio_data) {
          console.log('🎵 处理音频数据:', data.audio_data.byteLength, 'bytes');
          // 传递音频数据给前端处理
          options.onProgress('', undefined, undefined, data.audio_data);
        }

        // 处理只有情感信息的情况
        if (data.emotion && !data.content && !data.tts_text) {
          console.log('😊 只有情感信息:', data.emotion);
          options.onProgress('', undefined, data.emotion);
        }

        // 处理推理内容更新（如果有的话）
        if (data.reasoning) {
          const newReasoning = data.reasoning.slice(fullReasoning.length);
          if (newReasoning) {
            fullReasoning = data.reasoning;
            options.onProgress('', newReasoning);
          }
        }

        // 处理工具调用
        if (data.tool_calls && data.tool_calls.length > 0) {
          const tool = data.tool_calls[0]; // 取第一个工具调用
          if (tool) {
            options.onToolCalls(tool.name, tool.args);
            toolCall = {
              name: tool.name,
              args: tool.args
            };
          }
        }

        // 处理使用量信息
        if (data.usage) {
          usage = {
            inputTokens: data.usage.input_tokens,
            outputTokens: data.usage.output_tokens
          };
        }

        // 检查是否完成（TTS停止表示对话结束）
        if (data.finished) {
          this.completed = true;
          // 获取WebSocket管理器实例
          const wsManager = getWebSocketManager();
          wsManager.unregisterHandler(this.messageId);
          resolve({ 
            content: fullContent, 
            reasoning: fullReasoning, 
            tool: toolCall,
            ...usage
          });
        }
      };

      // 获取WebSocket管理器实例
      const wsManager = getWebSocketManager();
      wsManager.registerHandler(this.messageId, handler);
    });
  }
}

export default class WebSocketChatService extends NextChatService {
  private static instance: WebSocketChatService | null = null;

  constructor(name: string, context: IChatContext) {
    // 创建一个简化的provider，因为WebSocket模式不需要复杂的provider配置
    const wsProvider: IServiceProvider = {
      name: 'WebSocket' as ProviderType,
      description: 'WebSocket Chat Provider',
      apiBase: getWebSocketUrl(), // 从store获取WebSocket URL
      apiKey: '',
      currency: 'USD' as const,
      options: {
        apiBaseCustomizable: false,
        apiKeyCustomizable: false
      },
      chat: {
        apiSchema: [],
        presencePenalty: { min: 0, max: 2, default: 0 },
        topP: { min: 0, max: 1, default: 1 },
        temperature: { min: 0, max: 2, default: 1 },
        models: [],
        options: {}
      }
    };

    super({ name, context, provider: wsProvider });
    
    // 延迟连接建立，只在真正需要时才建立连接
    // this.ensureConnection(); // 注释掉构造函数中的连接建立
  }

  private async ensureConnection(): Promise<void> {
    try {
      const manager = getWebSocketManager();
      
      // 使用全局连接锁防止重复连接
      if (isConnecting) {
        console.log('🔍 全局连接锁：正在连接中，等待连接完成');
        // 等待连接完成
        while (isConnecting) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
        return;
      }
      
      // 简化连接逻辑：只在未连接时尝试连接
      if (!manager.isConnected) {
        console.log('🔌 连接未建立，开始连接WebSocket');
        isConnecting = true;
        try {
          await manager.connect();
          console.log('✅ WebSocket连接建立成功');
        } finally {
          isConnecting = false;
        }
      } else {
        console.log('🔍 WebSocket连接已存在，复用现有连接');
      }
    } catch (error) {
      isConnecting = false;
      console.error('❌ WebSocket连接失败:', error);
      debug('WebSocket连接失败:', error);
      throw error;
    }
  }

  protected getReaderType(): new (reader: ReadableStreamDefaultReader<Uint8Array>) => IChatReader {
    // WebSocket模式不使用传统的流读取器，但需要返回一个类型
    // 这个方法不会被实际调用，因为我们重写了chat方法
    return class implements IChatReader {
      async read(): Promise<IReadResult> {
        return {
          content: '',
          reasoning: '',
          tool: null
        };
      }
    } as any;
  }

  protected makeToolMessages(tool: ITool, toolResult: any, content?: string): IChatRequestMessage[] {
    // WebSocket模式下工具调用由后端处理，这里返回空数组
    return [];
  }

  protected makeTool(tool: IMCPTool): IOpenAITool | IAnthropicTool | IGoogleTool {
    // WebSocket模式下工具定义由后端处理
    return {} as IOpenAITool;
  }

  protected async makePayload(messages: IChatRequestMessage[], msgId?: string): Promise<IChatRequestPayload> {
    // 简化的payload，只包含消息
    return {
      messages: messages,
      stream: true
    } as IChatRequestPayload;
  }

  protected async makeRequest(messages: IChatRequestMessage[], msgId?: string): Promise<Response> {
    // WebSocket模式不使用HTTP请求，这个方法不会被调用
    // 但需要实现以满足抽象类要求
    throw new Error('makeRequest should not be called in WebSocket mode');
  }

  // 重写chat方法以使用WebSocket
  public async chat(messages: IChatRequestMessage[], msgId?: string): Promise<void> {
    console.log('🔌 开始WebSocket聊天，确保连接可用');
    await this.ensureConnection();

    const messageId = msgId || `chat_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    let signal: AbortSignal | null = null;

    try {
      signal = this.abortController.signal;
      
      // 获取WebSocket管理器实例，只调用一次
      const wsManager = getWebSocketManager();
      
      // 清理可能存在的旧处理器，防止重复处理音频数据
      wsManager.unregisterHandler(messageId);
      
      // 获取最后一条用户输入的文本内容
      const userMessages = messages.filter(msg => msg.role === 'user');
      const lastUserMessage = userMessages[userMessages.length - 1];
      const userText = lastUserMessage ? 
        (typeof lastUserMessage.content === 'string' ? lastUserMessage.content : JSON.stringify(lastUserMessage.content)) : 
        '';

      // 构建WebSocket消息
      const wsMessage: WebSocketMessage = {
        id: messageId,
        type: 'listen',
        state: 'detect',
        text: userText,
        source: 'text',
        is_text: true,
      };

      setTimeout(() => {
        // 发送消息
        wsManager.send(wsMessage);
      }, 150);

      // 创建WebSocket读取器
      const reader = new WebSocketChatReader(messageId);

      // 读取响应
      const readResult = await reader.read({
        onError: (err: any) => {
          this.onErrorCallback(err, !!signal?.aborted);
        },
        onProgress: (content: string, reasoning?: string, emotion?: string, audioData?: ArrayBuffer) => {
          this.onReadingCallback(content, reasoning, emotion, audioData);
        },
        onToolCalls: (toolName: string, toolArgs?: any) => {
          this.onToolCallsCallback(toolName, toolArgs);
        }
      });

      // 更新token使用量
      if (readResult?.inputTokens) {
        this.inputTokens += readResult.inputTokens;
      }
      if (readResult?.outputTokens) {
        this.outputTokens += readResult.outputTokens;
      }

      // 对话完成
      await this.onCompleteCallback({
        content: readResult.content || '',
        reasoning: readResult.reasoning || '',
        inputTokens: this.inputTokens,
        outputTokens: this.outputTokens,
      });
      this.inputTokens = 0;
      this.outputTokens = 0;

    } catch (error: any) {
      this.onErrorCallback(error, !!signal?.aborted);
      await this.onCompleteCallback({
        content: '',
        reasoning: '',
        inputTokens: this.inputTokens,
        outputTokens: this.outputTokens,
        error: {
          code: error.code || 500,
          message: error.message || error.toString(),
        },
      });
      this.inputTokens = 0;
      this.outputTokens = 0;
    }
  }

  public abort(): void {
    super.abort();
    // 可以选择发送取消消息给WebSocket服务器
  }

  // 静态方法获取单例实例
  public static getInstance(context: IChatContext): WebSocketChatService {
    if (!WebSocketChatService.instance) {
      WebSocketChatService.instance = new WebSocketChatService('websocket', context);
    }
    return WebSocketChatService.instance;
  }

  // 清理方法
  public static cleanup(): void {
    if (WebSocketChatService.instance) {
      // 强制断开WebSocket连接
      forceDisconnectWebSocket();
      WebSocketChatService.instance = null;
      console.log('🔌 WebSocket连接已完全断开，下次将重新建立连接');
    } else {
      // 即使没有实例，也要确保连接被断开
      forceDisconnectWebSocket();
      console.log('🔌 强制断开WebSocket连接（无实例）');
    }
  }

  // 获取活跃处理器数量
  public static getActiveHandlersCount(): number {
    const wsManager = getWebSocketManager();
    return wsManager?.getActiveHandlersCount() || 0;
  }
} 
export { getWebSocketManager, setWebSocketDisableAutoReconnect, forceDisconnectWebSocket };