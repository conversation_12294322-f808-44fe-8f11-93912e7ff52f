import Debug from 'debug';
import IChatReader, { ITool } from '../readers/IChatReader';
import {
  IAnthropicTool,
  IChatContext,
  IChatRequestMessage,
  IChatRequestMessageContent,
  IChatRequestPayload,
  IGeminiChatRequestMessagePart,
  IGoogleTool,
  IMCPTool,
  IOpenAITool,
} from '../../types/llm';
import { IServiceProvider } from '../providers/types';
import { raiseError, stripHtmlTags } from '../../utils/util';
import useInspectorStore from '../../stores/inspectorStore';
import useToolStore from '../../stores/toolStore';

const debug = Debug('aido:llm:services:NextChatService');

export default abstract class NextCharService {
  name: string;
  abortController: AbortController;
  context: IChatContext;
  provider: IServiceProvider;


  protected abstract getReaderType(): new (
    reader: ReadableStreamDefaultReader<Uint8Array>,
  ) => IChatReader;

  protected onCompleteCallback: (result: any) => Promise<void>;

  protected onReadingCallback: (chunk: string, reasoning?: string, emotion?: string, audioData?: ArrayBuffer) => void;

  protected onToolCallsCallback: (toolName: string, toolArgs?: any) => void;

  protected onToolResultCallback: (toolName: string, toolResult?: any) => void;

  protected onErrorCallback: (error: any, aborted: boolean) => void;

  protected usedToolNames: string[] = [];

  protected inputTokens = 0;

  protected outputTokens = 0;

  protected traceTool: (chatId: string, label: string, msg: string) => void;

  protected getSystemRoleName() {
    return 'system';
  }

  constructor({
    name,
    context,
    provider,
  }: {
    name: string;
    context: IChatContext;
    provider: IServiceProvider;
  }) {
    this.name = name;
    this.provider = provider;
    this.context = context;
    this.abortController = new AbortController();
    this.traceTool = useInspectorStore.getState().trace;

    this.onCompleteCallback = async () => {};
    this.onReadingCallback = () => {};
    this.onToolCallsCallback = () => {};
    this.onToolResultCallback = () => {};
    this.onErrorCallback = () => {};
  }

  protected createReader(
    reader: ReadableStreamDefaultReader<Uint8Array>,
  ): IChatReader {
    const ReaderType = this.getReaderType();
    return new ReaderType(reader);
  }

  protected abstract makeToolMessages(
    tool: ITool,
    toolResult: any,
    content?: string,
  ): IChatRequestMessage[];

  protected abstract makeTool(
    tool: IMCPTool,
  ): IOpenAITool | IAnthropicTool | IGoogleTool;

  protected abstract makePayload(
    messages: IChatRequestMessage[],
    msgId?: string,
  ): Promise<IChatRequestPayload>;

  protected abstract makeRequest(
    messages: IChatRequestMessage[],
    msgId?: string,
  ): Promise<Response>;

  protected getModelName() {
    const model = this.context.getModel();
    return model.name;
  }

  public onComplete(callback: (result: any) => Promise<void>) {
    this.onCompleteCallback = callback;
  }

  public onReading(callback: (chunk: string, reasoning?: string, emotion?: string, audioData?: ArrayBuffer) => void) {
    this.onReadingCallback = callback;
  }

  public onToolCalls(callback: (toolName: string, toolArgs?: any) => void) {
    this.onToolCallsCallback = callback;
  }

  public onToolResult(callback: (toolName: string, toolResult?: any) => void) {
    this.onToolResultCallback = callback;
  }

  public onError(callback: (error: any, aborted: boolean) => void) {
    this.onErrorCallback = callback;
  }

  // eslint-disable-next-line class-methods-use-this
  protected onReadingError(chunk: string) {
    try {
      const { error } = JSON.parse(chunk);
      console.error(error);
    } catch (err) {
      throw new Error(`Something went wrong`);
    }
  }

  // eslint-disable-next-line class-methods-use-this
  protected async convertPromptContent(
    content: string,
  ): Promise<
    | string
    | IChatRequestMessageContent[]
    | IChatRequestMessageContent[]
    | IGeminiChatRequestMessagePart[]
  > {
    return stripHtmlTags(content);
  }

  public abort() {
    this.abortController?.abort();
  }

  public isToolsEnabled() {
    // 检查模型是否支持工具调用
    const modelSupportsTools = this.context.getModel()?.capabilities?.tools?.enabled || false;
    
    // 检查用户界面上的工具开关是否启用
    const userToolsEnabled = useToolStore.getState().isToolsEnabled();
    
    // 只有当模型支持工具调用且用户启用了工具开关时，才返回 true
    return modelSupportsTools && userToolsEnabled;
  }

  public async chat(messages: IChatRequestMessage[], msgId?: string) {
    const chatId = this.context.getActiveChat().id;
    this.abortController = new AbortController();
    let reply = '';
    let reasoning = '';
    let signal: any = null;
    try {
      signal = this.abortController.signal;
      const response = await this.makeRequest(messages, msgId);
      debug(
        `${this.name} Start Reading:`,
        response.status,
        response.statusText,
      );
      if (response.status !== 200) {
        const contentType = response.headers.get('content-type');
        let msg;
        let json;
        if (response.status === 404) {
          msg = `${response.url} not found, verify your API base.`;
        } else if (contentType?.includes('application/json')) {
          json = await response.json();
        } else {
          msg = await response.text();
        }
        raiseError(response.status, json, msg);
      }
      const reader = response.body?.getReader();
      if (!reader) {
        this.onErrorCallback(new Error('No reader'), false);
        return;
      }
      const chatReader = this.createReader(reader);
      const readResult = await chatReader.read({
        onError: (err: any) => {
          this.onErrorCallback(err, !!signal?.aborted);
        },
        onProgress: (replyChunk: string, reasoningChunk?: string) => {
          reply += replyChunk;
          reasoning += reasoningChunk || '';
          this.onReadingCallback(replyChunk, reasoningChunk);
        },
        onToolCalls: this.onToolCallsCallback,
      });
      if (readResult?.inputTokens) {
        this.inputTokens += readResult.inputTokens;
      }
      if (readResult?.outputTokens) {
        this.outputTokens += readResult.outputTokens;
      }
      
      await this.onCompleteCallback({
        content: reply,
        reasoning,
        inputTokens: this.inputTokens,
        outputTokens: this.outputTokens,
      });
      this.inputTokens = 0;
      this.outputTokens = 0;
    } catch (error: any) {
      this.onErrorCallback(error, !!signal?.aborted);
      await this.onCompleteCallback({
        content: reply,
        reasoning,
        inputTokens: this.inputTokens,
        outputTokens: this.outputTokens,
        error: {
          code: error.code || 500,
          message: error.message || error.toString(),
        },
      });
      this.inputTokens = 0;
      this.outputTokens = 0;
    }
  }
}
