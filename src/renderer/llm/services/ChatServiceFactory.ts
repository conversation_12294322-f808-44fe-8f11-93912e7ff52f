import { IChatContext } from "../../types/llm";
import NextChatService from "./NextChatService";

import WebSocketChatService from "./WebSocketChatService";


/**
 * Creates a concrete NextChatService instance based on the provider
 * specified in the IChatContext.
 *
 * @param context The current chat context.
 * @returns An instance of a NextChatService subclass, or null if no matching service is found.
 */
export function createChatService(context: IChatContext): NextChatService | null {
  const providerName = context.getProvider()?.name?.toLowerCase();

  if (!providerName) {
    console.error("ChatServiceFactory: Provider name is missing in context.");
    return null;
  }

  console.log(`ChatServiceFactory: Creating service for provider: ${providerName}`);

  // --- Add cases for each supported provider ---
  switch (providerName) {

    case 'WebSocket':
      return WebSocketChatService.getInstance(context);
    default:
      console.warn(`ChatServiceFactory: No specific chat service found for provider "${providerName}".`);
      // 默认使用WebSocket服务
      return WebSocketChatService.getInstance(context);
  }
} 