import { IServiceProvider } from './types';

const chatModels = [
  {
    id: 'deepseek-v3-250324',
    name: 'doubao-seed-1-6-flash-250615',
    contextWindow: 65536,
    maxTokens: 8192,
    defaultMaxTokens: 8000,
    inputPrice: 0.0006,
    outputPrice: 0.002,
    isDefault: true,
    description: `60 tokens/second, Enhanced capabilities,API compatibility intact`,
    capabilities: {
      tools: {
        enabled: true,
      },
    },
  },
  {
    id: 'deepseek-r1-250528',
    name: 'doubao-seed-1-6-flash-250615',
    contextWindow: 65536,
    maxTokens: 8192,
    defaultMaxTokens: 8000,
    inputPrice: 0.003,
    outputPrice: 0.016,
    isDefault: false,
    description: `DeepSeek R1 reasoning model with enhanced performance`,
    capabilities: {
      tools: {
        enabled: true,
      },
    },
  },
];

export default {
  name: 'DeepSeek',
  apiBase: 'https://yunwu.ai/v1',
  apiKey: 'sk-NJC0MqLNorBCTjj9SldFAFyszaL9kW27glewjYJdj3yLA80z',
  currency: 'CNY',
  options: {
    apiBaseCustomizable: true,
    apiKeyCustomizable: true,
  },
  chat: {
    apiSchema: ['base', 'key'],
    presencePenalty: { min: -2, max: 2, default: 0 },
    topP: { min: 0, max: 1, default: 1 },
    temperature: { min: 0, max: 2, default: 1 },
    options: {
      modelCustomizable: true,
    },
    models: chatModels,
  },
} as IServiceProvider;
