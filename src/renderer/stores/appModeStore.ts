import { create } from 'zustand';
import { AppMode } from '../types/app';

interface AppModeStore {
  currentMode: AppMode;
  setAppMode: (mode: AppMode) => void;
  isClipboardMode: () => boolean;
}

export const useAppModeStore = create<AppModeStore>((set, get) => ({
  currentMode: AppMode.HOME,
  setAppMode: (mode: AppMode) => set({ currentMode: mode }),
  isClipboardMode: () => get().currentMode === AppMode.CLIPBOARD_HISTORY,
})); 