import { create } from 'zustand'
// import appSearch from '../../core/app-search';
// 默认值
const defaultState = {
  resizable: false,
  showTool: true,
  language: 'zh' as 'zh' | 'en'
}

export interface WinState {
  win: {
    resizable: boolean
    showTool: boolean
    language: 'zh' | 'en'
  }
}

export interface WinActions {
  setResizable: (resizable: boolean) => void
  setSwitchTool: (showTool: boolean) => void 
  setLanguage: (language: 'zh' | 'en') => void
  initStore: () => Promise<void>
}

export type WinStore = WinState & WinActions

// 创建独立的WinStore
export const useWinStore = create<WinStore>()((set) => ({
  win: {
    ...defaultState
  },
  
  // 初始化store，异步获取配置
  initStore: async () => {
    try {
      const resizable = await window.electron.window.isResizable();
      const config = await window.electron.window.getConfig() || {};


      
      set({
        win: {
          resizable,
          showTool: config.showTool ?? defaultState.showTool,
          language: config.language ?? defaultState.language,
        }
      });
    } catch (error) {
      console.error('初始化窗口store失败:', error);
    }
  },
  
  setResizable: (resizable) => {
    window.electron.window.setResizable(resizable)
    set((state) => ({ win: { ...state.win, resizable } }))
  },
  
  setSwitchTool: (showTool) => set((state) => ({ 
    win: { ...state.win, showTool } 
  })),
  
  setLanguage: (language) => set((state) => ({ 
    win: { ...state.win, language } 
  })),
})) 