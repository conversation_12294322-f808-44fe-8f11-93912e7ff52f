import { create } from 'zustand';
import type { SystemInfo } from '../../shared/ipc';

interface SystemStore {
  // 状态
  systemInfo: SystemInfo | null;
  websocketUrl: string | null;
  isLoading: boolean;
  error: string | null;
  
  // 方法
  fetchSystemInfo: () => Promise<SystemInfo>;
  setWebsocketUrl: (url: string) => void;
  clearError: () => void;
}

export const useSystemStore = create<SystemStore>((set, get) => ({
  // 初始状态
  systemInfo: null,
  websocketUrl: null,
  isLoading: false,
  error: null,
  
  // 获取系统信息
  fetchSystemInfo: async () => {
    try {
      set({ isLoading: true, error: null });
      
      if (!window.electron?.system?.getInfo) {
        throw new Error('系统API不可用');
      }
      
      const info = await window.electron.system.getInfo();
      console.log('🔍 SystemStore: 获取到系统信息:', {
        computerId: info.computerId,
        platform: info.platform
      });
      localStorage.setItem('deviceId', info.computerId);
      set({
        systemInfo: info,
        isLoading: false,
        error: null
      });

      // 验证store状态是否正确设置
      const currentState = get();
      console.log('🔍 SystemStore: 当前状态:', {
        hasSystemInfo: !!currentState.systemInfo,
        computerId: currentState.systemInfo?.computerId
      });

      return info; // 返回系统信息
    } catch (err) {
      console.error('获取系统信息失败:', err);
      set({ 
        error: `获取系统信息失败: ${(err as Error).message}`,
        isLoading: false 
      });
      throw err;
    }
  },
  
  // 设置 WebSocket URL
  setWebsocketUrl: (url: string) => {
    set({ websocketUrl: url });
  },
  
  // 清除错误
  clearError: () => {
    set({ error: null });
  },
}));

// 将systemStore暴露到全局对象，以便主进程通过executeJavaScript访问
if (typeof window !== 'undefined') {
  (window as any).useSystemStore = useSystemStore;
  console.log('🔍 SystemStore: 已暴露到window对象');

  // 添加一个测试方法来验证store状态
  (window as any).testSystemStore = () => {
    const state = useSystemStore.getState();
    console.log('🔍 SystemStore测试:', {
      hasSystemInfo: !!state.systemInfo,
      computerId: state.systemInfo?.computerId,
      isLoading: state.isLoading,
      error: state.error
    });
    return state;
  };
}