import Debug from 'debug';
import { MCPConfig, MCPServer } from '../../shared/types/mcp';
import { create } from 'zustand';

const debug = Debug('aido:stores:useMCPStore');

export interface IMCPStore {
  isLoading: boolean;
  config: MCPConfig;
  updateLoadingState: (isLoading: boolean) => void;
  loadConfig: (force?: boolean) => Promise<MCPConfig>;
  addServer: (key: string, server: MCPServer) => Promise<boolean>;
  updateServer: (key: string, server: MCPServer) => Promise<boolean>;
  deleteServer: (key: string) => Promise<boolean>;
  activateServer: (
    key: string,
    command?: string,
    args?: string[],
    env?: Record<string, string>,
  ) => Promise<boolean>;
  deactivateServer: (key: string) => Promise<boolean>;
}

const useMCPStore = create<IMCPStore>((set, get) => ({
  isLoading: true,
  config: { servers: {} },
  updateLoadingState: (isLoading: boolean) => {
    set({ isLoading });
  },
  loadConfig: async (force?: boolean) => {
    if (!force && Object.keys(get().config.servers).length > 0) {
      return get().config;
    }
    const config = await window.electron.mcp.getConfig();
    set({ config });
    return config;
  },
  addServer: async (key: string, server: MCPServer) => {
    const ok = await window.electron.mcp.addServer(key, server);
    if (ok) {
      get().loadConfig(true);
      return true;
    }
    return false;
  },
  updateServer: async (key: string, server: MCPServer) => {
    const ok = await window.electron.mcp.updateServer(key, server);
    if (ok) {
      get().loadConfig(true);
      return true;
    }
    return false;
  },
  deleteServer: async (key: string) => {
    const { servers } = get().config;
    const server = servers[key];
    if (server) {
      const ok = await get().deactivateServer(key);
      if (ok) {
        const { servers } = get().config;
        const newServers = { ...servers };
        delete newServers[key];
        const newConfig = { servers: newServers };
        set({ config: newConfig });
        await window.electron.mcp.putConfig(newConfig);
        return true;
      }
    }
    return false;
  },
  activateServer: async (
    key: string,
    command?: string,
    args?: string[],
    env?: Record<string, string>,
  ) => {
    debug('Activating server:', {
      key,
      command,
      args,
      env,
    });
    
    // 获取当前配置中的 server
    const { servers } = get().config;
    const server = servers[key];
    if (!server) {
      throw new Error(`Server with key ${key} not found`);
    }
    
    const { error } = await window.electron.mcp.activate({
      key,
      server,
      command,
      args,
      env,
    });
    if (error) {
      throw new Error(error);
    }
    await get().loadConfig(true);
    return true;
  },
  deactivateServer: async (key: string) => {
    const { error } = await window.electron.mcp.deactivated(key);
    if (error) {
      throw new Error(error);
    }
    await get().loadConfig(true);
    return true;
  },
}));

export default useMCPStore;
