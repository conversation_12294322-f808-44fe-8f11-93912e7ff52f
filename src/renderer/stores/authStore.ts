import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import baseApi from '../api/base';

/**
 * 广播用户状态变化到所有窗口
 */
const broadcastUserStatusChange = async (user: User | null, token?: string | null) => {
  try {
    // 使用模块化的 auth API
    if (typeof window !== 'undefined' && window.electron && window.electron.auth) {
      // 广播包含token的完整状态
      const authState = { user, token };
      const result = await window.electron.auth.broadcastUserStatusChange(authState);
      console.log('✅ 用户状态变化广播结果:', result);
    } else {
      console.warn('❌ electron.auth API 不可用，跳过广播用户状态变化');
    }
  } catch (error) {
    console.error('💥 广播用户状态变化失败:', error);
  }
};

export interface User {
  headerImg: string;
  id: string;
  userName: string;
  email?: string;
  avatar?: string;
  nickName?: string;
  createTime?: string;
  lastLoginTime?: string;
  vip_level?: number;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isLoggedIn: boolean;
  isLoading: boolean;
  loginDialogOpen: boolean;
  _skipBroadcast?: boolean; // 内部标志，用于防止循环广播
}

// 更灵活的登录参数类型
export interface LoginParams {
  provider: string;
  username?: string;
  password?: string;
  phone?: string;
  code?: string;
  validate?: string;
  metadata?: string;
}

export interface AuthActions {
  login: (params: LoginParams | { user: User; token: string }) => Promise<{ success: boolean; message?: string }>;
  logout: (forceSkipBroadcast?: boolean) => void;
  setUser: (user: User) => void;
  setToken: (token: string) => void;
  setLoading: (loading: boolean) => void;
  openLoginDialog: () => void;
  closeLoginDialog: () => void;
  checkAuthStatus: () => Promise<boolean>;
}

export type AuthStore = AuthState & AuthActions;

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get): AuthStore => ({
      // 初始状态
      user: null,
      token: null,
      isLoggedIn: false,
      isLoading: false,
      loginDialogOpen: false,
      _skipBroadcast: false,

      // 登录方法 - 支持直接设置用户信息或API登录
      login: async (params: LoginParams | { user: User; token: string }) => {
        // 如果直接传入用户信息和token（例如从其他登录方式获得）
        if ('user' in params && 'token' in params) {
          set({
            user: params.user,
            token: params.token,
            isLoggedIn: true,
            isLoading: false,
            loginDialogOpen: false,
          });
          // 广播用户状态变化到所有窗口
          broadcastUserStatusChange(params.user, params.token);
          
          // 通知主进程登录成功
          if (window.electron?.auth?.notifyLoginSuccess) {
            window.electron.auth.notifyLoginSuccess();
          }
          
          return { success: true };
        }

        // API登录
        set({ isLoading: true });
        
        try {
          const result: any = await baseApi.unifiedLogin(params);
          console.log(result, "result");
          if(result.code === 0) {
            console.log('登录成功，设置用户信息:', result.data.user);
            set({
              user: result.data.user,
              token: result.data.token,
              isLoggedIn: true,
              isLoading: false,
              loginDialogOpen: false,
            });
            // 通知主进程登录成功
            if (window.electron?.auth?.notifyLoginSuccess) {
              window.electron.auth.notifyLoginSuccess();
            }
            // 广播用户状态变化到所有窗口，包含token
            broadcastUserStatusChange(result.data.user, result.data.token);
            return { success: true };
          } else {
            set({ isLoading: false });
            console.error('登录失败:', result.message);
            return { success: false, message: result.message || '登录失败，请重试' };
          }
        } catch (error: any) {
          set({ isLoading: false });
          const errorMessage = error?.response?.data?.message || error?.message || '网络错误，请检查网络连接后重试';
          return { success: false, message: errorMessage };
        }
      },

      // 登出方法
      logout: (forceSkipBroadcast?: boolean) => {
        const currentState = get();

        set({
          user: null,
          token: null,
          isLoggedIn: false,
          loginDialogOpen: false, // 确保登出时关闭登录弹窗
          _skipBroadcast: false, // 重置标志
        });

        // 清除localStorage中的token缓存
        try {
          const authData = localStorage.getItem('auth-storage');
          if (authData) {
            const parsed = JSON.parse(authData);
            const data = parsed.state || parsed;
            if (data) {
              data.token = null;
              data.user = null;
              data.isLoggedIn = false;
              localStorage.setItem('auth-storage', JSON.stringify({ state: data }));
              console.log('✅ 已清除localStorage中的token缓存');
            }
          }
        } catch (error) {
          console.error('清除localStorage缓存失败:', error);
        }

        // 只有在明确强制跳过广播时才跳过，或者在接收到广播状态时跳过
        const shouldSkipBroadcast = forceSkipBroadcast || currentState._skipBroadcast;
        
        // 只有在不跳过广播时才通知主进程和广播用户状态变化
        if (!shouldSkipBroadcast) {
          // 通知主进程用户已登出
          if (window.electron?.auth?.notifyLogout) {
            window.electron.auth.notifyLogout();
          }
          
          // 广播用户状态变化到所有窗口
          broadcastUserStatusChange(null, null);
        } else {
          console.log('跳过广播，仅清除本地状态');
        }
      },

      // 设置用户信息
      setUser: (user: User) => {
        const currentState = get();
        set({
          user,
          isLoggedIn: true,
          // 保留现有的token，不要覆盖
          token: currentState.token,
          _skipBroadcast: false, // 重置标志
        });

        // 只有在不跳过广播时才广播
        if (!currentState._skipBroadcast) {
          broadcastUserStatusChange(user, currentState.token);
        }
      },

      // 设置token
      setToken: (token: string) => {
        set({ token });
        
        // 通知主进程登录成功，重新获取websocket地址
        if (window.electron?.auth?.notifyLoginSuccess) {
          window.electron.auth.notifyLoginSuccess();
        }
      },

      // 设置加载状态
      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      // 打开登录弹窗
      openLoginDialog: () => {
        set({ loginDialogOpen: true });
      },

      // 关闭登录弹窗
      closeLoginDialog: () => {
        set({ loginDialogOpen: false });
      },

      // 检查认证状态
      checkAuthStatus: async () => {
        const { token, user } = get();
        
        if (!token || !user) {
          set({ isLoggedIn: false });
          return false;
        }

        // 在实际项目中，这里应该验证token的有效性
        // 比如调用后端API验证token
        try {
          // 模拟token验证
          set({ isLoggedIn: true });
          return true;
        } catch (error) {
          console.error('Token验证失败:', error);
          set({ 
            user: null, 
            token: null, 
            isLoggedIn: false 
          });
          return false;
        }
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => localStorage),
      // 只持久化用户信息和token，不持久化loading状态和弹窗状态
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isLoggedIn: state.isLoggedIn,
      } as Pick<AuthState, 'user' | 'token' | 'isLoggedIn'>),
    }
  )
);

// 将authStore暴露到全局对象，以便主进程通过executeJavaScript访问
if (typeof window !== 'undefined') {
  (window as any).useAuthStore = useAuthStore;

  // 同时暴露当前状态供其他模块使用
  const updateGlobalState = () => {
    const state = useAuthStore.getState();
    (window as any).__authStore__ = state;
    console.log('全局authStore状态已更新:', {
      user: state.user,
      vip_level: state.user?.vip_level,
      isLoggedIn: state.isLoggedIn
    });
  };

  // 初始化全局状态
  updateGlobalState();

  // 监听状态变化并更新全局状态
  useAuthStore.subscribe(() => {
    updateGlobalState();
  });

  // 监听主进程发送的清除所有token事件
  if (window.electron?.auth?.onClearAllTokens) {
    const unsubscribe = window.electron.auth.onClearAllTokens(() => {
      console.log('收到主进程清除所有token事件，清除本地缓存...');
      
      // 清除localStorage中的token缓存
      try {
        const authData = localStorage.getItem('auth-storage');
        if (authData) {
          const parsed = JSON.parse(authData);
          const data = parsed.state || parsed;
          if (data) {
            data.token = null;
            data.user = null;
            data.isLoggedIn = false;
            localStorage.setItem('auth-storage', JSON.stringify({ state: data }));
            console.log('✅ 已清除localStorage中的token缓存');
          }
        }
      } catch (error) {
        console.error('清除localStorage缓存失败:', error);
      }
      
      // 直接更新store状态，不调用logout方法避免循环
      const currentState = useAuthStore.getState();
      if (currentState.isLoggedIn) {
        useAuthStore.setState({
          user: null,
          token: null,
          isLoggedIn: false,
          loginDialogOpen: false,
          _skipBroadcast: true, // 跳过广播避免循环
        });
        console.log('✅ 已更新authStore状态为未登录');
      }
    });
    
    // 在页面卸载时清理监听器
    window.addEventListener('beforeunload', () => {
      unsubscribe();
    });
  }
}