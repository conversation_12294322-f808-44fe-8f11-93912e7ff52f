import { find, isNil, keyBy, omit, pickBy, unionBy } from 'lodash';
import { getBuiltInProviders } from '../llm/providers';
import {
  IChatModel,
  IChatModelConfig,
  IChatProviderConfig,
} from '../llm/providers/types';
import { genDefaultName } from '../utils/util';
import { create } from 'zustand';
import OpenAI from '../llm/providers/OpenAI';
import {
  DEFAULT_CONTEXT_WINDOW,
  DEFAULT_MAX_TOKENS,
  ERROR_MODEL,
  MAX_TOKENS,
} from '../utils/consts';
import { isBlank } from '../utils/validators';
import { typeid } from 'typeid-js';
import { modelApi } from '../api/thirdParty';

const ErrorModel = {
  id: ERROR_MODEL,
  name: ERROR_MODEL,
  label: 'Invalid Model',
  isReady: false,
  isDefault: false,
  contextWindow: DEFAULT_CONTEXT_WINDOW,
  capabilities: {},
  maxTokens: MAX_TOKENS,
  defaultMaxTokens: DEFAULT_MAX_TOKENS,
  inputPrice: 0,
  outputPrice: 0,
  description: 'Error fetching models',
  isBuiltIn: true,
} as IChatModelConfig;

const isProviderReady = (provider: IChatProviderConfig) => {
  if (!provider.apiBase) {
    return false;
  }
  try {
    const url = new URL(provider.apiBase);
    const isReady = ['http:', 'https:'].includes(url.protocol);
    if (isReady) {
      return !provider.schema.includes('key') || !isBlank(provider.apiKey);
    }
    return false;
  } catch {
    return false;
  }
};

const sortByName = (a: { name: string }, b: { name: string }) =>
  (a.name || '').localeCompare(b.name || '');

const isModelReady = (
  modelExtras: string[],
  model: IChatModelConfig,
): boolean => {
  return modelExtras.every((key: string) => {
    return (
      !isNil(model.extras?.[key]) &&
      (model.extras?.[key] as string).trim() !== ''
    );
  });
};

const mergeRemoteModel = (
  modelName: string,
  customModel?: IChatModelConfig,
) => {
  return {
    id: modelName,
    name: modelName,
    label:
      customModel?.label ||
      (modelName === ERROR_MODEL ? 'Invalid Model' : modelName),
    isReady: modelName !== ERROR_MODEL,
    isDefault: customModel?.isDefault || false,
    contextWindow: customModel?.contextWindow || DEFAULT_CONTEXT_WINDOW,
    capabilities: customModel?.capabilities || {},
    maxTokens: customModel?.maxTokens || MAX_TOKENS,
    defaultMaxTokens: customModel?.defaultMaxTokens || DEFAULT_MAX_TOKENS,
    inputPrice: customModel?.inputPrice || 0,
    outputPrice: customModel?.outputPrice || 0,
    description: customModel?.description || '',
    isFromApi: true,
    isBuiltIn: true, // remote models are always built-in
    disabled: customModel?.disabled || false,
  } as IChatModelConfig;
};

const getMergedLocalModels = (provider: IChatProviderConfig) => {
  const builtInProviders = getBuiltInProviders();
  const builtinModels =
    builtInProviders.find((p) => p.name === provider.name)?.chat.models || [];
  const userCreatedModels =
    provider?.models
      ?.filter((model: IChatModelConfig) => {
        return (
          !provider.isBuiltIn ||
          !builtinModels?.some(
            (builtInModel: IChatModel) => builtInModel.id === model.id,
          )
        );
      })
      .map((model) => {
        model.isBuiltIn = false;
        model.label = model.label || model.name;
        model.isReady = model.name !== ERROR_MODEL;
        return model;
      }) || [];
  const customModels = keyBy(provider?.models || [], 'id');
  return [
    ...(builtinModels?.map((model) => {
      const customModel = customModels[model.id];
      const mergedModel = {
        id: model.id,
        name: customModel?.name || model.name,
        label: customModel?.label || model.label || model.name,
        contextWindow: customModel?.contextWindow || model.contextWindow,
        maxTokens: customModel?.maxTokens || model.maxTokens,
        defaultMaxTokens:
          customModel?.defaultMaxTokens || model.defaultMaxTokens,
        inputPrice: customModel?.inputPrice || model.inputPrice,
        outputPrice: customModel?.outputPrice || model.outputPrice,
        description: customModel?.description || model.description || null,
        isDefault: customModel?.isDefault || model.isDefault,
        isBuiltIn: true,
        isPremium: provider.isPremium,
        noStreaming: model.noStreaming || false,
        disabled: customModel?.disabled || false,
        capabilities: customModel?.capabilities || model.capabilities || {},
        extras: { ...model.extras, ...customModel?.extras },
      } as IChatModelConfig;
      mergedModel.isReady = isModelReady(
        provider.modelExtras as string[],
        mergedModel,
      );
      return mergedModel;
    }) || []),
    ...userCreatedModels,
  ].sort(sortByName);
};

export type ModelOption = {
  label: string;
  name: string;
  isReady: boolean;
  isDefault: boolean;
};

const mergeProviders = (
  custom?: IChatProviderConfig[],
): IChatProviderConfig[] => {
  const customProviders = keyBy(
    custom || window.electron.store?.get('providers'),
    'name',
  );
  const builtInProviders = keyBy(getBuiltInProviders(), 'name');
  const mergedProviders = unionBy(
    [...Object.values(customProviders), ...Object.values(builtInProviders)],
    'name',
  ).map((provider) => {
    const customProvider = customProviders[provider.name];
    const builtInProvider = builtInProviders[provider.name];
    const defaultProvider = { ...OpenAI };
    const mergedProvider = {
      name: provider.name,
      description:
        customProvider?.description || builtInProvider?.description || '',
      schema: builtInProvider?.chat?.apiSchema || ['base'],
      apiBase: customProvider?.apiBase || builtInProvider?.apiBase,
      apiKey: customProvider?.apiKey || builtInProvider?.apiKey || '',
      apiVersion: customProvider?.apiVersion || builtInProvider?.apiVersion,
      temperature: (builtInProvider || defaultProvider).chat.temperature,
      topP: (builtInProvider || defaultProvider).chat.topP,
      presencePenalty: (builtInProvider || defaultProvider).chat
        .presencePenalty,
      currency:
        customProvider?.currency ||
        (builtInProvider || defaultProvider).currency,
      isDefault: customProvider?.isDefault || false,
      isPremium: !!builtInProvider?.isPremium,
      disabled: customProvider?.disabled || false,
      isBuiltIn: !!builtInProvider,
      modelExtras: builtInProvider?.chat.modelExtras || [],
      modelsEndpoint: builtInProvider?.options?.modelsEndpoint,
      isReady: false,
      models: customProvider?.models || [],
    } as IChatProviderConfig;
    mergedProvider.isReady = isProviderReady(mergedProvider);
    return mergedProvider;
  });
  return mergedProviders.sort(sortByName);
};

export interface IProviderStore {
  providers: IChatProviderConfig[];
  provider: IChatProviderConfig | null;
  setProvider: (provider: IChatProviderConfig) => IChatProviderConfig;
  createProvider: (providerName?: string) => void;
  updateProvider: (
    name: string,
    provider: Partial<IChatProviderConfig>,
  ) => void;
  overwrite: (providers: IChatProviderConfig[]) => void;
  deleteProvider: (providerName: string) => void;
  isProviderDuplicated: (providerName: string) => boolean;
  getDefaultProvider: () => IChatProviderConfig;
  getAvailableProviders: (options?: {
    withDisabled?: boolean;
  }) => IChatProviderConfig[];
  getAvailableProvider: (providerName: string) => IChatProviderConfig;
  getAvailableModel: (
    providerName: string,
    modelName: string,
  ) => IChatModelConfig;
  getModelsSync: (
    provider: IChatProviderConfig,
    options?: { withDisabled?: boolean },
  ) => IChatModelConfig[];
  getModels: (
    provider: IChatProviderConfig,
    options?: { withDisabled?: boolean },
  ) => Promise<IChatModelConfig[]>;
  getGroupedModelOptions: () => Promise<{
    [key: string]: ModelOption[];
  }>;
  createModel: (model: IChatModelConfig) => void;
  updateModel: (model: Partial<IChatModelConfig> & { id: string }) => void;
  deleteModel: (modelId: string) => void;
}

const useProviderStore = create<IProviderStore>((set, get) => ({
  providers: mergeProviders(),
  provider: null,
  setProvider: (provider: IChatProviderConfig) => {
    set({ provider });
    return provider;
  },
  overwrite: (providers: IChatProviderConfig[]) => {
    const { provider } = get();
    window.electron.store.set('providers', providers);
    const mergedProviders = mergeProviders(providers);
    set({ providers: mergedProviders });
    if (provider) {
      const newProvider = mergedProviders.find((p) => p.name === provider.name);
      if (newProvider) {
        set({ provider: newProvider });
      } else {
        set({ provider: mergedProviders[0] });
      }
    }
  },
  createProvider: (providerName = 'Untitled') => {
    const { providers } = get();
    const names = providers.map((provider) => provider.name);
    const defaultName = genDefaultName(names, providerName);
    const newProvider = {
      name: defaultName,
      apiBase: '',
      currency: 'USD',
      apiKey: '',
      isDefault: false,
      models: [],
      disabled: false,
    } as Partial<IChatProviderConfig>;
    const customProviders = window.electron.store.get('providers');
    const newCustomProviders = (
      [...customProviders, newProvider] as IChatProviderConfig[]
    ).sort(sortByName);
    window.electron.store.set('providers', newCustomProviders);
    const newProviders = mergeProviders(newCustomProviders);
    set({
      provider: find(newProviders, { name: defaultName }),
      providers: newProviders,
    });
  },
  updateProvider: (name: string, provider: Partial<IChatProviderConfig>) => {
    const customProviders = window.electron.store.get('providers') || [];
    let found = false;
    const newProvider = omit(provider, ['isBuiltIn', 'isReady', 'isPremium']);
    const updatedProviders = customProviders.map(
      (providerItem: IChatProviderConfig) => {
        if (providerItem.name === name) {
          found = true;
          return {
            ...providerItem,
            ...newProvider,
          };
        }
        if (provider.isDefault) {
          providerItem.isDefault = false;
        }
        return providerItem;
      },
    ) as IChatProviderConfig[];
    if (!found) {
      updatedProviders.push({ ...newProvider, name } as IChatProviderConfig);
    }
    window.electron.store.set('providers', updatedProviders);
    const providers = mergeProviders(updatedProviders);
    set({ providers });
    if (get().provider?.name === name) {
      set({
        provider:
          providers.find((p) => p.name === (provider.name || name)) || null,
      });
    }
  },
  deleteProvider: (providerName: string) => {
    const customProviders = window.electron.store.get('providers');
    const updatedProviders = customProviders.filter(
      (providerItem: IChatProviderConfig) => {
        return providerItem.name !== providerName;
      },
    ) as IChatProviderConfig[];
    window.electron.store.set('providers', updatedProviders);
    const newProviders = mergeProviders(updatedProviders);
    const { provider } = get();
    if (provider?.name === providerName) {
      set({ provider: newProviders[0] });
    }
    set({ providers: newProviders });
  },
  isProviderDuplicated: (providerName: string) => {
    const { providers } = get();
    return providers.map((provider) => provider.name).includes(providerName);
  },
  getAvailableProviders: ({
    withDisabled,
  }: { withDisabled?: boolean } = {}) => {
    const { providers } = get();
    const enabledProviders = providers.filter(
      (p) => withDisabled || !p.disabled,
    );
    // const { session } = useAuthStore.getState();
    // if (session) return enabledProviders;
    return enabledProviders.filter((p) => !p.isPremium);
  },
  getAvailableProvider: (providerName: string) => {
    const { getAvailableProviders } = get();
    const providers = getAvailableProviders();
    return (
      find(providers, { name: providerName }) ||
      find(providers, { isDefault: true }) ||
      providers[0]
    );
  },
  getDefaultProvider: () => {
    const { getAvailableProviders } = get();
    const providers = getAvailableProviders();
    return find(providers, { isDefault: true }) || providers[0];
  },
  getAvailableModel: (providerName: string, modelName: string) => {
    const { getModelsSync, getAvailableProvider } = get();
    const provider = getAvailableProvider(providerName);
    if (provider.modelsEndpoint) {
      return mergeRemoteModel(modelName);
    }
    const models = getModelsSync(provider, { withDisabled: true });
    return (
      find(models, { name: modelName }) ||
      find(models, { isDefault: true }) ||
      models[0] ||
      ErrorModel
    );
  },
  getModelsSync: (
    provider: IChatProviderConfig,
    options?: { withDisabled?: boolean },
  ) => {
    let $models = [];
    if (provider.modelsEndpoint) {
      $models = provider.models.map((model) => {
        const customModel = provider.models.find(
          (m: IChatModelConfig) => m.name === model.name,
        );
        return mergeRemoteModel(model.name, customModel);
      });
    } else {
      $models = getMergedLocalModels(provider);
    }
    if (options?.withDisabled) {
      return $models;
    }
    return $models.filter((model) => model.isDefault);
  },
  getModels: async (
    provider: IChatProviderConfig,
    options?: { withDisabled?: boolean },
  ) => {
    const modelsMap = keyBy(provider.models || [], 'name');
    let $models: IChatModelConfig[] = [];
    if (provider.modelsEndpoint) {
      try {
        const resp = await modelApi.getModels(
          provider.apiBase,
          provider.modelsEndpoint,
          {
            'Content-Type': 'application/json',
          }
        );
        const data = resp.data;
        $models = (data.models || [])
          .filter((model: { name: string }) => model.name.indexOf('embed') < 0)
          .map((model: { name: string }) => {
            const customModel = modelsMap[model.name];
            delete modelsMap[model.name];
            return mergeRemoteModel(model.name, {
              ...customModel,
              isFromApi: true,
            });
          });
      } catch (e) {
        $models = [ErrorModel];
      }
    } else {
      $models = getMergedLocalModels(provider);
    }
    if (options?.withDisabled) {
      return $models;
    }
    return $models.filter((model) => !model.disabled);
  },
  getGroupedModelOptions: async () => {
    const { getAvailableProviders, getModels } = get();
    const result: { [key: string]: ModelOption[] } = {};
    const providers = getAvailableProviders();
    await Promise.all(
      providers.map(async (provider) => {
        const models = await getModels(provider);
        result[provider.name] = models
          .map((model) => ({
            label: model.label || model.name,
            name: model.name,
            isReady: model.isReady,
            isDefault: model.isDefault || false,
          }))
          .filter((model: ModelOption) => model.name !== ERROR_MODEL);
        return provider;
      }),
    );
    return pickBy(result, (val: ModelOption[]) => val.length > 0);
  },
  createModel: (model: IChatModelConfig) => {
    const { provider, updateProvider } = get();
    if (!provider) return;
    const customProviders = window.electron.store.get('providers');
    const customProvider = customProviders.find(
      (p: IChatProviderConfig) => p.name === provider.name,
    ) || {
      models: [],
    };
    model.id = typeid('mod').toString();
    const newModel = omit(model, ['isBuiltIn', 'isReady']) as IChatModelConfig;
    const updatedProvider = {
      name: provider.name,
      models: [...(customProvider.models || []), newModel],
    };
    updateProvider(provider.name, updatedProvider);
  },

  updateModel: (model: Partial<IChatModelConfig> & { id: string }) => {
    const { provider, updateProvider } = get();
    if (!provider) return;
    const customProviders = window.electron.store.get('providers');
    const customProvider = customProviders.find(
      (p: IChatProviderConfig) => p.name === provider.name,
    ) || {
      models: [],
    };
    let found = false;
    const newModel = omit(model, ['isBuiltIn', 'isReady']) as IChatModelConfig;
    const updatedModels =
      customProvider.models?.map((m: IChatModelConfig) => {
        if (m.id === model.id) {
          found = true;
          return { ...m, ...newModel };
        }
        if (model.isDefault) {
          m.isDefault = false;
        }
        return m;
      }) || [];
    if (!found) {
      updatedModels.push({ ...newModel } as IChatModelConfig);
    }
    const updatedProvider = {
      name: provider.name,
      models: updatedModels,
    };
    updateProvider(provider.name, updatedProvider);
  },

  deleteModel: (modelId: string) => {
    const { provider, updateProvider } = get();
    if (!provider) return;
    const customProviders = window.electron.store.get('providers');
    const customProvider = customProviders.find(
      (p: IChatProviderConfig) => p.name === provider.name,
    ) || {
      models: [],
    };
    const updatedProvider = {
      name: provider.name,
      models: customProvider.models.filter(
        (model: IChatModelConfig) => model.id !== modelId,
      ),
    };
    updateProvider(provider.name, updatedProvider);
  },
}));

export default useProviderStore;
