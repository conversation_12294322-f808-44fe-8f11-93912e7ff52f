import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import initModelList from '../models/models.json'

export interface ConfigState {
  config: {
    modelPath: string
    modelList: string[]
  }
}

export interface ConfigActions {
  setModelList: (modelList: string[]) => void
  setModelPath: (modelPath: string) => void
  nextModel: () => void
  prevModel: () => void
}

export type ConfigStore = ConfigState & ConfigActions

localStorage.removeItem('pet-config')

// 创建独立的ConfigStore
export const useConfigStore = create<ConfigStore>()(
  persist(
    (set) => ({
      config: {
        modelPath: initModelList[0],
        modelList: initModelList,
      },
      
      setModelList: (modelList) => set((state) => ({ 
        config: { ...state.config, modelList } 
      })),
      
      setModelPath: (modelPath) => set((state) => ({ 
        config: { ...state.config, modelPath } 
      })),
      
      
      nextModel: () => set((state) => {
        const { modelList, modelPath } = state.config
        let idx = modelList.findIndex((f) => modelPath === f)
        if (idx > -1) {
          if (++idx >= modelList.length) {
            idx = 0
          }
          return { config: { ...state.config, modelPath: modelList[idx] } }
        }
        return { config: state.config }
      }),
      
      prevModel: () => set((state) => {
        const { modelList, modelPath } = state.config
        let idx = modelList.findIndex((f) => modelPath === f)
        if (idx > -1) {
          if (--idx < 0) {
            idx = modelList.length - 1
          }
          return { config: { ...state.config, modelPath: modelList[idx] } }
        }
        return { config: state.config }
      }),
    }),
    {
      name: 'pet-config',
      storage: createJSONStorage(() => localStorage),
    }
  )
) 