import { create } from 'zustand';
import type { MCPTool } from '../../shared/types/mcpTool';

interface MCPToolsStore {
  // 状态
  tools: MCPTool[];
  loading: boolean;
  lastUpdated: number | null;
  error: string | null;
  
  // 操作
  loadTools: () => Promise<void>;
  refreshTools: () => Promise<void>;
  clearCache: () => void;
  
  // 搜索相关
  searchTools: (query: string) => MCPTool[];
}

const useMCPToolsStore = create<MCPToolsStore>((set, get) => ({
  // 初始状态
  tools: [],
  loading: false,
  lastUpdated: null,
  error: null,

  // 加载技能列表
  loadTools: async () => {
    const currentState = get();
    
    // 如果正在加载，避免重复请求
    if (currentState.loading) {
      console.log('MCP技能正在加载中，跳过重复请求');
      return;
    }

    set({ loading: true, error: null });
    
    try {
      console.log('开始加载MCP技能列表...');
      const response = await window.electron.mcp.getAllTools();
      
      if (response.error) {
        throw new Error(response.error);
      }
      
      const tools = response.tools || [];
      console.log(`成功加载 ${tools.length} 个MCP技能`);
      
      set({
        tools,
        loading: false,
        lastUpdated: Date.now(),
        error: null
      });
      
    } catch (error: any) {
      console.error('加载MCP技能失败:', error);
      set({
        tools: [],
        loading: false,
        error: error.message || '加载失败',
        lastUpdated: Date.now()
      });
    }
  },

  // 刷新技能列表
  refreshTools: async () => {
    console.log('刷新MCP技能缓存...');
    await get().loadTools();
  },

  // 清除缓存
  clearCache: () => {
    console.log('清除MCP技能缓存');
    set({
      tools: [],
      loading: false,
      lastUpdated: null,
      error: null
    });
  },

  // 在缓存中搜索技能
  searchTools: (query: string) => {
    const { tools } = get();
    
    if (!query.trim()) {
      return [];
    }
    
    const queryLower = query.toLowerCase();
    
    return tools.filter(tool => {
      // 搜索多个字段：中文描述、英文描述、中文名、英文名、关键词等
      const searchFields = [
        tool.c_name,
        tool.name,
        tool.fullName,
        tool.description,
        tool.descriptionChinese,
        tool.keywords
      ].filter(Boolean); // 过滤掉null/undefined值
      
      // 检查是否有任何字段匹配查询
      return searchFields.some(field => {
        if (field) {
          return field.toLowerCase().includes(queryLower);
        }
        return false;
      });
    });
  }
}));

export default useMCPToolsStore; 