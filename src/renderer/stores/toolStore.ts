import { create } from 'zustand';

export interface IToolState {
  tools: boolean;
  network: boolean;
  deepThinking: boolean;
  voice: boolean;
  pin: boolean;
}

export interface IToolStore {
  activeTools: IToolState;
  setToolEnabled: (tool: keyof IToolState, enabled: boolean) => void;
  toggleTool: (tool: keyof IToolState) => void;
  isToolsEnabled: () => boolean;
  isNetworkEnabled: () => boolean;
  isDeepThinkingEnabled: () => boolean;
  isVoiceEnabled: () => boolean;
  isPinEnabled: () => boolean;
}

const useToolStore = create<IToolStore>((set, get) => ({
  activeTools: {
    tools: true,
    network: false,
    deepThinking: false,
    voice: false,
    pin: false,
  },

  setToolEnabled: (tool, enabled) => {
    set((state) => ({
      activeTools: {
        ...state.activeTools,
        [tool]: enabled,
      },
    }));
  },

  toggleTool: (tool) => {
    set((state) => ({
      activeTools: {
        ...state.activeTools,
        [tool]: !state.activeTools[tool],
      },
    }));
  },

  isToolsEnabled: () => get().activeTools.tools,
  isNetworkEnabled: () => get().activeTools.network,
  isDeepThinkingEnabled: () => get().activeTools.deepThinking,
  isVoiceEnabled: () => get().activeTools.voice,
  isPinEnabled: () => get().activeTools.pin,
}));

export default useToolStore; 