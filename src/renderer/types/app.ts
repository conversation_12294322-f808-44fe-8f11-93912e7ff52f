export enum AppMode {
  HOME = 'home',
  SEARCH = 'search',
  AI_CHAT = 'ai-chat',
  FILE_SEARCH = 'file-search',
  CLIPBOARD_HISTORY = 'clipboard-history',
  TRANSLATOR = 'translator'
}

// 应用程序信息接口
export interface AppInfo {
  id: string;
  name: string;
  path: string;
  icon?: string;
}

// 搜索结果项接口
export interface ResultItem {
  id: string;
  name: string;
  shortcut: string;
  path?: string;
  execute?: (query: string) => Promise<void>;
  renderIcon?: () => React.ReactNode;
  renderContent?: (props?: any) => React.ReactNode;
}

// 定义主题选项
export type Theme = 'light' | 'dark' | 'system';

// 定义语言选项
export interface LanguageOption {
  code: string;
  name: string;
} 