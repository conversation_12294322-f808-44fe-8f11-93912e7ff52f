import { useAuthStore } from '../stores/authStore';
import { useCallback } from 'react';

/**
 * 用户认证相关的hook
 * 提供便捷的用户信息和认证状态访问
 */
export const useAuth = () => {
  const {
    user,
    token,
    isLoggedIn,
    isLoading,
    loginDialogOpen,
    login,
    logout,
    setUser,
    setToken,
    setLoading,
    openLoginDialog,
    closeLoginDialog,
    checkAuthStatus,
  } = useAuthStore();

  // 使用useCallback确保requireAuth函数的稳定性
  const requireAuth = useCallback(() => {
    
    // 不仅检查isLoggedIn，还检查user和token是否存在
    if (!isLoggedIn || !user || !token) {
      console.log('❌ 用户未登录或状态不完整，弹出登录框');
      openLoginDialog();
      return false;
    }
    console.log('✅ 用户已登录，允许继续');
    return true;
  }, [isLoggedIn, openLoginDialog, user, token]);

  // 使用useCallback确保getAuthHeaders函数的稳定性
  const getAuthHeaders = useCallback(() => {
    if (token) {
      return {
        'Authorization': `Bearer ${token}`,
      };
    }
    return {};
  }, [token]);

  return {
    // 状态
    user,
    token,
    isLoggedIn,
    isLoading,
    loginDialogOpen,
    
    // 用户信息
    username: user?.userName,
    nickname: user?.nickName,
    email: user?.email,
    avatar: user?.avatar,
    
    // 方法
    login,
    logout,
    setUser,
    setToken,
    setLoading,
    openLoginDialog,
    closeLoginDialog,
    checkAuthStatus,
    
    // 便捷方法
    requireAuth,
    
    // 获取认证头部，用于API请求
    getAuthHeaders,
  };
};

export default useAuth; 