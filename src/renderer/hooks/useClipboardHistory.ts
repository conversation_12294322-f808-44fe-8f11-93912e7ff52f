import { useState, useEffect, useCallback, useRef } from 'react';
import { ClipboardItem } from '../../shared/types/clipboard';
import { handleClipboardItemSelect } from '../utils/clipboard';
import { clipboardManagerClient } from '../services/api/clipboard-manager';
import { useAuthStore } from '../stores/authStore';

export interface UseClipboardHistoryOptions {
  searchQuery?: string;
  onItemSelect?: (item: ClipboardItem) => void;
  keyboardNavigation?: 'horizontal' | 'vertical';
  autoSelectFirst?: boolean;
}

export interface UseClipboardHistoryReturn {
  // 数据
  history: ClipboardItem[];
  filteredData: ClipboardItem[];
  isLoading: boolean;
  hasMoreData: boolean;
  totalCount: number;

  // 会员状态
  isVip: boolean;
  hasReachedFreeLimit: boolean;
  freeItemsUsed: number;
  maxFreeItems: number;

  // 选中状态
  selectedItemId: string | null;
  selectedIndex: number;
  selectedItem: ClipboardItem | null;

  // 操作方法
  handleItemClick: (item: ClipboardItem) => void;
  handleItemDoubleClick: (item: ClipboardItem) => Promise<void>;
  setSelectedIndex: (index: number) => void;
  loadMore: () => Promise<void>;
  refresh: () => Promise<void>;
  resetSelection: () => void; // 新增重置选中项的方法

  // 滚动检测
  onScroll: (element: HTMLElement) => void;

  // 键盘导航绑定
  keyboardProps: {
    onKeyDown: (e: KeyboardEvent) => void;
  };
}

// 常量配置
const PAGE_SIZE = 20; // 每页加载20条
const DEFAULT_MAX_FREE_ITEMS = 100; // 非会员默认最多100条
const SCROLL_THRESHOLD = 0.95; // 滚动到95%时加载下一页

export const useClipboardHistory = (options: UseClipboardHistoryOptions = {}): UseClipboardHistoryReturn => {
  const {
    searchQuery = '',
    onItemSelect,
    keyboardNavigation = 'vertical',
    autoSelectFirst = true
  } = options;

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [history, setHistory] = useState<ClipboardItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasMoreData, setHasMoreData] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const [lastSearchQuery, setLastSearchQuery] = useState('');

  // 最大免费条数限制（从设置中获取）
  const [maxFreeItems, setMaxFreeItems] = useState(DEFAULT_MAX_FREE_ITEMS);

  // 选中状态
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);
  const [selectedIndex, setSelectedIndex] = useState(0);

  // 加载标志，防止重复加载
  const loadingRef = useRef(false);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 获取当前选中的项目
  const selectedItem = history[selectedIndex] || null;

  // 获取最大历史记录数设置
  useEffect(() => {
    const loadMaxHistoryCount = async () => {
      try {
        const settings = await window.electron.settings.get();
        const maxHistoryCount = settings?.clipboard?.maxHistoryCount || DEFAULT_MAX_FREE_ITEMS;
        setMaxFreeItems(maxHistoryCount);
      } catch (error) {
        console.error('获取设置失败:', error);
        setMaxFreeItems(DEFAULT_MAX_FREE_ITEMS);
      }
    };

    loadMaxHistoryCount();

    // 初始化时输出当前用户状态
    console.log('useClipboardHistory 初始化，当前用户状态:', {
      user,
      vip_level: user?.vip_level,
      isVip: isVipUser()
    });
  }, []);

  // 监听剪贴板设置变化
  useEffect(() => {
    const handleClipboardSettingsChange = (clipboardSettings: any) => {
      console.log('收到剪贴板设置变化事件:', clipboardSettings);
      const newMaxFreeItems = clipboardSettings?.maxHistoryCount || DEFAULT_MAX_FREE_ITEMS;

      // 如果限制发生了变化
      if (newMaxFreeItems !== maxFreeItems) {
        setMaxFreeItems(newMaxFreeItems);

        // 重置状态并重新加载数据
        setHasMoreData(true);
        setCurrentPage(1);

        // 延迟一点时间确保状态更新完成
        setTimeout(() => {
          loadData(1, searchQuery, true);
        }, 50);
      }
    };

    // 监听来自主进程的剪贴板设置变化事件
    const removeListener = window.electron.system.onClipboardSettingsChanged(handleClipboardSettingsChange);

    return () => {
      removeListener();
    };
  }, [maxFreeItems, searchQuery]);

  // 监听窗口焦点变化，确保获取最新的用户状态
  useEffect(() => {
    const handleFocus = () => {
      console.log('窗口获得焦点，检查用户状态变化');
      // 强制重新加载数据以反映最新的用户状态
      setTimeout(() => {
        loadData(1, searchQuery, true);
      }, 100);
    };

    window.addEventListener('focus', handleFocus);

    return () => {
      window.removeEventListener('focus', handleFocus);
    };
  }, [searchQuery]);

  // 获取用户信息
  const { user, isLoggedIn } = useAuthStore();

  // 检查是否为会员用户
  const isVipUser = useCallback(() => {
    // 必须先登录，且 vip_level 不为 0 时，才是会员
    if (!isLoggedIn || !user) {
      return false;
    }
    return user.vip_level !== undefined && user.vip_level !== 0;
  }, [user?.vip_level, isLoggedIn]);

  // 监听来自主进程的用户状态变化事件
  useEffect(() => {
    const handleUserStatusChange = (authState: any) => {
      console.log('收到用户状态变化事件:', authState);

      // 更新本地 authStore 状态，使用标志避免循环广播
      if (authState && authState.user) {
        // 设置跳过广播标志，然后更新完整的认证状态
        useAuthStore.setState({
          _skipBroadcast: true,
          user: authState.user,
          token: authState.token,
          isLoggedIn: true
        });
      } else {
        // 设置跳过广播标志，然后调用 logout
        useAuthStore.setState({ _skipBroadcast: true });
        useAuthStore.getState().logout();
      }

      // 重新加载数据以反映新的VIP状态
      setHasMoreData(true);
      setTimeout(() => {
        loadData(1, searchQuery, true);
      }, 100);
    };

    // 监听来自主进程的用户状态变化事件
    const removeListener = window.electron.system.onUserStatusChanged(handleUserStatusChange);

    return () => {
      removeListener();
    };
  }, [searchQuery]);

  // 监听本地用户VIP状态变化
  useEffect(() => {
    console.log('本地用户VIP状态变化:', {
      user,
      vip_level: user?.vip_level,
      isVip: isVipUser(),
      historyLength: history.length,
      maxFreeItems
    });

    // 当用户状态发生变化时，重新加载数据
    if (user) {
      console.log('检测到本地用户状态变化，重新加载数据');
      setHasMoreData(true);
      // 重新加载数据
      setTimeout(() => {
        loadData(1, searchQuery, true);
      }, 100);
    }
  }, [user?.vip_level, user?.id, searchQuery]); // 简化依赖项，避免循环依赖

  // 检查是否达到非会员限制
  const hasReachedFreeLimit = useCallback(() => {
    if (isVipUser()) return false; // 会员无限制
    return history.length >= maxFreeItems;
  }, [isVipUser, history.length, maxFreeItems]);

  // 加载数据
  const loadData = useCallback(async (page: number, search: string = '', isRefresh: boolean = false) => {
    if (loadingRef.current) return;
    
    try {
      loadingRef.current = true;
      setIsLoading(true);

      // 检查非会员用户限制
      const maxItems = isVipUser() ? undefined : maxFreeItems;
      const limit = PAGE_SIZE;
      const offset = (page - 1) * PAGE_SIZE;

      // 如果非会员用户已经达到限制，不再加载
      if (maxItems && offset >= maxItems) {
        setHasMoreData(false);
        return;
      }

             // 调用API获取分页数据
       const response = await clipboardManagerClient.getHistoryPaginated({
         page,
         limit,
         search: search.trim() || undefined,
         maxItems
       });

      const { items, total, hasMore } = response;

      let newHistoryLength = 0;
      if (isRefresh || page === 1) {
        // 刷新或首次加载，替换数据
        setHistory(items);
        setCurrentPage(1);
        newHistoryLength = items.length;
      } else {
        // 加载更多，追加数据
        setHistory(prev => {
          const newHistory = [...prev, ...items];
          newHistoryLength = newHistory.length;
          return newHistory;
        });
      }

      setTotalCount(total);
      setHasMoreData(hasMore && (!maxItems || newHistoryLength < maxItems));
      
      if (page > 1) {
        setCurrentPage(page);
      }

    } catch (error) {
      console.error('加载剪贴板历史失败:', error);
    } finally {
      setIsLoading(false);
      loadingRef.current = false;
    }
  }, [history.length, isVipUser, maxFreeItems]);

  // 搜索变化处理
  useEffect(() => {
    // 清除之前的搜索定时器
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // 如果搜索内容变化，延迟执行搜索
    if (searchQuery !== lastSearchQuery) {
      searchTimeoutRef.current = setTimeout(() => {
        setLastSearchQuery(searchQuery);
        loadData(1, searchQuery, true);
      }, 300); // 300ms防抖
    }

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [searchQuery, lastSearchQuery, loadData]);

  // 初始加载
  useEffect(() => {
    loadData(1, searchQuery, true);
  }, []); // 空依赖，只在组件挂载时执行

  // 监听剪贴板变化事件
  useEffect(() => {
    const unsubscribe = window.electron.clipboard.onChange(() => {
      // 剪贴板变化时，刷新第一页数据
      loadData(1, searchQuery, true);
    });

    return unsubscribe;
  }, [searchQuery, loadData]);

      // 加载下一页
    const loadMore = useCallback(async () => {
      if (isLoading || !hasMoreData) return;
      
      // 检查非会员限制
      if (hasReachedFreeLimit()) {
        setHasMoreData(false);
        return;
      }
      
      setIsLoading(true);
      
      try {
        const result = await clipboardManagerClient.getHistoryPaginated({
          page: currentPage + 1,
          limit: PAGE_SIZE,
          search: searchQuery.trim() || undefined,
          maxItems: isVipUser() ? undefined : maxFreeItems,
        });
        
        const { items, total, page, hasMore } = result;
        
        if (items.length > 0) {
          setCurrentPage(page);
          
          let finalItems = items;
          let finalHasMore = hasMore;
          
          if (page === 1) {
            // 对于第一页，如果是非会员且返回数据超过限制，只保留前maxFreeItems条
            if (!isVipUser() && finalItems.length > maxFreeItems) {
              finalItems = finalItems.slice(0, maxFreeItems);
              finalHasMore = false;
            }
            setHistory(finalItems);
          } else {
            // 对于追加数据，检查是否会超过非会员限制
            if (!isVipUser()) {
              const remainingSlots = maxFreeItems - history.length;
              if (remainingSlots <= 0) {
                finalHasMore = false;
                finalItems = [];
              } else if (finalItems.length > remainingSlots) {
                finalItems = finalItems.slice(0, remainingSlots);
                finalHasMore = false;
              }
            }
            
            if (finalItems.length > 0) {
              setHistory(prev => [...prev, ...finalItems]);
            }
          }
          
          // 更新hasMoreData状态
          setHasMoreData(finalHasMore);
          setTotalCount(total);
        }
      } catch (error) {
        console.error('加载剪贴板历史失败:', error);
      } finally {
        setIsLoading(false);
      }
    }, [currentPage, isLoading, hasMoreData, searchQuery, isVipUser, history.length, totalCount, maxFreeItems]);

  // 刷新数据
  const refresh = useCallback(async () => {
    await loadData(1, searchQuery, true);
  }, [searchQuery, loadData]);

  // 重置选中项到第一个
  const resetSelection = useCallback(() => {
    setSelectedIndex(0);
    if (history.length > 0) {
      setSelectedItemId(history[0].id);
    } else {
      setSelectedItemId(null);
    }
  }, [history]);

  // 滚动检测
  const onScroll = useCallback((element: HTMLElement) => {
    const { scrollTop, scrollHeight, clientHeight } = element;
    const scrollPercentage = (scrollTop + clientHeight) / scrollHeight;
    
    // 当滚动到阈值时，自动加载下一页
    if (scrollPercentage >= SCROLL_THRESHOLD && hasMoreData && !isLoading) {
      loadMore();
    }
  }, [hasMoreData, isLoading, loadMore]);

  // 处理项目点击
  const handleItemClick = useCallback((item: ClipboardItem) => {
    const idx = history.findIndex(i => i.id === item.id);
    if (idx !== -1) {
      setSelectedIndex(idx);
      setSelectedItemId(item.id);
    }
  }, [history]);

  // 处理项目双击
  const handleItemDoubleClick = useCallback(async (item: ClipboardItem) => {
    try {
      await handleClipboardItemSelect(item);
      
      // 重置选中状态到第一项
      if (autoSelectFirst && history.length > 0) {
        setSelectedIndex(0);
        setSelectedItemId(history[0].id);
      }
      
      // 调用回调
      if (onItemSelect) {
        onItemSelect(item);
      }
    } catch (error) {
      console.error('选择剪贴板项目失败:', error);
    }
  }, [history, autoSelectFirst, onItemSelect]);

  // 键盘导航处理
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    const target = e.target as HTMLElement;
    const isInputElement = target && (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable);

    // 对于垂直导航（居中窗口），即使在输入框中也要响应上下键
    // 对于水平导航（底部窗口），在输入框中不响应任何导航键（由页面组件处理）
    if (isInputElement) {
      if (keyboardNavigation === 'horizontal') {
        // 水平布局：完全不处理，让页面组件处理
        return;
      } else {
        // 垂直布局：只处理上下键，其他键不处理
        if (e.key !== 'ArrowUp' && e.key !== 'ArrowDown' && e.key !== 'Enter') {
          return;
        }
      }
    }

    if (e.key === 'Enter') {
      const item = history[selectedIndex];
      if (item) {
        e.preventDefault();
        handleItemDoubleClick(item);
        return;
      }
    }

    // 根据导航方向处理方向键
    if (keyboardNavigation === 'horizontal') {
      if (e.key === 'ArrowLeft') {
        e.preventDefault();
        setSelectedIndex(prev => (prev > 0 ? prev - 1 : prev));
      } else if (e.key === 'ArrowRight') {
        e.preventDefault();
        const newIndex = selectedIndex + 1;
        if (newIndex < history.length) {
          setSelectedIndex(newIndex);
        } else if (hasMoreData && !isLoading) {
          // 如果已经到达最后一项且还有更多数据，加载下一页
          loadMore();
        }
      }
    } else {
      if (e.key === 'ArrowUp') {
        e.preventDefault();
        setSelectedIndex(prev => (prev > 0 ? prev - 1 : prev));
      } else if (e.key === 'ArrowDown') {
        e.preventDefault();
        const newIndex = selectedIndex + 1;
        if (newIndex < history.length) {
          setSelectedIndex(newIndex);
        } else if (hasMoreData && !isLoading) {
          // 如果已经到达最后一项且还有更多数据，加载下一页
          loadMore();
        }
      }
    }

    // ESC键处理
    if (e.key === 'Escape') {
      e.preventDefault();
      if (window.electron?.clipboardHistory?.hide) {
        window.electron.clipboardHistory.hide();
      }
    }
  }, [history, selectedIndex, keyboardNavigation, handleItemDoubleClick, hasMoreData, isLoading, loadMore]);

  // 当数据变化时，自动选中第一项
  useEffect(() => {
    if (autoSelectFirst && history.length > 0) {
      const newIndex = selectedIndex < history.length ? selectedIndex : 0;
      setSelectedIndex(newIndex);
      setSelectedItemId(history[newIndex]?.id || null);
    } else {
      setSelectedIndex(0);
      setSelectedItemId(null);
    }
  }, [history, autoSelectFirst, selectedIndex]);

  // 根据 selectedIndex 同步 selectedItemId
  useEffect(() => {
    const item = history[selectedIndex];
    if (item) {
      setSelectedItemId(item.id);
    }
  }, [selectedIndex, history]);

  // 键盘事件监听
  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);

  return {
    // 数据
    history,
    filteredData: history, // 现在filteredData就是history，因为过滤在服务端进行
    isLoading,
    hasMoreData,
    totalCount,
    
    // 会员状态
    isVip: isVipUser(),
    hasReachedFreeLimit: hasReachedFreeLimit(),
    freeItemsUsed: isVipUser() ? 0 : Math.min(history.length, maxFreeItems),
    maxFreeItems: maxFreeItems,
    
    // 选中状态
    selectedItemId,
    selectedIndex,
    selectedItem,
    
    // 操作方法
    handleItemClick,
    handleItemDoubleClick,
    setSelectedIndex,
    loadMore,
    refresh,
    resetSelection,
    
    // 滚动检测
    onScroll,
    
    // 键盘导航绑定
    keyboardProps: {
      onKeyDown: handleKeyDown,
    },
  };
};