import { APP } from '../../shared/ipc';

/**
 * 应用图标更新监听器
 * 用于监听应用图标更新事件并更新UI
 */
class AppIconListener {
  private listeners: Map<string, Function[]> = new Map();

  constructor() {
    this.setupListener();
  }

  /**
   * 设置IPC监听器
   */
  private setupListener() {
    // 监听应用图标更新事件 - 通过 crossWindow API
    if (window.electron?.crossWindow) {
      window.electron.crossWindow.on(APP.ICON_UPDATED, (data: { appId: string, iconPath: string }) => {
        console.log('通过 crossWindow 收到应用图标更新:', data);
        this.notifyListeners(data.appId, data.iconPath);
      });
    }
  }

  /**
   * 添加图标更新监听器
   * @param appId 应用ID
   * @param callback 回调函数
   * @returns 取消监听的函数
   */
  public addListener(appId: string, callback: (iconPath: string) => void): () => void {
    if (!this.listeners.has(appId)) {
      this.listeners.set(appId, []);
    }
    
    const appListeners = this.listeners.get(appId)!;
    appListeners.push(callback);
    
    // 返回取消监听的函数
    return () => {
      const index = appListeners.indexOf(callback);
      if (index !== -1) {
        appListeners.splice(index, 1);
      }
    };
  }

  /**
   * 通知所有监听器
   * @param appId 应用ID
   * @param iconPath 图标路径
   */
  private notifyListeners(appId: string, iconPath: string) {
    const appListeners = this.listeners.get(appId);
    if (appListeners) {
      appListeners.forEach(callback => {
        try {
          callback(iconPath);
        } catch (error) {
          console.error('执行图标更新回调失败:', error);
        }
      });
    }
  }
}

// 导出单例实例
export const appIconListener = new AppIconListener();