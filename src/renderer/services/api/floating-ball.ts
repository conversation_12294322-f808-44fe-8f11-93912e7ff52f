/**
 * 浮动球客户端
 * 封装与浮动球相关的所有API调用
 */
class FloatingBallClient {
  /**
   * 拖动浮动球（旧方法，保留兼容性）
   * @param delta 拖动位移
   */
  drag(delta: { x: number, y: number }): void {
    window.electron.floatingBall.drag(delta);
  }

  /**
   * 开始拖动浮动球
   * @param mousePosition 鼠标位置
   */
  dragStart(mousePosition: { x: number, y: number }): void {
    window.electron.floatingBall.dragStart(mousePosition);
  }

  /**
   * 拖动浮动球移动
   * @param mousePosition 鼠标位置
   */
  dragMove(mousePosition: { x: number, y: number }): void {
    window.electron.floatingBall.dragMove(mousePosition);
  }

  /**
   * 结束拖动浮动球
   */
  dragEnd(): void {
    window.electron.floatingBall.dragEnd();
  }

  /**
   * 点击浮动球
   */
  click(): void {
    console.log('浮动球客户端API: 发送点击事件');
    window.electron.floatingBall.click();
  }

  /**
   * 双击浮动球
   */
  doubleClick(): void {
    console.log('浮动球客户端API: 发送双击事件');
    window.electron.floatingBall.doubleClick();
  }

  /**
   * 长按浮动球
   */
  longPress(): void {
    console.log('浮动球客户端API: 发送长按事件');
    window.electron.floatingBall.longPress();
  }

  /**
   * 开始屏幕镜像
   */
  captureScreenStart(position: { x: number, y: number }): void {
    console.log('浮动球客户端API: 开始屏幕镜像');
    // 如果API还不支持，则提供空实现
    if (window.electron.floatingBall.captureScreenStart) {
      window.electron.floatingBall.captureScreenStart(position);
    }
  }

  /**
   * 结束屏幕镜像
   */
  captureScreenEnd(imageBase64: string): void {
    console.log('浮动球客户端API: 结束屏幕镜像');
    // 如果API还不支持，则提供空实现
    if (window.electron.floatingBall.captureScreenEnd) {
      window.electron.floatingBall.captureScreenEnd(imageBase64);
    }
  }

  /**
   * 取消屏幕镜像
   */
  cancelCapture(): void {
    console.log('浮动球客户端API: 取消屏幕镜像');
    // 如果API还不支持，则提供空实现
    if (window.electron.floatingBall.cancelCapture) {
      window.electron.floatingBall.cancelCapture();
    }
  }

  /**
   * 开始语音输入
   */
  startVoiceInput(): void {
    console.log('浮动球客户端API: 开始语音输入');
    // 如果API还不支持，则提供空实现
    if (window.electron.floatingBall.startVoiceInput) {
      window.electron.floatingBall.startVoiceInput();
    }
  }

  /**
   * 结束语音输入
   */
  endVoiceInput(voiceBase64: ArrayBuffer): void {
    console.log('浮动球客户端API: 结束语音输入');
    // 如果API还不支持，则提供空实现
    if (window.electron.floatingBall.endVoiceInput) {
      window.electron.floatingBall.endVoiceInput(voiceBase64);
    }
  }

  /**
   * 设置鼠标事件穿透
   * @param ignore 是否启用穿透
   * @param region 可选的特定区域参数
   */
  setIgnoreMouseEvents(ignore: boolean, region?: { x: number, y: number, width: number, height: number }): void {
    console.log('浮动球客户端API: 设置鼠标事件穿透', { ignore, region });
    // 如果API还不支持，则提供空实现
    if (window.electron.floatingBall.setIgnoreMouseEvents) {
      window.electron.floatingBall.setIgnoreMouseEvents(ignore, region);
    }
  }

  /**
   * 添加拖动状态变化监听
   * @param callback 回调函数
   * @returns 卸载函数
   */
  onDraggingChange(callback: (isDragging: boolean) => void): () => void {
    return window.electron.floatingBall.onDraggingChange(callback);
  }

  /**
   * 添加吸附侧边变化监听
   * @param callback 回调函数
   * @returns 卸载函数
   */
  onSnapSideChange(callback: (side: 'left' | 'right') => void): () => void {
    // 如果API还不支持，则提供无操作的卸载函数
    if (window.electron.floatingBall.onSnapSideChange) {
      return window.electron.floatingBall.onSnapSideChange(callback);
    }
    return () => { /* 空函数，无需操作 */ }; // 返回空函数
  }

  /**
   * 监听屏幕镜像状态变化
   */
  onScreenCaptureChange(callback: (isCapturing: boolean) => void): () => void {
    // 如果API还不支持，则提供无操作的卸载函数
    if (window.electron.floatingBall.onScreenCaptureChange) {
      return window.electron.floatingBall.onScreenCaptureChange(callback);
    }
    return () => { /* 空函数，无需操作 */ }; // 返回空函数
  }

  /**
   * 监听语音输入状态变化
   */
  onVoiceRecordingChange(callback: (isRecording: boolean, volume?: number) => void): () => void {
    // 如果API还不支持，则提供无操作的卸载函数
    if (window.electron.floatingBall.onVoiceRecordingChange) {
      return window.electron.floatingBall.onVoiceRecordingChange(callback);
    }
    return () => { /* 空函数，无需操作 */ }; // 返回空函数
  }

  /**
   * 获取所有显示器信息
   */
  async getDisplays(): Promise<any[]> {
    console.log('浮动球客户端API: 获取显示器信息');
    if (window.electron.floatingBall.getDisplays) {
      return await window.electron.floatingBall.getDisplays();
    }
    return [];
  }

  /**
   * 启动多屏截图
   */
  startMultiScreenCapture(): void {
    console.log('浮动球客户端API: 启动多屏截图');
    if (window.electron.floatingBall.startMultiScreenCapture) {
      window.electron.floatingBall.startMultiScreenCapture();
    }
  }

  /**
   * 选择特定显示器进行截图
   */
  selectDisplayCapture(displayId: string): void {
    console.log('浮动球客户端API: 选择显示器截图', displayId);
    if (window.electron.floatingBall.selectDisplayCapture) {
      window.electron.floatingBall.selectDisplayCapture(displayId);
    }
  }

  /**
   * 通知全局拖拽开始
   */
  onGlobalDragStart(): void {
    console.log('浮动球客户端API: 全局拖拽开始');
    if (window.electron.floatingBall.onGlobalDragStart) {
      window.electron.floatingBall.onGlobalDragStart();
    }
  }

  /**
   * 通知全局拖拽结束
   */
  onGlobalDragEnd(): void {
    console.log('浮动球客户端API: 全局拖拽结束');
    if (window.electron.floatingBall.onGlobalDragEnd) {
      window.electron.floatingBall.onGlobalDragEnd();
    }
  }

  /**
   * 监听全局拖拽状态变化
   */
  onGlobalDragStateChange(callback: (isActive: boolean) => void): () => void {
    console.log('浮动球客户端API: 监听全局拖拽状态变化');
    if (window.electron.floatingBall.onGlobalDragStateChange) {
      return window.electron.floatingBall.onGlobalDragStateChange(callback);
    }
    return () => { /* 空函数，无需操作 */ };
  }

  /**
   * 发送文件信息
   */
  showFileInfo(fileInfos: Array<{
    name: string;
    path: string | null;
    size: number;
    type: string;
    lastModified: number;
  }>): void {
    if (window.electron.floatingBall.showFileInfo) {
      window.electron.floatingBall.showFileInfo(fileInfos);
    }
  }
}

// 导出单例实例
export const floatingBallClient = new FloatingBallClient(); 