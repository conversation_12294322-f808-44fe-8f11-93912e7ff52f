import { FileInfo } from '../../../shared/types/file';

/**
 * 文件管理客户端
 * 封装与文件相关的所有API调用
 */
class FileManagerClient {
  /**
   * 获取最近文件
   * @returns 最近访问的文件列表
   */
  async getRecentFiles(): Promise<FileInfo[]> {
    try {
      return await window.electron.files.getRecent();
    } catch (error) {
      console.error('获取最近文件失败:', error);
      return [];
    }
  }

  /**
   * 搜索文件
   * @param query 搜索关键词
   * @returns 搜索结果
   */
  async searchFiles(query: string): Promise<FileInfo[]> {
    try {
      return await window.electron.files.search(query);
    } catch (error) {
      console.error('搜索文件失败:', error);
      return [];
    }
  }

  /**
   * 打开文件
   * @param path 文件路径
   * @returns 是否成功打开
   */
  async openFile(path: string): Promise<boolean> {
    try {
      return await window.electron.files.open(path);
    } catch (error) {
      console.error('打开文件失败:', error);
      return false;
    }
  }

  /**
   * 打开文件所在位置
   * @param path 文件路径
   * @returns 是否成功打开
   */
  async openFileLocation(path: string): Promise<boolean> {
    try {
      return await window.electron.files.openLocation(path);
    } catch (error) {
      console.error('打开文件位置失败:', error);
      return false;
    }
  }

  /**
   * 获取文件预览内容
   * @param path 文件路径
   * @returns 文件预览内容
   */
  async readFilePreview(path: string): Promise<string> {
    try {
      return await window.electron.files.readFilePreview(path);
    } catch (error) {
      console.error('获取文件预览失败:', error);
      return '';
    }
  }

  /**
   * 选择文件夹
   * @returns 选中的文件夹路径，如果取消则返回 null
   */
  async selectFolder(): Promise<string | null> {
    try {
      return await window.electron.files.selectFolder();
    } catch (error) {
      console.error('选择文件夹失败:', error);
      return null;
    }
  }
}

// 导出单例实例
export const fileManagerClient = new FileManagerClient(); 