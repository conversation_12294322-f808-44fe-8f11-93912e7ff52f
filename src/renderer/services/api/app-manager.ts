import { AppInfo, ResultItem } from '../../types/app';

/**
 * 应用管理客户端
 * 封装与应用相关的所有API调用
 */
class AppManagerClient {
  /**
   * 启动应用
   * @param appId 应用ID
   * @returns 是否成功启动
   */
  async launchApp(appPath: string): Promise<boolean> {
    try {
      return await window.electron.apps.launch(appPath);
    } catch (error) {
      console.error('启动应用失败:', error);
      return false;
    }
  }

  /**
   * 移动窗口
   * @param mouseData 鼠标数据
   */
  moveWindow(mouseData: { mouseX: number, mouseY: number, width: number, height: number }) {
    window.electron.window.moveWindow(mouseData);
  }

  // 获取应用列表
  async getAppList() {
    return await window.electron.apps.getAppList();
  }
}

// 导出单例实例
export const appManagerClient = new AppManagerClient(); 