import { HotkeyInfo } from '../../../shared/ipc';

/**
 * 窗口管理客户端
 * 封装与窗口相关的所有API调用
 */
class WindowManagerClient {
  /**
   * 隐藏窗口
   */
  hideWindow(): void {
    window.electron.window.hideWindow();
  }

  /**
   * 显示窗口
   */
  showWindow(): void {
    window.electron.window.showWindow();
  }

  /**
   * 切换窗口显示状态
   */
  toggleWindow(): void {
    window.electron.window.toggleWindow();
  }

  /**
   * 打开开发者工具
   */
  openDevTools(): void {
    window.electron.window.openDevTools();
  }

  /**
   * 添加窗口显示事件监听
   * @param callback 回调函数
   * @returns 卸载函数
   */
  onWindowShow(callback: () => void): () => void {
    return window.electron.window.onWindowShow(callback);
  }

  /**
   * 设置窗口钉住状态
   * @param pinned 是否钉住
   */
  setPinned(pinned: boolean): void {
    window.electron.window.setPinned(pinned);
  }

  /**
   * 获取窗口钉住状态
   * @returns 是否钉住
   */
  async isPinned(): Promise<boolean> {
    return await window.electron.window.isPinned();
  }
}

// 导出单例实例
export const windowManagerClient = new WindowManagerClient(); 