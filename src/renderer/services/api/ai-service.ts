import axios from 'axios';

interface AiServiceConfig {
  apiKey: string;
  model: string;
  maxTokens: number;
  temperature: number;
}

/**
 * AI服务类
 * 封装与OpenAI通信的所有方法
 */
class AiService {
  private config: AiServiceConfig;
  // 对话历史消息队列
  private systemPrompt: string;
  private messages: { role: 'system' | 'user' | 'assistant'; content: string }[];
  
  constructor() {
    // 默认配置，实际项目中应从环境变量或配置文件加载
    this.config = {
      apiKey: 'sk-NJC0MqLNorBCTjj9SldFAFyszaL9kW27glewjYJdj3yLA80z',
      model: 'deepseek-r1-250528',
      maxTokens: 4096,
      temperature: 0.7
    };
    // 系统提示
    this.systemPrompt = '你是Aido - 一个高效AI助手，擅长调用各种工具帮助用户解决问题或回答疑问。请保持回答简洁、实用，并尽可能提供准确信息。';
    // 初始化对话历史，仅包含系统提示
    this.messages = [
      { role: 'system', content: this.systemPrompt }
    ];
  }
  
  /**
   * 发送聊天请求到OpenAI
   * @param prompt 用户输入的问题
   * @returns AI响应文本
   */
  async chat(prompt: string): Promise<string> {
    try {
      if (!this.config.apiKey) {
        throw new Error('OpenAI API密钥未配置');
      }
      
      // 添加用户消息到历史
      this.messages.push({ role: 'user', content: prompt });
      const response = await axios.post(
        'https://yunwu.ai/v1/chat/completions',
        {
          model: this.config.model,
          messages: this.messages,
          max_tokens: this.config.maxTokens,
          temperature: this.config.temperature
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.config.apiKey}`
          }
        }
      );
      
      const aiContent = response.data.choices[0].message.content;
      // 添加 AI 响应到历史
      this.messages.push({ role: 'assistant', content: aiContent });
      return aiContent;
    } catch (error) {
      console.error('OpenAI API调用失败:', error);
      throw error;
    }
  }
  
  /**
   * 设置API配置
   * @param newConfig 新的配置参数
   */
  setConfig(newConfig: Partial<AiServiceConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
  
  /**
   * 获取当前配置
   * @returns 当前配置的副本
   */
  getConfig(): AiServiceConfig {
    return { ...this.config };
  }
  
  /**
   * 重置对话，仅保留系统提示
   */
  resetConversation(): void {
    this.messages = [
      { role: 'system', content: this.systemPrompt }
    ];
  }
  
  /**
   * 流式聊天：支持边接收边回调
   * @param prompt 用户输入
   * @param onChunk 接收到的内容片段
   */
  async streamChat(
    prompt: string,
    onChunk: (chunk: string) => void
  ): Promise<void> {
    // 添加用户消息到历史
    this.messages.push({ role: 'user', content: prompt });
    
    // 发起支持流式的请求
    const response = await fetch('https://yunwu.ai/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`
      },
      body: JSON.stringify({
        model: this.config.model,
        messages: this.messages,
        max_tokens: this.config.maxTokens,
        temperature: this.config.temperature,
        stream: true
      })
    });
    if (!response.body) throw new Error('No response body');
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let done = false;
    let buffer = '';
    let fullResponse = ''; // 添加变量来累积完整响应
    
    while (!done) {
      const { value, done: doneReading } = await reader.read();
      done = doneReading;
      buffer += decoder.decode(value || new Uint8Array(), { stream: !doneReading });
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';
      for (const line of lines) {
        if (!line.startsWith('data: ')) continue;
        const dataStr = line.replace(/^data: /, '').trim();
        if (dataStr === '[DONE]') {
          // 标记流已经结束，但不立即返回
          done = true;
          break; // 只跳出当前循环
        }
        try {
          const parsed = JSON.parse(dataStr);
          const delta = parsed.choices?.[0]?.delta?.content;
          if (delta) {
            // 回调增量内容
            onChunk(delta);
            // 累积响应内容
            fullResponse += delta;
          }
        } catch (e) {
          console.error('解析流式数据失败:', e);
        }
      }
    }
    
    // 在流结束后，将完整的响应添加到 history
    if (fullResponse) {
      this.messages.push({ role: 'assistant', content: fullResponse });
    }
  }
}

// 导出单例实例
export const aiService = new AiService(); 