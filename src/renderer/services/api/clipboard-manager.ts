/**
 * 获取用户VIP状态的辅助函数
 */
const getVipStatus = (): boolean => {
  try {
    // 优先通过全局变量访问最新状态
    const authStoreState = (window as any).__authStore__;
    if (authStoreState) {
      // 如果用户未登录或者没有vip_level字段，则为非会员
      if (!authStoreState.user || !authStoreState.isLoggedIn) {
        console.log('从全局状态获取VIP状态: 用户未登录，非会员');
        return false;
      }

      const isVip = authStoreState.user.vip_level !== undefined && authStoreState.user.vip_level !== 0;
      console.log('从全局状态获取VIP状态:', {
        user: authStoreState.user,
        isLoggedIn: authStoreState.isLoggedIn,
        vip_level: authStoreState.user.vip_level,
        isVip
      });
      return isVip;
    }

    // 备用方案：直接调用 useAuthStore
    const useAuthStore = (window as any).useAuthStore;
    if (useAuthStore) {
      const state = useAuthStore.getState();

      // 如果用户未登录，则为非会员
      if (!state.user || !state.isLoggedIn) {
        console.log('从store获取VIP状态: 用户未登录，非会员');
        return false;
      }

      const isVip = state.user.vip_level !== undefined && state.user.vip_level !== 0;
      console.log('从store获取VIP状态:', {
        user: state.user,
        isLoggedIn: state.isLoggedIn,
        vip_level: state.user.vip_level,
        isVip
      });
      return isVip;
    }

    console.log('无法获取认证状态，默认为非会员');
    return false;
  } catch (error) {
    console.warn('获取VIP状态失败:', error);
    return false;
  }
};

/**
 * 剪贴板管理客户端
 * 封装与剪贴板相关的所有API调用
 */
class ClipboardManagerClient {
  private isInitialized = false;
  private unsubscribeCallback: (() => void) | null = null;

  /**
   * 初始化剪贴板历史记录服务
   */
  initialize(): void {
    if (this.isInitialized) {
      return;
    }

    try {
      this.setupClipboardListener();
      this.isInitialized = true;
    } catch (error) {
      console.error('初始化剪贴板历史记录服务失败:', error);
    }
  }

  /**
   * 设置剪贴板变化监听
   */
  private setupClipboardListener(): void {
    // 监听剪贴板变化事件
    this.unsubscribeCallback = window.electron.clipboard.onChange(() => {
      // 剪贴板变化事件，由hook自行处理刷新逻辑
    });
  }

  /**
   * 获取分页的剪贴板历史记录
   */
  async getHistoryPaginated(options: {
    page?: number;
    limit?: number;
    search?: string;
    type?: string;
    favorite?: boolean;
    maxItems?: number;
  } = {}): Promise<{
    items: any[];
    total: number;
    page: number;
    limit: number;
    hasMore: boolean;
  }> {
    try {
      // 检查会员限制
      const isVip = getVipStatus(); // 从 authStore 获取真实的VIP状态
      const maxFreeItems = options.maxItems || 100; // 使用传入的maxItems或默认100
      const currentPage = options.page || 1;
      const pageSize = options.limit || 20;
      const requestedOffset = (currentPage - 1) * pageSize;

      // 先获取真实数据
      const response = await window.electron.clipboardHistory.getHistoryPaginated(options);

      // 如果是非会员用户，需要应用限制
      if (!isVip && maxFreeItems > 0) {
        // 如果请求的偏移量已经超过了限制，返回空数据
        if (requestedOffset >= maxFreeItems) {
          return {
            items: [],
            total: response.total, // 使用真实的总数
            page: currentPage,
            limit: pageSize,
            hasMore: false
          };
        }

        // 如果当前页的数据会超过限制，需要截断
        const remainingSlots = maxFreeItems - requestedOffset;
        let finalItems = response.items;
        let finalHasMore = response.page < response.totalPages;

        if (finalItems.length > remainingSlots) {
          finalItems = finalItems.slice(0, remainingSlots);
          finalHasMore = false;
        } else if (requestedOffset + finalItems.length >= maxFreeItems) {
          finalHasMore = false;
        }

        return {
          ...response,
          items: finalItems,
          hasMore: finalHasMore
        };
      }

      // 会员用户或无限制，返回原始数据
      return {
        ...response,
        hasMore: response.page < response.totalPages
      };
    } catch (error) {
      console.error('获取分页剪贴板历史失败:', error);
      return {
        items: [],
        total: 0,
        page: options.page || 1,
        limit: options.limit || 20,
        hasMore: false
      };
    }
  }

  /**
   * 获取剪贴板历史记录（保持向后兼容）
   */
  async getHistory(): Promise<any[]> {
    try {
      // 获取设置中的最大历史记录数
      let maxHistoryCount = 100; // 默认值
      try {
        const settings = await window.electron.settings.get();
        maxHistoryCount = settings?.clipboard?.maxHistoryCount || 100;
      } catch (settingsError) {
        console.warn('获取设置失败，使用默认值:', settingsError);
      }

      const response = await this.getHistoryPaginated({ page: 1, limit: maxHistoryCount });
      return response.items;
    } catch (error) {
      console.error('获取剪贴板历史记录失败:', error);
      return [];
    }
  }

  /**
   * 选择剪贴板项目
   */
  async selectItem(item: any): Promise<void> {
    try {
      await window.electron.clipboardHistory.selectItem(item);
    } catch (error) {
      console.error('选择剪贴板项目失败:', error);
      throw error;
    }
  }

  /**
   * 删除剪贴板项目
   */
  async deleteItem(id: string): Promise<boolean> {
    try {
      return await window.electron.clipboardHistory.deleteItem(id);
    } catch (error) {
      console.error('删除剪贴板项目失败:', error);
      return false;
    }
  }

  /**
   * 清空剪贴板历史
   */
  async clearHistory(): Promise<boolean> {
    try {
      return await window.electron.clipboardHistory.clearHistory();
    } catch (error) {
      console.error('清空剪贴板历史失败:', error);
      return false;
    }
  }

  /**
   * 设置项目收藏状态
   */
  async setItemFavorite(id: string, favorite: boolean): Promise<boolean> {
    try {
      return await window.electron.clipboardHistory.setFavorite(id, favorite);
    } catch (error) {
      console.error('设置收藏状态失败:', error);
      return false;
    }
  }

  /**
   * 更新项目备注
   */
  async updateItemMemo(id: string, memo: string): Promise<boolean> {
    try {
      return await window.electron.clipboardHistory.updateMemo(id, memo);
    } catch (error) {
      console.error('更新备注失败:', error);
      return false;
    }
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    if (this.unsubscribeCallback) {
      this.unsubscribeCallback();
      this.unsubscribeCallback = null;
    }
    this.isInitialized = false;
  }
}

// 导出单例实例
export const clipboardManagerClient = new ClipboardManagerClient(); 