import React, { useEffect, useState, useRef } from 'react';
import { floatingBallClient } from '../../services/api/floating-ball';
import ScreenShot from 'js-web-screen-shot';
import { changeLanguage } from '../../i18n';
import './ScreenCapture.css';

/**
 * 屏幕截图页面组件
 */
// 简单的防重复机制
let hasRequested = false;

const ScreenCapturePage: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isReady, setIsReady] = useState(false);
  const screenshotInstanceRef = useRef<any>(null);

  // 初始化页面样式
  useEffect(() => {
    // 设置页面背景为透明
    document.body.style.background = 'transparent';
    document.documentElement.style.background = 'transparent';

    // 优化页面性能
    document.body.style.overflow = 'hidden';
    document.documentElement.style.overflow = 'hidden';

    // 应用样式到容器
    if (containerRef.current) {
      containerRef.current.style.background = 'transparent';
      containerRef.current.style.pointerEvents = 'none';
    }
    // 标记组件已准备好
    setIsReady(true);
  }, []);

  // 截图完成回调
  const handleScreenshotComplete = ({base64}: {base64: string, cutInfo?: any}) => {
    hasRequested = false;
    if (floatingBallClient.captureScreenEnd) {
      floatingBallClient.captureScreenEnd(base64);
    }
  };

  // 截图取消回调
  const handleScreenshotClose = () => {
    hasRequested = false;
    if (floatingBallClient.cancelCapture) {
      floatingBallClient.cancelCapture();
    }
  };

  // 截图保存回调
  const handleScreenshotSave = () => {
    hasRequested = false;
    if (floatingBallClient.cancelCapture) {
      floatingBallClient.cancelCapture();
    }
  };

  // 监听语言变化事件
  useEffect(() => {
    const handleLanguageChange = (language: string) => {
      console.log(`屏幕截图窗口收到语言变化事件: ${language}`);
      changeLanguage(language);
    };

    // 监听来自主进程的语言变化事件
    const removeListener = window.electron.system.onLanguageChanged(handleLanguageChange);

    return () => {
      removeListener();
    };
  }, []);
  
  // 处理接收到的截图数据
  useEffect(() => {
    if (!isReady) return;

    hasRequested = false;

    // 处理屏幕捕获图像
    const handleScreenCaptureImage = (data: { imageData: string }) => {
      if (data.imageData.startsWith('data:image')) {
        createScreenshotInstance(data.imageData);
      }
    };

    // 监听屏幕捕获图像
    if (window.electron.floatingBall.onScreenCaptureImage) {
      const unsubscribe = window.electron.floatingBall.onScreenCaptureImage(handleScreenCaptureImage);

      // 立即请求截图
      requestNewScreenshot();

      return unsubscribe;
    }
  }, [isReady]);

  // 请求新的屏幕截图
  const requestNewScreenshot = () => {
    if (hasRequested || !floatingBallClient.captureScreenStart) return;

    hasRequested = true;
    floatingBallClient.captureScreenStart({ x: 0, y: 0 });
  };

  // 创建截图实例
  const createScreenshotInstance = (imageData: string) => {
    // 清理旧实例
    if (screenshotInstanceRef.current) {
      try {
        if (screenshotInstanceRef.current.destroyComponents) {
          screenshotInstanceRef.current.destroyComponents();
        }
        screenshotInstanceRef.current = null;
      } catch (error) {
        console.error('清理旧实例时出错:', error);
      }
    }

    // 优化页面样式
    document.body.style.overflow = 'hidden';
    document.documentElement.style.overflow = 'hidden';

    try {
      // 创建截图实例
      const instance = new ScreenShot({
        enableWebRtc: false,
        imgSrc: imageData,
        showScreenData: true,
        imgAutoFit: true,
        clickCutFullScreen: true,
        canvasWidth: window.innerWidth,
        canvasHeight: window.innerHeight,
        level: 9999,

        hiddenScrollBar: {
          state: true,
          fillState: false,
          color: '#000000'
        },
        wrcWindowMode: false,
        writeBase64: true,

        // 回调函数
        completeCallback: handleScreenshotComplete,
        closeCallback: handleScreenshotClose,
        saveCallback: handleScreenshotSave
      });

      screenshotInstanceRef.current = instance;

    } catch (error: any) {
      console.error('创建截图实例失败:', error);
    }
  };


  
  // 处理ESC键取消和组件清理
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && floatingBallClient.cancelCapture) {
        floatingBallClient.cancelCapture();
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);

      // 清理截图实例
      if (screenshotInstanceRef.current?.destroyComponents) {
        screenshotInstanceRef.current.destroyComponents();
        screenshotInstanceRef.current = null;
      }

      hasRequested = false;
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className="screen-capture-container"
      style={{
        background: 'transparent',
        pointerEvents: 'none',
        opacity: 0.01 // 几乎不可见但仍然存在
      }}
    />
  );
};

export default ScreenCapturePage; 