/* 截图容器 */
.screen-capture-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  overflow: visible !important; /* 修改为visible以确保内容不被裁剪 */
  display: flex;
  justify-content: center;
  align-items: center;
  /* background-color: rgba(0, 0, 0, 0.7); */
  background: transparent;
  width: 100% !important;
  height: 100% !important;
}

/* 截图信息面板 */
.screen-capture-info {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  max-width: 400px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  text-align: center;
}

.screen-capture-info h2 {
  margin-top: 0;
  color: #333;
  font-size: 20px;
}

.screen-capture-info p {
  margin: 10px 0;
}

.screen-capture-info ul {
  text-align: left;
}

/* 背景图像 */
.screen-capture-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  user-select: none;
  display: none; /* 隐藏背景图像，使用js-web-screen-shot自己的图像显示 */
}

/* 遮罩层 */
.screen-capture-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  /* background-color: rgba(0, 0, 0, 0.2); */
  background: transparent;
  z-index: 1;
}

/* 选择区域 */
.screen-capture-selection {
  position: absolute;
  z-index: 2;
  background-color: transparent;
  box-sizing: border-box;
  /* 选中区域内清除遮罩 */
  box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5);
}

/* 选择区域边框 */
.screen-capture-border {
  position: absolute;
  background-color: rgba(0, 153, 255, 0.7);
}

/* 上边框 */
.screen-capture-border.top {
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
}

/* 右边框 */
.screen-capture-border.right {
  top: 0;
  right: 0;
  bottom: 0;
  width: 2px;
}

/* 下边框 */
.screen-capture-border.bottom {
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
}

/* 左边框 */
.screen-capture-border.left {
  top: 0;
  left: 0;
  bottom: 0;
  width: 2px;
}

/* 控制点 */
.screen-capture-handle {
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: #fff;
  border: 1px solid rgba(0, 153, 255, 0.9);
  z-index: 3;
}

/* 左上控制点 */
.screen-capture-handle.tl {
  top: -4px;
  left: -4px;
  cursor: nw-resize;
}

/* 右上控制点 */
.screen-capture-handle.tr {
  top: -4px;
  right: -4px;
  cursor: ne-resize;
}

/* 左下控制点 */
.screen-capture-handle.bl {
  bottom: -4px;
  left: -4px;
  cursor: sw-resize;
}

/* 右下控制点 */
.screen-capture-handle.br {
  bottom: -4px;
  right: -4px;
  cursor: se-resize;
}

/* 尺寸信息 */
.screen-capture-size-info {
  position: absolute;
  top: -30px;
  left: 0;
  padding: 4px 8px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
}

/* 十字线 */
.screen-capture-crosshair-h,
.screen-capture-crosshair-v {
  position: absolute;
  background-color: rgba(255, 255, 255, 0.5);
  z-index: 2;
  pointer-events: none;
}

.screen-capture-crosshair-h {
  left: 0;
  right: 0;
  height: 1px;
}

.screen-capture-crosshair-v {
  top: 0;
  bottom: 0;
  width: 1px;
} 