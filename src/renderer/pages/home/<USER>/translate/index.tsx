import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '../../../../components/ui/button';
import { useToast } from '../../../../components/ui/use-toast';
import {
  Globe,
  ArrowLeftRight,
  // Upload,
  // Download,
  Copy,
  ChevronDown
} from 'lucide-react';
import { cn } from '../../../../utils';
import translateApi from '../../../../api/translate';

// 语言接口定义
interface Language {
  code: string;
  cname: string; // 中文名称
  ename: string; // 英文名称
}

// 下拉选择组件
interface LanguageSelectProps {
  value: string;
  onChange: (value: string) => void;
  languages: Language[];
  placeholder?: string;
  className?: string;
  currentLang?: string; // 当前软件使用的语言
}

const LanguageSelect: React.FC<LanguageSelectProps> = ({
  value,
  onChange,
  languages,
  placeholder = "选择语言",
  className,
  currentLang = 'zh' // 默认中文
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const selectedLanguage = languages.find(lang => lang.code === value);

  // 根据当前语言获取语言名称
  const getLanguageName = (language: Language) => {
    return currentLang === 'zh' ? language.cname : language.ename;
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div ref={dropdownRef} className={cn("relative", className)}>
      <Button
        variant="ghost"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full justify-between h-9 px-3 text-sm bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 hover:bg-blue-50/80 dark:hover:bg-blue-950/30 hover:border-blue-200 dark:hover:border-blue-800 shadow-sm hover:shadow-md transition-all duration-200 rounded-lg"
      >
        <div className="flex items-center gap-2 min-w-0">
          <Globe className="w-3.5 h-3.5 flex-shrink-0 text-blue-600 dark:text-blue-400" />
          <span className="truncate text-slate-700 dark:text-slate-300 font-medium">
            {selectedLanguage
              ? getLanguageName(selectedLanguage)
              : placeholder
            }
          </span>
        </div>
        <ChevronDown className={cn(
          "w-3.5 h-3.5 transition-transform flex-shrink-0 text-slate-500 dark:text-slate-400",
          isOpen && "rotate-180"
        )} />
      </Button>

      {isOpen && (
        <div className="absolute top-full left-0 right-0 z-50 mt-2 max-h-48 overflow-auto bg-white/95 dark:bg-slate-800/95 backdrop-blur-md border border-slate-200/50 dark:border-slate-700/50 rounded-xl shadow-xl">
          {languages.map((language) => (
            <button
              key={language.code}
              onClick={() => {
                onChange(language.code);
                setIsOpen(false);
              }}
              className={cn(
                "w-full px-4 py-2.5 text-left text-sm hover:bg-blue-50/80 dark:hover:bg-blue-950/30 transition-colors first:rounded-t-xl last:rounded-b-xl",
                value === language.code && "bg-blue-100/80 dark:bg-blue-950/50 text-blue-700 dark:text-blue-300"
              )}
            >
              <div className="truncate">
                <span className="font-medium text-slate-700 dark:text-slate-300">
                  {getLanguageName(language)}
                </span>
              </div>
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

// 主翻译组件
export const Translator: React.FC = () => {
  const { t, i18n } = useTranslation();
  const [inputText, setInputText] = useState('');
  const [outputText, setOutputText] = useState('');
  const [fromLanguage, setFromLanguage] = useState('auto');
  const [toLanguage, setToLanguage] = useState('zh');
  const [languages, setLanguages] = useState<Language[]>([]);
  const [isTranslating, setIsTranslating] = useState(false);
  // 记住原始语言设置，用于智能切换回复
  const [originalFromLanguage, setOriginalFromLanguage] = useState('auto');
  const [originalToLanguage, setOriginalToLanguage] = useState('zh');
  // 记录切换次数，用于判断是否应该恢复到原始状态
  const [switchCount, setSwitchCount] = useState(0);
  // const [uploadedFileName, setUploadedFileName] = useState('');

  const { toast } = useToast();
  // const fileInputRef = useRef<HTMLInputElement>(null);
  const translateTimeoutRef = useRef<NodeJS.Timeout>();
  const languagesLoadedRef = useRef(false); // 防止重复加载语言列表
  const inputTextareaRef = useRef<HTMLTextAreaElement>(null); // 输入框ref

  // 获取语言列表
  useEffect(() => {
    // 防止React StrictMode导致的重复调用
    if (languagesLoadedRef.current) {
      return;
    }

    const fetchLanguages = async () => {
      console.log('获取语言');
      languagesLoadedRef.current = true; // 标记为已加载

      try {
        const response = await translateApi.getLanguages();
        if (response.data && Array.isArray(response.data)) {
          // 直接使用API返回的语言列表（已包含自动检测选项）
          setLanguages(response.data);
        }
      } catch (error) {
        console.error('获取语言列表失败:', error);
        languagesLoadedRef.current = false; // 失败时重置标记，允许重试
        toast({
          title: t('translate.getLanguagesFailed'),
          description: t('translate.getLanguagesFailedDescription'),
          variant: "destructive",
        });
      }
    };

    fetchLanguages();
  }, []);

  // 组件挂载时重置到默认状态并聚焦输入框
  useEffect(() => {
    resetToDefault();
    // 延迟聚焦，确保组件完全渲染
    setTimeout(() => {
      inputTextareaRef.current?.focus();
    }, 100);
  }, []);

  // 监听外部设置文本的事件
  useEffect(() => {
    const handleSetText = (text: string) => {
      console.log('翻译组件收到外部文本:', text);
      setInputText(text);
      // 清空输出文本，准备重新翻译
      setOutputText('');

      // 延迟一下再触发翻译，确保 inputText 已经更新
      setTimeout(() => {
        if (text.trim()) {
          // 由于 handleTranslate 使用 inputText state，我们需要等待状态更新
          // 这里直接调用翻译API
          performTranslation(text);
        }
      }, 100);
    };

    // 通过全局事件监听来自主进程的设置文本事件
    const eventListener = (event: any) => {
      if (event.detail && event.detail.type === 'set-translator-text') {
        handleSetText(event.detail.text);
      } else if (event.detail && event.detail.type === 'show-copy-hint') {
        // 显示复制提示
        toast({
          title: '未检测到选中文字',
          description: '请先选中要翻译的文字，然后按 Cmd+C 复制，再粘贴到输入框中',
          duration: 4000,
        });
      }
    };

    window.addEventListener('electron-message', eventListener);

    return () => {
      window.removeEventListener('electron-message', eventListener);
    };
  }, []);

  // 重置到默认状态
  const resetToDefault = () => {
    setFromLanguage('auto');
    setToLanguage('zh');
    setOriginalFromLanguage('auto');
    setOriginalToLanguage('zh');
    setSwitchCount(0);
  };

  // 记录用户主动选择的语言设置（用于智能恢复自动检测）
  const recordUserLanguageChoice = (from: string, to: string) => {
    setOriginalFromLanguage(from);
    setOriginalToLanguage(to);
    setSwitchCount(0); // 重置切换计数
  };

  // 检测中文字符
  const containsChinese = (text: string) => {
    return /[\u4e00-\u9fff]/.test(text);
  };

  // 智能语言检测和设置
  const handleInputChange = (text: string) => {
    if (text.length <= 5000) {
      setInputText(text);

      // 智能语言检测：如果输入中文，自动设置目标语言为英文
      if (text.trim() && containsChinese(text) && toLanguage !== 'en') {
        setToLanguage('en');
      }
    }
  };

  // 翻译函数
  const handleTranslate = async () => {
    if (!inputText.trim()) {
      setOutputText('');
      return;
    }

    if (fromLanguage === toLanguage && fromLanguage !== 'auto') {
      setOutputText(inputText);
      return;
    }

    setIsTranslating(true);
    try {
      const response = await translateApi.baiduTranslate({
        text: inputText,
        from: fromLanguage,
        to: toLanguage
      });

      // 处理不同的响应格式
      let translatedText = '';
      let detectedLanguage = '';

      if (response.data) {
        // 尝试不同的响应格式
        if (response.data.result) {
          translatedText = response.data.result;
          detectedLanguage = response.data.from || '';
        } else if (response.data.trans_result && Array.isArray(response.data.trans_result)) {
          // 百度翻译API的标准格式
          translatedText = response.data.trans_result.map((item: any) => item.dst).join('');
          detectedLanguage = response.data.from || '';
        } else if (typeof response.data === 'string') {
          translatedText = response.data;
        } else if (response.data.translation) {
          translatedText = response.data.translation;
          detectedLanguage = response.data.from || '';
        } else {
          console.warn('未知的响应格式:', response.data);
          translatedText = JSON.stringify(response.data);
        }
      }

      // 智能目标语言调整：如果自动检测到英文，且目标语言也是英文，则自动切换为中文
      if (fromLanguage === 'auto' && detectedLanguage === 'en' && toLanguage === 'en') {
        console.log('检测到英文输入，目标语言也是英文，自动切换为中文');
        setToLanguage('zh');

        // 重新翻译为中文
        try {
          const retryResponse = await translateApi.baiduTranslate({
            text: inputText,
            from: fromLanguage,
            to: 'zh'
          });

          if (retryResponse.data) {
            if (retryResponse.data.result) {
              translatedText = retryResponse.data.result;
            } else if (retryResponse.data.trans_result && Array.isArray(retryResponse.data.trans_result)) {
              translatedText = retryResponse.data.trans_result.map((item: any) => item.dst).join('');
            } else if (typeof retryResponse.data === 'string') {
              translatedText = retryResponse.data;
            } else if (retryResponse.data.translation) {
              translatedText = retryResponse.data.translation;
            }
          }
        } catch (retryError) {
          console.error('重新翻译失败:', retryError);
          // 如果重新翻译失败，使用原来的结果
        }
      }

      setOutputText(translatedText || t('translate.translateFailed'));
    } catch (error) {
      console.error('翻译失败:', error);
      toast({
        title: t('translate.translateFailed'),
        description: t('translate.translateFailedDescription'),
        variant: "destructive",
      });
      setOutputText('');
    } finally {
      setIsTranslating(false);
    }
  };

  // 带文本参数的翻译函数（用于外部调用）
  const performTranslation = async (text: string) => {
    if (!text.trim()) {
      setOutputText('');
      return;
    }

    if (fromLanguage === toLanguage && fromLanguage !== 'auto') {
      setOutputText(text);
      return;
    }

    setIsTranslating(true);
    try {
      const response = await translateApi.baiduTranslate({
        text: text,
        from: fromLanguage,
        to: toLanguage
      });

      // 处理不同的响应格式
      let translatedText = '';
      let detectedLanguage = '';

      if (response.data) {
        // 尝试不同的响应格式
        if (response.data.result) {
          translatedText = response.data.result;
          detectedLanguage = response.data.from || '';
        } else if (response.data.trans_result && Array.isArray(response.data.trans_result)) {
          // 百度翻译API的标准格式
          translatedText = response.data.trans_result.map((item: any) => item.dst).join('');
          detectedLanguage = response.data.from || '';
        } else if (typeof response.data === 'string') {
          translatedText = response.data;
        } else if (response.data.translation) {
          translatedText = response.data.translation;
          detectedLanguage = response.data.from || '';
        } else {
          console.warn('未知的响应格式:', response.data);
          translatedText = JSON.stringify(response.data);
        }
      }

      // 智能目标语言调整：如果自动检测到英文，且目标语言也是英文，则自动切换为中文
      if (fromLanguage === 'auto' && detectedLanguage === 'en' && toLanguage === 'en') {
        console.log('检测到英文输入，目标语言也是英文，自动切换为中文');
        setToLanguage('zh');

        // 重新翻译为中文
        try {
          const retryResponse = await translateApi.baiduTranslate({
            text: text,
            from: fromLanguage,
            to: 'zh'
          });

          if (retryResponse.data) {
            if (retryResponse.data.result) {
              translatedText = retryResponse.data.result;
            } else if (retryResponse.data.trans_result && Array.isArray(retryResponse.data.trans_result)) {
              translatedText = retryResponse.data.trans_result.map((item: any) => item.dst).join('');
            } else if (typeof retryResponse.data === 'string') {
              translatedText = retryResponse.data;
            } else if (retryResponse.data.translation) {
              translatedText = retryResponse.data.translation;
            }
          }
        } catch (retryError) {
          console.error('重新翻译失败:', retryError);
        }
      }

      setOutputText(translatedText);
    } catch (error) {
      console.error('翻译失败:', error);
      toast({
        title: t('translate.translateFailed'),
        description: t('translate.translateFailedDescription'),
        variant: "destructive",
      });
      setOutputText('');
    } finally {
      setIsTranslating(false);
    }
  };

  // 输入文本变化时的防抖翻译
  useEffect(() => {
    if (translateTimeoutRef.current) {
      clearTimeout(translateTimeoutRef.current);
    }

    translateTimeoutRef.current = setTimeout(() => {
      handleTranslate();
    }, 500);

    return () => {
      if (translateTimeoutRef.current) {
        clearTimeout(translateTimeoutRef.current);
      }
    };
  }, [inputText, fromLanguage, toLanguage]);

  return (
    <div className="flex flex-col h-full max-h-[400px] p-5 space-y-4 bg-gradient-to-br from-slate-50/50 to-blue-50/30 dark:from-slate-900/50 dark:to-blue-950/30">
      {/* 语言选择栏 */}
      <div className="flex items-center gap-4">
        <div className="flex-1">
          <div className="text-xs font-medium text-slate-600 dark:text-slate-400 mb-1.5 ml-1">
            {t('translate.sourceLanguage')}
          </div>
          <LanguageSelect
            value={fromLanguage}
            onChange={(value) => {
              setFromLanguage(value);
              recordUserLanguageChoice(value, toLanguage);
            }}
            languages={languages}
            placeholder={t('translate.autoDetect')}
            currentLang={i18n.language}
          />
        </div>

        <div className="flex flex-col items-center pt-5">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              // 只交换语言选择，不交换文本内容

              // 增加切换计数
              const newSwitchCount = switchCount + 1;
              setSwitchCount(newSwitchCount);

              // 如果原始设置是自动检测，且已经切换了偶数次，则恢复到原始状态
              if (originalFromLanguage === 'auto' && newSwitchCount % 2 === 0) {
                setFromLanguage('auto');
                setToLanguage(originalToLanguage);
              } else if (fromLanguage === 'auto') {
                // 当前是自动检测，第一次切换到具体语言
                setFromLanguage(toLanguage);
                // 智能设置目标语言：如果当前目标语言是中文，则设置为英文，否则设置为中文
                if (toLanguage === 'zh') {
                  setToLanguage('en');
                } else {
                  setToLanguage('zh');
                }
              } else {
                // 正常交换语言
                const newFromLanguage = toLanguage;
                const newToLanguage = fromLanguage;
                setFromLanguage(newFromLanguage);
                setToLanguage(newToLanguage);
              }

              // 左侧内容不动，右侧清空并重新翻译
              setOutputText('');

              // 如果有输入内容，立即重新翻译
              if (inputText.trim()) {
                // 使用setTimeout确保状态更新后再翻译
                setTimeout(() => {
                  handleTranslate();
                }, 100);
              }
            }}
            disabled={!outputText.trim()} // 只有当有翻译结果时才能交换
            className="h-8 w-8 p-0 rounded-full bg-white/80 dark:bg-slate-800/80 shadow-sm hover:shadow-md hover:bg-blue-50 dark:hover:bg-blue-950/50 transition-all duration-200 disabled:opacity-50"
          >
            <ArrowLeftRight className="w-4 h-4 text-blue-600 dark:text-blue-400" />
          </Button>
        </div>

        <div className="flex-1">
          <div className="text-xs font-medium text-slate-600 dark:text-slate-400 mb-1.5 ml-1">
            {t('translate.targetLanguage')}
          </div>
          <LanguageSelect
            value={toLanguage}
            onChange={(value) => {
              setToLanguage(value);
              recordUserLanguageChoice(fromLanguage, value);
            }}
            languages={languages.filter(lang => lang.code !== 'auto')}
            placeholder={t('translate.selectLanguage')}
            currentLang={i18n.language}
          />
        </div>
      </div>

      {/* 翻译区域 */}
      <div className="flex-1 flex gap-4 min-h-0">
        {/* 输入区域 */}
        <div className="flex-1 flex flex-col">
          <div className="relative flex-1 group">
            <textarea
              ref={inputTextareaRef}
              value={inputText}
              onChange={(e) => handleInputChange(e.target.value)}
              placeholder={t('translate.inputPlaceholder')}
              className="w-full h-full p-4 border-2 border-transparent rounded-xl resize-none focus:outline-none focus:border-blue-600 dark:focus:border-blue-400 bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm text-foreground text-sm leading-relaxed shadow-sm hover:shadow-md focus:shadow-lg transition-all duration-200 placeholder:text-slate-400 dark:placeholder:text-slate-500"
            />
            <div className="absolute bottom-1 right-3 text-xs text-slate-500 dark:text-slate-400 bg-white/80 dark:bg-slate-800/80 px-2 py-1 rounded-full backdrop-blur-sm shadow-sm z-10">
              {inputText.length}/5000
            </div>
          </div>
        </div>

        {/* 分割线 */}
        <div className="w-px bg-gradient-to-b from-transparent via-slate-200 dark:via-slate-700 to-transparent"></div>

        {/* 输出区域 */}
        <div className="flex-1 flex flex-col">
          <div className="relative flex-1 group">
            <textarea
              value={outputText}
              readOnly
              placeholder={isTranslating ? t('translate.translating') : t('translate.outputPlaceholder')}
              className="w-full h-full p-4 border-2 border-transparent rounded-xl resize-none focus:outline-none focus:border-blue-600 dark:focus:border-blue-400 bg-gradient-to-br from-blue-50/50 to-indigo-50/50 dark:from-blue-950/30 dark:to-indigo-950/30 backdrop-blur-sm text-foreground text-sm leading-relaxed shadow-sm hover:shadow-md transition-all duration-200 placeholder:text-slate-400 dark:placeholder:text-slate-500"
            />
            {outputText && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  navigator.clipboard.writeText(outputText);
                  toast({
                    title: t('translate.copied'),
                    description: t('translate.copiedDescription'),
                  });
                }}
                className="absolute top-2 right-2 h-7 w-7 p-0 rounded-lg bg-white/80 dark:bg-slate-800/80 hover:bg-blue-50 dark:hover:bg-blue-950/50 shadow-sm hover:shadow-md transition-all duration-200 z-10"
              >
                <Copy className="w-3.5 h-3.5 text-blue-600 dark:text-blue-400" />
              </Button>
            )}
            {isTranslating && (
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 flex items-center gap-2 text-sm text-blue-600 dark:text-blue-400 bg-white/90 dark:bg-slate-800/90 px-3 py-2 rounded-xl backdrop-blur-md shadow-lg z-20">
                <div className="w-2.5 h-2.5 bg-blue-500 rounded-full animate-pulse"></div>
                {t('translate.translating')}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 底部工具栏 - 暂时注释掉上传和下载功能 */}
      {/*
      <div className="flex items-center justify-between pt-3 border-t border-slate-200/50 dark:border-slate-700/50">
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => fileInputRef.current?.click()}
            className="h-8 px-3 text-xs rounded-lg bg-white/60 dark:bg-slate-800/60 hover:bg-blue-50 dark:hover:bg-blue-950/50 shadow-sm hover:shadow-md transition-all duration-200"
          >
            <Upload className="w-3.5 h-3.5 mr-1.5 text-blue-600 dark:text-blue-400" />
            <span className="text-slate-700 dark:text-slate-300">
              {uploadedFileName ? uploadedFileName.slice(0, 12) + '...' : t('translate.uploadFile')}
            </span>
          </Button>
          <input
            ref={fileInputRef}
            type="file"
            accept=".txt,.doc,.docx,.pdf"
            className="hidden"
            onChange={(e) => {
              const file = e.target.files?.[0];
              if (file) {
                setUploadedFileName(file.name);
                const reader = new FileReader();
                reader.onload = (event) => {
                  const text = event.target?.result as string;
                  handleInputChange(text.length <= 5000 ? text : text.slice(0, 5000));
                  if (text.length > 5000) {
                    toast({
                      title: t('translate.fileTooLarge'),
                      description: t('translate.fileTooLargeDescription'),
                      variant: "destructive",
                    });
                  }
                };
                reader.readAsText(file);
              }
            }}
          />
        </div>

        <div className="flex items-center gap-2">
          {outputText && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                if (outputText) {
                  const blob = new Blob([outputText], { type: 'text/plain' });
                  const url = URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = `translated-to-${toLanguage}.txt`;
                  document.body.appendChild(a);
                  a.click();
                  document.body.removeChild(a);
                  URL.revokeObjectURL(url);
                }
              }}
              className="h-8 px-3 text-xs rounded-lg bg-white/60 dark:bg-slate-800/60 hover:bg-green-50 dark:hover:bg-green-950/50 shadow-sm hover:shadow-md transition-all duration-200"
            >
              <Download className="w-3.5 h-3.5 mr-1.5 text-green-600 dark:text-green-400" />
              <span className="text-slate-700 dark:text-slate-300">{t('translate.download')}</span>
            </Button>
          )}
        </div>
      </div>
      */}
    </div>
  );
};

export default Translator;
