import { useNavigate, useLocation } from 'react-router-dom';
import { useCallback } from 'react';
import { ROUTES } from '../router';

/**
 * 主窗口导航钩子
 * 提供路由跳转和状态管理功能
 */
export const useHomeNavigation = () => {
  const navigate = useNavigate();
  const location = useLocation();

  /**
   * 跳转到搜索页面
   * @param query 搜索查询参数
   * @param preserveQuery 是否保留当前查询参数
   */
  const goToSearch = useCallback((query?: string, preserveQuery = false) => {
    const searchParams = new URLSearchParams();
    
    if (query) {
      searchParams.set('q', query);
    } else if (preserveQuery) {
      const currentQuery = new URLSearchParams(location.search).get('q');
      if (currentQuery) {
        searchParams.set('q', currentQuery);
      }
    }
    
    const queryString = searchParams.toString();
    navigate(`${ROUTES.SEARCH}${queryString ? `?${queryString}` : ''}`);
  }, [navigate, location.search]);

  /**
   * 跳转到AI对话页面
   * @param initialQuery 初始查询内容
   */
  const goToAiChat = useCallback((initialQuery?: string) => {
    const searchParams = new URLSearchParams();
    if (initialQuery) {
      searchParams.set('q', initialQuery);
    }
    
    const queryString = searchParams.toString();
    navigate(`${ROUTES.AI_CHAT}${queryString ? `?${queryString}` : ''}`);
  }, [navigate]);

  /**
   * 跳转到文件搜索页面
   * @param initialQuery 初始搜索内容
   */
  const goToFileSearch = useCallback((initialQuery?: string) => {
    const searchParams = new URLSearchParams();
    if (initialQuery) {
      searchParams.set('q', initialQuery);
    }
    
    const queryString = searchParams.toString();
    navigate(`${ROUTES.FILE_SEARCH}${queryString ? `?${queryString}` : ''}`);
  }, [navigate]);

  /**
   * 跳转到剪贴板页面
   */
  const goToClipboard = useCallback(() => {
    navigate(ROUTES.CLIPBOARD);
  }, [navigate]);

  /**
   * 跳转到翻译页面
   * @param text 待翻译文本
   */
  const goToTranslate = useCallback((text?: string) => {
    const searchParams = new URLSearchParams();
    if (text) {
      searchParams.set('text', text);
    }
    
    const queryString = searchParams.toString();
    navigate(`${ROUTES.TRANSLATE}${queryString ? `?${queryString}` : ''}`);
  }, [navigate]);

  /**
   * 返回上一页
   */
  const goBack = useCallback(() => {
    navigate(-1);
  }, [navigate]);

  /**
   * 获取当前路由信息
   */
  const getCurrentRoute = useCallback(() => {
    return {
      pathname: location.pathname,
      search: location.search,
      searchParams: new URLSearchParams(location.search)
    };
  }, [location]);

  /**
   * 检查当前是否在指定路由
   */
  const isCurrentRoute = useCallback((route: string) => {
    return location.pathname === route;
  }, [location.pathname]);

  /**
   * 根据输入类型自动导航到对应页面
   * @param query 用户输入
   * @param inputType 输入类型
   */
  const autoNavigateByInput = useCallback((query: string, inputType?: string) => {
    // 如果没有输入内容，跳转到搜索页面
    if (!query.trim()) {
      goToSearch();
      return;
    }

    // 根据输入类型决定跳转目标
    // 注意：颜色、数学、IP等特殊类型仍然在搜索页面处理
    switch (inputType) {
      case 'ai-request':
        goToAiChat(query);
        break;
      case 'file-search':
        goToFileSearch(query);
        break;
      default:
        // 默认跳转到搜索页面，包括颜色、数学、IP等特殊输入
        goToSearch(query);
        break;
    }
  }, [goToSearch, goToAiChat, goToFileSearch]);

  return {
    // 导航方法
    goToSearch,
    goToAiChat,
    goToFileSearch,
    goToClipboard,
    goToTranslate,
    goBack,
    
    // 路由信息
    getCurrentRoute,
    isCurrentRoute,
    
    // 智能导航
    autoNavigateByInput,
    
    // 当前路由状态
    currentPath: location.pathname,
    currentSearch: location.search,
    searchParams: new URLSearchParams(location.search)
  };
};

export default useHomeNavigation;