import React from 'react';

// 导入各个页面组件
import SearchPage from './pages/search/index';
import AiChatPage from './pages/ai-chat/index';
import FileSearchPage from './pages/file-search/index';
import ClipboardPage from './pages/clipboard/index';
import TranslatePage from './pages/translate/index';

// 路由类型定义
export type RouteType = '/' | '/ai-chat' | '/file-search' | '/clipboard' | '/translate';

// HomeRouter组件的Props接口
interface HomeRouterProps {
  currentRoute: RouteType;
  query: string;
  onRouteChange: (route: RouteType, searchQuery?: string) => void;
  onBackToHome: () => void;
  onAiChatComplete?: () => void;
}

/**
 * 主窗口路由组件
 * 基于状态进行路由渲染，而非使用React Router
 */
export const HomeRouter: React.FC<HomeRouterProps> = ({ 
  currentRoute, 
  query, 
  onRouteChange, 
  onBackToHome,
  onAiChatComplete 
}) => {
  // 根据当前路由渲染对应的页面组件
  const renderCurrentPage = () => {
    console.log('currentRoute: ', currentRoute)
    switch (currentRoute) {
      case '/':
        return (
          <SearchPage 
            query={query}
            onRouteChange={onRouteChange}
          />
        );
      
      case '/ai-chat':
        return (
          <AiChatPage 
            query={query} 
            onComplete={() => {
              // AI对话完成后调用搜索栏的状态重置函数
              console.log('AI对话完成，重置搜索栏状态');
              if (onAiChatComplete) {
                onAiChatComplete();
              }
            }}
          />
        );
      
      case '/file-search':
        return (
          <FileSearchPage 
            query={query}
          />
        );
      
      case '/clipboard':
        return <ClipboardPage />;
      
      case '/translate':
        return <TranslatePage />;
      
      default:
        return (
          <SearchPage 
            query={query}
            onRouteChange={onRouteChange}
          />
        );
    }
  };

  return (
    <div className="h-full w-full">
      {renderCurrentPage()}
    </div>
  );
};

/**
 * 路由路径常量
 * 用于在组件中进行路由跳转时使用
 */
export const ROUTES = {
  SEARCH: '/search',
  AI_CHAT: '/ai-chat', 
  FILE_SEARCH: '/file-search',
  CLIPBOARD: '/clipboard',
  TRANSLATE: '/translate'
} as const;



export default HomeRouter;