import React, { useState, useEffect } from 'react';
import { fileManagerClient } from '../../../../services/api/file-manager';
import { FileInfo } from '../../../../../shared/types/file';
import { useTranslation } from 'react-i18next';
import FileSearchResults from '../../../../components/file-search-results';
import FilePreview from '../../../../components/file-preview';

interface FileSearchProps {
  query: string;
  onComplete?: () => void;
}

/**
 * 文件搜索组件 (重构后)
 * 使用 FileSearchResults 和 FilePreview 来展示界面
 */
const FileSearch: React.FC<FileSearchProps> = ({ query }) => {
  const { t } = useTranslation();
  const [selectedFile, setSelectedFile] = useState<FileInfo | null>(null);

  useEffect(() => {
    setSelectedFile(null);
  }, [query]);

  const handleFileSelect = (file: FileInfo) => {
    setSelectedFile(file);
  };

  const handleOpenFile = () => {
    if (selectedFile) {
      fileManagerClient.openFile(selectedFile.path);
    }
  };

  const handleOpenFileLocation = () => {
    if (selectedFile) {
      fileManagerClient.openFileLocation(selectedFile.path);
    }
  };

  return (
    <div className="flex bg-background border rounded-b-lg overflow-hidden shadow-lg max-h-[85vh]">
      <div className="w-1/2 border-r overflow-hidden">
        <FileSearchResults
          query={query || ''}
          onSelectFile={handleFileSelect}
          selectedFilePath={selectedFile?.path || null}
        />
      </div>

      <div className="w-1/2 overflow-hidden">
        <FilePreview
          filePath={selectedFile?.path || null}
          fileName={selectedFile?.name || null}
          fileType={!selectedFile?.extension ? 'directory' : selectedFile?.extension || null}
          fileSize={selectedFile?.size || null}
          onOpen={handleOpenFile}
          onOpenLocation={handleOpenFileLocation}
        />
      </div>
    </div>
  );
};

export default FileSearch;
