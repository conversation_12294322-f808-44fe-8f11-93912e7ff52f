import React, { useState, useRef, useEffect, useContext } from 'react';
import { FileText } from 'lucide-react';
import { ClipboardItem } from '../../../../../shared/types/clipboard';
import { SearchBarContext } from '../../index';
import { useClipboardHistory } from '../../../../hooks/useClipboardHistory';
import { getItemContent, formatTime, getIcon } from '../../../../utils/clipboard';
import '../../../../pages/clipboard-history/clipboard-history.css';
import ClipboardItemPreview from '../../../../components/clipboard-item-preview';

interface ClipboardHistoryViewProps {
  onBack?: () => void;
}

const ClipboardHistoryView: React.FC<ClipboardHistoryViewProps> = ({ onBack }) => {
  const scrollRef = useRef<HTMLDivElement>(null);

  // 从SearchBarContext获取搜索查询
  const { query: searchQuery } = useContext(SearchBarContext);

  // 使用自定义hook管理剪贴板历史
  const {
    filteredData,
    selectedItemId,
    selectedIndex,
    isLoading,
    hasMoreData,
    totalCount,
    isVip,
    hasReachedFreeLimit,
    freeItemsUsed,
    maxFreeItems,
    handleItemClick,
    handleItemDoubleClick: originalHandleItemDoubleClick,
    setSelectedIndex,
    onScroll,
    resetSelection,
  } = useClipboardHistory({
    searchQuery,
    keyboardNavigation: 'vertical',
    autoSelectFirst: true,
    onItemSelect: onBack,
  });

  // 监听窗口重置事件（主窗口模式下的剪切板历史重置）
  useEffect(() => {
    const handleWindowReset = () => {
      console.log('主窗口剪切板历史收到重置事件，重置状态');
      // 重置选中项到第一个
      resetSelection();
      // 重置滚动位置到顶部
      if (scrollRef.current) {
        scrollRef.current.scrollTop = 0;
      }
    };

    // 监听来自主进程的窗口重置消息
    const removeListener = window.electron.clipboardHistory.onReset(handleWindowReset);

    return () => {
      removeListener();
    };
  }, [resetSelection]);

  // 自定义的双击处理（包含回调）
  const handleItemDoubleClick = async (item: ClipboardItem) => {
    await originalHandleItemDoubleClick(item);
  };

  // 滚动事件处理
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const element = e.currentTarget;
    onScroll(element);
  };

  // 自动滚动以保证选中项可见
  useEffect(() => {
    const container = scrollRef.current;
    if (!container) return;
    const selectedEl = container.querySelector(`[data-index='${selectedIndex}']`) as HTMLElement | null;
    if (!selectedEl) return;
    selectedEl.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'nearest' });
  }, [selectedIndex]);

  const selectedItem = filteredData[selectedIndex] || null;

  return (
    <div className="flex h-full bg-background min-h-0">
      {/* 左侧：历史列表 */}
      <div className="w-2/5 flex flex-col border-r">
        <div className="flex-1 overflow-hidden p-4">
          <div
            ref={scrollRef}
            className="h-full overflow-y-auto scrollbar-hide"
            style={{ maxHeight: 'calc(100vh - 120px)' }}
            onScroll={handleScroll}
          >
            {filteredData.length === 0 && !isLoading ? (
              <div className="flex items-center justify-center text-muted-foreground min-h-[200px]">
                <div className="text-center">
                  <FileText className="w-12 h-12 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">
                    {searchQuery ? '没有找到匹配的内容' : '剪贴板历史为空'}
                  </p>
                </div>
              </div>
            ) : (
              <div className="space-y-2 pb-4">
                {filteredData.map((item, index) => (
                  <div
                    key={item.id}
                    data-index={index}
                    className={`
                      group relative bg-card border rounded-lg p-3 cursor-pointer
                      hover:bg-accent hover:border-accent-foreground/20 hover:shadow-sm
                      ${selectedItemId === item.id ? 'bg-selected border-primary' : ''}
                    `}
                    onClick={() => handleItemClick(item)}
                    onDoubleClick={() => handleItemDoubleClick(item)}
                  >
                    <div className="flex items-start gap-3">
                      {/* 图标 */}
                      <div className="flex-shrink-0 text-muted-foreground mt-1">
                        {getIcon(item)}
                      </div>

                      {/* 内容 */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-xs text-muted-foreground">
                            {formatTime(item.timestamp)}
                          </span>
                          <span className="text-xs text-muted-foreground capitalize">
                            {item.type}
                          </span>
                        </div>

                        {/* 根据类型显示内容 */}
                        {item.type === 'text' && (
                          <p className="text-sm text-foreground line-clamp-3 break-words">
                            {getItemContent(item)}
                          </p>
                        )}

                        {item.type === 'image' && (
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-foreground">
                              {getItemContent(item)}
                            </span>
                          </div>
                        )}

                        {(item.type === 'file' || item.type === 'directory') && (
                          <div className="space-y-1">
                            <p className="text-sm font-medium text-foreground">
                              {(item.items[0] as any).name}
                            </p>
                            {item.items.length > 1 && (
                              <p className="text-xs text-muted-foreground">
                                +{item.items.length - 1} 个其他项目
                              </p>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
                
                {/* 加载中指示器 */}
                {isLoading && (
                  <div className="flex items-center justify-center py-4">
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
                      <span className="text-sm">加载中...</span>
                    </div>
                  </div>
                )}
                
                {/* 没有更多数据指示器 */}
                {!hasMoreData && filteredData.length > 0 && (
                  <div className="flex items-center justify-center py-4">
                    {hasReachedFreeLimit && !isVip ? (
                      <div className="text-center space-y-2">
                        <span className="text-xs text-muted-foreground block">
                          非会员限制：{freeItemsUsed}/{maxFreeItems} 条记录
                        </span>
                        <div className="text-xs text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 px-3 py-2 rounded-md">
                          💎 升级会员可查看所有 {totalCount} 条记录
                        </div>
                      </div>
                    ) : (
                      <span className="text-xs text-muted-foreground">
                        已显示全部 {totalCount} 条记录
                      </span>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 右侧：预览区 */}
      <div className="w-3/5 h-full max-h-full min-h-0 flex flex-col">
        <ClipboardItemPreview item={selectedItem} />
      </div>
    </div>
  );
};

export default ClipboardHistoryView; 