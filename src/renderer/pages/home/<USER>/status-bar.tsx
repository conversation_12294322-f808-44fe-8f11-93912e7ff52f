import React from 'react';
import { useTranslation } from 'react-i18next';
import { windowManagerClient } from '../../../services/api/window-manager';
import { Button } from '../../../components/ui/button';
import { Code, Settings } from 'lucide-react';
// import UserProfile from '../../../components/UserProfile';
// import LoginDialog from '../../../components/LoginDialog';

interface StatusBarProps {
  items?: {
    label: string;
    onClick?: () => void;
  }[];
}

/**
 * 全局状态栏组件
 * 固定在窗口底部的状态栏，用于显示应用信息和快捷操作
 */
const StatusBar: React.FC<StatusBarProps> = ({
  items = [
    
  ]
}) => {
  const { t } = useTranslation();

  const openDevTools = () => {
    windowManagerClient.openDevTools();
  };

  return (
    <>
      <div className="fixed bottom-0 left-0 w-full border-t px-1 py-1 flex justify-between items-center text-lg text-muted-foreground dark:text-gray-100 bg-gray-100 dark:bg-gray-900 backdrop-blur-sm z-50">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => window.electron.window.showSettingsWindow()}
            title={t('settings.title')}
            className="h-8 w-8"
          >
            <Settings className="h-3.5 w-3.5" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={openDevTools}
            title={t('tools.devTools')}
            className="h-8 w-8"
          >
            <Code className="h-3.5 w-3.5" />
          </Button>
        </div>
        
        <div className="flex items-center space-x-4">
          {/* 状态栏项目 */}
          {/* <div className="flex space-x-4">
            {items.map((item, index) => (
              <span 
                key={index}
                className="cursor-pointer hover:text-foreground dark:hover:text-white hover:underline dark:text-gray-100"
                onClick={item.onClick}
              >
                {item.label}
              </span>
            ))}
          </div> */}
          
          {/* 用户信息 */}
          {/* <div className="border-l border-border pl-4">
            <UserProfile variant="compact" />
          </div> */}
        </div>
      </div>
      
      {/* 登录弹窗 */}
      {/* <LoginDialog /> */}
    </>
  );
};

export default StatusBar; 