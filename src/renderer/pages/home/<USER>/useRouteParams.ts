import { useSearchParams } from 'react-router-dom';
import { useCallback, useMemo } from 'react';

/**
 * 路由参数管理钩子
 * 统一管理URL查询参数的读取和更新
 */
export const useRouteParams = () => {
  const [searchParams, setSearchParams] = useSearchParams();

  /**
   * 获取查询参数
   */
  const query = useMemo(() => searchParams.get('q') || '', [searchParams]);
  
  /**
   * 获取翻译文本参数
   */
  const translateText = useMemo(() => searchParams.get('text') || '', [searchParams]);

  /**
   * 获取文件路径参数
   */
  const filePath = useMemo(() => searchParams.get('file') || '', [searchParams]);

  /**
   * 更新查询参数
   */
  const updateQuery = useCallback((newQuery: string) => {
    const newParams = new URLSearchParams(searchParams);
    if (newQuery.trim()) {
      newParams.set('q', newQuery);
    } else {
      newParams.delete('q');
    }
    setSearchParams(newParams);
  }, [searchParams, setSearchParams]);

  /**
   * 更新翻译文本参数
   */
  const updateTranslateText = useCallback((text: string) => {
    const newParams = new URLSearchParams(searchParams);
    if (text.trim()) {
      newParams.set('text', text);
    } else {
      newParams.delete('text');
    }
    setSearchParams(newParams);
  }, [searchParams, setSearchParams]);

  /**
   * 更新文件路径参数
   */
  const updateFilePath = useCallback((path: string) => {
    const newParams = new URLSearchParams(searchParams);
    if (path.trim()) {
      newParams.set('file', path);
    } else {
      newParams.delete('file');
    }
    setSearchParams(newParams);
  }, [searchParams, setSearchParams]);

  /**
   * 清除所有参数
   */
  const clearParams = useCallback(() => {
    setSearchParams(new URLSearchParams());
  }, [setSearchParams]);

  /**
   * 设置多个参数
   */
  const setParams = useCallback((params: Record<string, string>) => {
    const newParams = new URLSearchParams(searchParams);
    
    Object.entries(params).forEach(([key, value]) => {
      if (value.trim()) {
        newParams.set(key, value);
      } else {
        newParams.delete(key);
      }
    });
    
    setSearchParams(newParams);
  }, [searchParams, setSearchParams]);

  /**
   * 获取所有参数对象
   */
  const allParams = useMemo(() => {
    const params: Record<string, string> = {};
    searchParams.forEach((value, key) => {
      params[key] = value;
    });
    return params;
  }, [searchParams]);

  return {
    // 参数值
    query,
    translateText,
    filePath,
    allParams,
    
    // 更新方法
    updateQuery,
    updateTranslateText,
    updateFilePath,
    setParams,
    clearParams,
    
    // 原始对象
    searchParams,
    setSearchParams
  };
};

export default useRouteParams;