import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Settings, 
  Clipboard, 
  MessageSquareText, 
  Layout, 
  Cat, 
  Cloud, 
  User, 
  Info,
  LayoutList
} from 'lucide-react';
import { ThemeProvider } from '../../components/theme-provider';
import { useAuthStore } from '../../stores/authStore';

// 引入各设置组件
import GeneralSettings from './components/GeneralSettings';
import ClipboardSettings from './components/ClipboardSettings';
import AISettings from './components/AISettings';
import DesktopSettings from './components/DesktopSettings';
import PetSettings from './components/PetSettings';
import CloudSettings from './components/CloudSettings';
import AccountSettings from './components/AccountSettings';
import AboutSettings from './components/AboutSettings';
import LoginDialog from '../../components/LoginDialog';
import Mcpcn from './components/Mcpcn';

// 注意：菜单项将在组件内部使用 t() 函数获取翻译
const menuKeys = [
  { key: 'general', icon: <Settings /> },
  { key: 'clipboard', icon: <Clipboard /> },
  { key: 'ai', icon: <MessageSquareText /> },
  { key: 'desktop', icon: <Layout /> },
  { key: 'pet', icon: <Cat /> },
  { key: 'cloud', icon: <Cloud /> },
  { key: 'account', icon: <User /> },
  { key: 'about', icon: <Info /> },
  { key: 'mcpcn', icon: <LayoutList /> },
];

const SettingsPage: React.FC = () => {
  const { t } = useTranslation();
  const [activeMenu, setActiveMenu] = useState('general');
  const [settings, setSettings] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  // 在组件加载时获取所有设置
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const allSettings = await window.electron.settings.get();
        setSettings(allSettings);
      } catch (error) {
        console.error('加载设置失败:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadSettings();
  }, []);

  // 监听用户状态变化事件
  useEffect(() => {
    const handleUserStatusChange = (authState: any) => {
      // 更新本地 authStore 状态，使用标志避免循环广播
      if (authState && authState.user) {
        console.log('📝 设置窗口更新为登录状态');
        // 设置跳过广播标志，然后更新完整的认证状态
        useAuthStore.setState({
          _skipBroadcast: false, // 重置标志，准备接受下次操作
          user: authState.user,
          token: authState.token,
          isLoggedIn: true,
          loginDialogOpen: false // 关闭登录弹窗
        });
      } else {
        console.log('📝 设置窗口更新为登出状态');
        // 登出状态：直接更新状态，不要调用logout方法，避免循环广播
        useAuthStore.setState({ 
          user: null,
          token: null,
          isLoggedIn: false,
          loginDialogOpen: false,
          _skipBroadcast: false // 重置标志，准备接受下次操作
        });
      }
    };

    // 监听来自主进程的用户状态变化事件
    const removeListener = window.electron.system.onUserStatusChanged(handleUserStatusChange);

    return () => {
      removeListener();
    };
  }, []);

  // 更新设置的方法
  const updateSettings = async (newSettings: any) => {
    try {
      await window.electron.settings.update(newSettings);
      // 重新加载所有设置
      const allSettings = await window.electron.settings.get();
      setSettings(allSettings);
    } catch (error) {
      console.error('更新设置失败:', error);
    }
  };

  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="text-gray-500 dark:text-gray-400">{t('settings.loading')}</div>
        </div>
      );
    }

    switch (activeMenu) {
      case 'general':
        return <GeneralSettings settings={settings} onUpdate={updateSettings} />;
      case 'clipboard':
        return <ClipboardSettings settings={settings} onUpdate={updateSettings} />;
      case 'ai':
        return <AISettings settings={settings} onUpdate={updateSettings} />;
      case 'desktop':
        return <DesktopSettings settings={settings} onUpdate={updateSettings} />;
      case 'pet':
        return <PetSettings settings={settings} onUpdate={updateSettings} />;
      case 'cloud':
        return <CloudSettings settings={settings} onUpdate={updateSettings} />;
      case 'account':
        return <AccountSettings />;
      case 'about':
        return <AboutSettings settings={settings} onUpdate={updateSettings} />;
      case 'mcpcn':
        return <Mcpcn />;
      default:
        return <GeneralSettings settings={settings} onUpdate={updateSettings} />;
    }
  };

  return (
    <ThemeProvider defaultTheme="system">
      <div className="flex h-screen bg-white dark:bg-gray-900">
        {/* 左侧菜单栏 */}
        <div className="w-60 bg-gray-50 dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col">
          {/* 菜单项 */}
          <div className="flex-1 py-4 overflow-y-auto">
            {menuKeys.map(item => (
              <div
                key={item.key}
                className={`
                  flex items-center px-5 py-3 cursor-pointer mb-1 mx-2 rounded-lg group
                  ${activeMenu === item.key 
                    ? 'bg-blue-100 text-blue-700 font-medium shadow-sm dark:bg-blue-900 dark:text-blue-300' 
                    : 'text-gray-600 hover:bg-gray-200 dark:text-gray-300 dark:hover:bg-gray-700'
                  }
                `}
                onClick={() => setActiveMenu(item.key)}
              >
                <div className={`
                  p-1.5 rounded-md mr-3
                  ${activeMenu === item.key 
                    ? 'text-blue-700 dark:text-blue-300' 
                    : 'text-gray-500 dark:text-gray-400'
                  }
                `}>
                  {React.cloneElement(item.icon, { 
                    size: 18
                  })}
                </div>
                <span className="text-base">{t(`settings.menu.${item.key}`)}</span>
              </div>
            ))}
          </div>
        </div>
        
        {/* 内容区域 */}
        <div className="flex-1 overflow-y-auto dark:bg-gray-900">
          {renderContent()}
        </div>
      </div>
      <LoginDialog />
      <div id="captcha" style={{zIndex: 999999, pointerEvents: 'auto'}} onClick={(e) => {
          e.stopPropagation()
          e.preventDefault()
        }}></div>
    </ThemeProvider>
  );
};

export default SettingsPage; 