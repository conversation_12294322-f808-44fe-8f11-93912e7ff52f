import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Switch, Select, Input, Button } from '../../../components/ui';
import { Crown, ArrowLeft, Plus, X, Info } from 'lucide-react';
import CharacterSettings from './CharacterSettings';
import SettingItem from '../../../components/SettingItem';
import xiaozhiApi from '../../../api/xiaozhi';
import { useAuthStore } from '../../../stores/authStore';
import { formatTime } from '../../../utils/timeUtils';

interface DesktopSettingsProps {
  settings: any;
  onUpdate: (settings: any) => void;
}

const DesktopSettings: React.FC<DesktopSettingsProps> = ({ settings, onUpdate }) => {
  const { t } = useTranslation();
  const [showCharacterSettings, setShowCharacterSettings] = useState(false);
  
  // 从 props 中获取当前设置
  const currentSettings = settings?.desktopAssistant || {};
  const currentPetSettings = settings?.pet || {};
  
  // 角色设置状态
  const [characterName, setCharacterName] = useState('');
  const [voiceCharacter, setVoiceCharacter] = useState('');
  const [lastInteraction, setLastInteraction] = useState('');
  const [assistantInfo, setAssistantInfo] = useState<any>(null);
  const { isLoggedIn } = useAuthStore();
  
  useEffect(() => {
    const deviceId = localStorage.getItem('deviceId');
    if(isLoggedIn && deviceId) {
      getCharacterInfo();
    }
  }, [isLoggedIn]);
  const getCharacterInfo = () => {
    xiaozhiApi.assistantList().then((res: any) => {
      if(res.data) {
        setCharacterName(res.data.agentName);
        setVoiceCharacter(res.data.ttsVoiceName);
        res.data.lastConnectedAt && setLastInteraction(formatTime(res.data.lastConnectedAt));
        setAssistantInfo(res.data);
      }
    });
  }
  const handleFloatingBallChange = (checked: boolean) => {
    // 悬浮球助手开关控制悬浮球的显示与隐藏
    onUpdate({
      ...settings,
      desktopAssistant: { ...settings.desktopAssistant, floatingBall: checked ? 'floating-ball' : 'live2d' }
    });
    
    // 通知主进程控制悬浮球显示/隐藏
    // if (checked) {
    //   window.electron.floatingBall.show();
    // } else {
    //   window.electron.floatingBall.hide();
    // }
  };

  const handleDesktopChange = (checked: boolean) => {
    // 动态桌面助手开关控制pet助手的显示与隐藏
    onUpdate({
      ...settings,
      pet: { ...settings.pet, available: checked }
    });
    // 这里不再控制主窗口显隐，只通过设置 pet.available 让主进程处理 pet 窗口
  };

  if (showCharacterSettings) {
    // 角色设置页面
    return (
      <CharacterSettings
        assistantId={assistantInfo?.id}
        onBack={() => setShowCharacterSettings(false)}
        getCharacterInfo={getCharacterInfo}
      />
    );
  }

  // 主设置页面
  return (
    <div className="px-4 space-y-0 dark:bg-gray-900 dark:text-gray-100">
      {/* 悬浮球助手 */}
      <SettingItem label={t('settings.desktop.floatingBall')}>
        <div className="flex items-center gap-2">
          <span className="text-xs text-gray-500 dark:text-gray-400">{t('settings.desktop.floatingBallDemo')}</span>
          <Switch 
            checked={currentSettings.floatingBall === 'floating-ball' || false}
            onChange={handleFloatingBallChange}
          />
        </div>
      </SettingItem>

      {/* VIP 动态桌面助手 */}
      <SettingItem label={t('settings.desktop.dynamicDesktop')}>
        <div className="flex items-center gap-2">
          <button className="flex items-center gap-1 px-3 py-1 bg-yellow-500 text-white text-xs rounded-full hover:bg-yellow-600 dark:bg-yellow-600 dark:hover:bg-yellow-700">
            <Crown className="w-3 h-3" />
            {t('settings.desktop.freeTrial')}
          </button>
          <Switch 
            checked={currentPetSettings.available || false}
            onChange={handleDesktopChange}
          />
        </div>
      </SettingItem>

      {/* 助手角色设置 */}
      <SettingItem label={t('settings.desktop.assistantSettings')}>
        <div className="flex items-center gap-4">
          {/* 头像已去除，如需恢复可加回来 */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <span className="text-sm font-medium text-gray-900 dark:text-gray-100">{characterName}</span>
              <span className="flex items-center justify-center">
                <Info className="w-4 h-4 text-gray-400 dark:text-gray-500" aria-label={t('settings.desktop.infoTip') || '信息'} />
              </span>
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
              <div>{t('settings.desktop.voiceCharacter')} {voiceCharacter}</div>
              <div>{t('settings.desktop.lastInteraction')} {formatTime(lastInteraction)}</div>
            </div>
          </div>
          <button 
            onClick={() => setShowCharacterSettings(true)}
            className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
          >
            {t('settings.desktop.modifySettings')} {'>'}
          </button>
        </div>
      </SettingItem>
    </div>
  );
};

export default DesktopSettings; 