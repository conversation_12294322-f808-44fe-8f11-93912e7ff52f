import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Input, Select, Button } from '../../../components/ui';
import { ArrowLeft, Plus, X, RotateCcw } from 'lucide-react';
import SettingItem from '../../../components/SettingItem';
import xiaozhiApi from '../../../api/xiaozhi';
import { useToast } from '../../../components/ui/use-toast';

const CharacterSettings: React.FC<{ assistantId: string, onBack: () => void, getCharacterInfo: () => void }> = ({ assistantId, onBack, getCharacterInfo }) => {
  const { t } = useTranslation();
  const [assistantInfo, setAssistantInfo] = useState<any>(null);
  const [model, setModel] = useState('TTS_MinimaxStreamTTS');
  const [voiceOptions, setVoiceOptions] = useState([]);
  const [modelOptions, setModelOptions] = useState([]);
  const [agentTemplateOptions, setAgentTemplateOptions] = useState([]);
  const [mainPersonalityOptions, setMainPersonalityOptions] = useState([
    { value: 'loved_one', label: '亲爱的' },
    { value: 'master', label: '主人' },
    { value: 'friend', label: '老公' }
  ]);
  const { toast } = useToast();

  // const voiceOptions = [
  //   { value: 'xiaohe', label: '清澈小何' },
  //   { value: 'xiaoya', label: '温柔小雅' },
  //   { value: 'xiaoli', label: '活泼小丽' }
  // ];

  // const modelOptions = [  
  //   { value: 'TTS_MinimaxStreamTTS', label: 'MiniMax单流式语音合成' },
  //   { value: 'gpt-4o', label: 'gpt-4o' },
  //   { value: 'gpt-4o-mini', label: 'gpt-4o-mini' },
  //   { value: 'gpt-3.5-turbo', label: 'gpt-3.5-turbo' },
  //   { value: 'gpt-3.5-turbo-mini', label: 'gpt-3.5-turbo-mini' },
  // ];

  useEffect(() => {
    if(assistantId) {
      xiaozhiApi.assistantInfo(assistantId).then((res: any) => {
        setAssistantInfo(res.data);
      });
      xiaozhiApi.modelVoices(model).then((res: any) => {
        setVoiceOptions(res.data.map((item: any) => ({ value: item.id, label: item.name })));
      });
      xiaozhiApi.modelList('LLM').then((res: any) => {
        setModelOptions(res.data.map((item: any) => ({ value: item.id, label: item.modelName })));
      });
      xiaozhiApi.agentTemplateList().then((res: any) => {
        setAgentTemplateOptions(res.data);
      });
    }
  }, [assistantId]);

  const handleRemovePersonality = (idx: number) => {
    setMainPersonalityOptions((options: { value: string; label: string }[]) => options.filter((_: { value: string; label: string }, i: number) => i !== idx));
  };

  const handleResetSystemPrompt = () => {
    setAssistantInfo({ ...assistantInfo, systemPrompt: agentTemplateOptions[0].systemPrompt });
    toast({
      title: '个性设置已恢复到默认值',
      variant: 'default'
    });
  };

  const handleSave = () => {
    xiaozhiApi.updateAssistant(assistantInfo).then((res: any) => {
      if(res.code === 0) {
        toast({
          title: '保存成功',
          variant: 'success'
        });
      }
    });
  };

  return (
    <div className="flex h-full dark:bg-gray-900 dark:text-gray-100">
      {/* 左侧：角色配置表单 */}
      <div className="flex-1 px-6 py-4 overflow-y-auto">
        {/* 返回按钮 */}
        <div className="flex items-center mb-6">
          <button
            onClick={() => {
              getCharacterInfo();
              onBack();
            }}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg mr-3"
          >
            <ArrowLeft className="w-5 h-5 text-gray-600 dark:text-gray-400" />
          </button>
          <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            {t('settings.desktop.characterSetting')}
          </h2>
        </div>

        {/* 角色名称 */}
        <SettingItem label={t('settings.desktop.assistantName')}>
          <div className="flex items-center gap-2">
            {/* <span className="text-xs text-gray-500 dark:text-gray-400 whitespace-nowrap">{t('settings.desktop.floatingBallDemo')}</span> */}
            <Input
              value={assistantInfo?.agentName}
              onChange={(e) => setAssistantInfo({ ...assistantInfo, agentName: e.target.value })}
              className="w-full dark:bg-gray-800 dark:border-gray-700 dark:text-gray-100"
            />
          </div>
        </SettingItem>
        {/* 个性设置 */}
        <SettingItem label={t('settings.desktop.personalitySettings')}>
          <div className="relative">
            <textarea
              value={assistantInfo?.systemPrompt}
              onChange={(e) => setAssistantInfo({ ...assistantInfo, systemPrompt: e.target.value })}
              rows={4}
              style={{ width: '380px' }}
              className="w-full px-3 py-2 pr-12 border border-gray-300 dark:border-gray-700 rounded-md text-sm dark:bg-gray-800 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
            />
            <button
              onClick={handleResetSystemPrompt}
              className="absolute bottom-2 right-2 p-1 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
              title="恢复默认设置"
            >
              <RotateCcw className="w-4 h-4" />
            </button>
          </div>
        </SettingItem>
        {/* 声音角色 */}
        <SettingItem label={t('settings.desktop.voiceCharacter')}>
          <div className="flex flex-col items-end gap-2">
            <Select
              options={voiceOptions}
              value={assistantInfo?.ttsVoiceId}
              onChange={(value) => setAssistantInfo({ ...assistantInfo, ttsVoiceId: value })}
              className="w-full dark:bg-gray-800 dark:border-gray-700 dark:text-gray-100"
              minWidth="min-w-[120px]"
            />
            {/* <button className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 whitespace-nowrap">
              {t('settings.desktop.customVoiceColors')}
            </button> */}
          </div>
        </SettingItem>
        {/* 对话语言模型 */}
        <SettingItem label={t('settings.desktop.modelSelect')}>
          <div className="flex flex-col items-end gap-2">
            <Select
              options={modelOptions}
              value={assistantInfo?.llmModelId}
              onChange={(value) => setAssistantInfo({ ...assistantInfo, llmModelId: value })}
              className="w-full dark:bg-gray-800 dark:border-gray-700 dark:text-gray-100"
              minWidth="min-w-[120px]"
            />
          </div>
        </SettingItem>
        {/* 角色记忆 */}
        {/* <SettingItem label={t('settings.desktop.characterMemory')}>
          <div className="flex flex-end gap-1">
            <textarea
              value={characterMemory}
              onChange={(e) => setCharacterMemory(e.target.value)}
              rows={3}
              style={{ width: '380px' }}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md text-sm dark:bg-gray-800 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
            />
          </div>
        </SettingItem> */}

        {/* 主人识别 */}
        {/* <SettingItem label={t('settings.desktop.mainPersonality')}>
          <div className="flex justify-end flex-wrap gap-2">
            {mainPersonalityOptions.map((option: { value: string; label: string }, idx: number) => (
              <span
                key={option.value}
                className="flex items-center px-3 py-1 text-sm border border-gray-300 dark:border-gray-700 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 dark:text-gray-300"
              >
                {option.label}
                <button
                  className="ml-1 text-gray-400 hover:text-red-500"
                  onClick={() => handleRemovePersonality(idx)}
                  aria-label="删除"
                  type="button"
                >
                  <X className="w-3 h-3" />
                </button>
              </span>
            ))}
            <button className="flex items-center gap-1 px-3 py-1 text-sm text-blue-600 dark:text-blue-400 border border-blue-300 dark:border-blue-600 rounded-full hover:bg-blue-50 dark:hover:bg-blue-900/20">
              <Plus className="w-3 h-3" />
              {t('settings.desktop.addMainPersonality')}
            </button>
          </div>
        </SettingItem> */}

        {/* 保存按钮 */}
        <div className='flex justify-center pt-6'>
          <Button 
            className="bg-blue-600 hover:bg-blue-700 text-white font-medium"
            onClick={handleSave}
            disabled={!assistantInfo?.id}
          >
            {t('settings.desktop.saveCharacterInfo')}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CharacterSettings; 