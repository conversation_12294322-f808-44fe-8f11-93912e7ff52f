import React, { useState, useEffect } from 'react';
import { CheckCircle, Smartphone, Crown } from 'lucide-react';
import { <PERSON>lider } from 'antd';
import { useTranslation } from 'react-i18next';
import { ClipboardWindowStyle } from '../../../../shared/types/';
import SettingItem from '../../../components/SettingItem';
// 导入基础UI组件
import { HotkeyButton } from '../../../components/ui';

interface ClipboardSettingsProps {
  settings: any;
  onUpdate: (settings: any) => void;
}

const ClipboardSettings: React.FC<ClipboardSettingsProps> = ({ settings, onUpdate }) => {
  const { t } = useTranslation();
  const [clipboardWindowStyle, setWindowStyle] = useState<ClipboardWindowStyle>('center-window');
  const [maxHistoryCount, setMaxHistoryCount] = useState(200);

  // 从props初始化设置
  useEffect(() => {
    if (settings?.clipboard) {
      setWindowStyle(settings.clipboard.clipboardWindowStyle || 'bottom-bar');
      setMaxHistoryCount(settings.clipboard.maxHistoryCount || 100);
    }
  }, [settings]);

  // 保存设置到主进程
  const saveClipboardSettings = (updates: Partial<{clipboardWindowStyle: ClipboardWindowStyle, maxHistoryCount: number, hotkey: string}>) => {
    onUpdate({ clipboard: updates });
  };

  const handleWindowStyleChange = (style: ClipboardWindowStyle) => {
    setWindowStyle(style);
    saveClipboardSettings({ clipboardWindowStyle: style });
  };

  const handleHistoryCountChange = (count: number) => {
    setMaxHistoryCount(count);
    saveClipboardSettings({ maxHistoryCount: count });
  };

  return (
    <div className="px-4 space-y-0 dark:bg-gray-900 dark:text-gray-100">
      {/* 唤起快捷键 */}
      <SettingItem label={`${t('settings.clipboard.activationHotkey')}：`}>
        <HotkeyButton
          shortcutId="clipboard-hotkey"
          value={settings?.clipboard?.hotkey || '⌘ + V'}
          onChange={(newShortcut) => onUpdate({ clipboard: { hotkey: newShortcut } })}
        />
      </SettingItem>

      {/* 历史记录条数 */}
      <div className="py-4 border-b border-gray-100 dark:border-gray-800">
        <div className="flex items-center justify-between mb-4">
          <span className="text-sm font-medium text-gray-900 dark:text-gray-100">{t('settings.clipboard.historyCount')}：</span>
          <div className="flex items-center gap-3">
            <span className="text-sm text-gray-500 dark:text-gray-400">{t('settings.clipboard.memberUnlimited')}</span>
            <button className="flex items-center gap-1 px-3 py-1 bg-yellow-500 text-white text-xs rounded-full hover:bg-yellow-600 dark:bg-yellow-600 dark:hover:bg-yellow-700">
              <Crown className="w-3 h-3" />
              {t('settings.clipboard.freeTrial')}
            </button>
          </div>
        </div>
        
        <div className="px-4">
          <Slider
            min={100}
            max={300}
            step={50}
            value={maxHistoryCount}
            onChange={handleHistoryCountChange}
            marks={{
              100: '100',
              200: '200',
              300: '300'
            }}
            tooltip={{
              formatter: (value: number | undefined) => t('settings.clipboard.historyCountTooltip', { count: value })
            }}
          />
        </div>
      </div>

      {/* 显示位置 */}
      <div className="py-4 border-b border-gray-100 dark:border-gray-800">
        <div className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-4">{t('settings.clipboard.displayPosition')}：</div>
        
        <div className="flex gap-8">
          <div 
            className={`flex flex-col items-center gap-2 p-4 cursor-pointer rounded-lg border-2 transition-all ${
              clipboardWindowStyle === 'bottom-bar' 
                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-400' 
                : 'border-gray-300 hover:border-gray-400 dark:border-gray-600 dark:hover:border-gray-500'
            }`}
            onClick={() => handleWindowStyleChange('bottom-bar')}
          >
            <div className="w-16 h-12 bg-gray-400 dark:bg-gray-600 rounded-lg flex items-end justify-center p-1">
              <div className="w-10 h-2 bg-gray-600 dark:bg-gray-400 rounded"></div>
            </div>
            <span className="text-sm text-gray-900 dark:text-gray-100">{t('settings.clipboard.bottomBar')}</span>
          </div>

          <div 
            className={`flex flex-col items-center gap-2 p-4 cursor-pointer rounded-lg border-2 transition-all relative ${
              clipboardWindowStyle === 'center-window' 
                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-400' 
                : 'border-gray-300 hover:border-gray-400 dark:border-gray-600 dark:hover:border-gray-500'
            }`}
            onClick={() => handleWindowStyleChange('center-window')}
          >
            <div className="w-16 h-12 bg-gradient-to-br from-orange-400 to-orange-600 rounded-lg flex flex-col overflow-hidden">
              <div className="h-3 bg-orange-500 flex items-center px-1">
                <div className="flex gap-0.5">
                  <div className="w-1 h-1 bg-white/60 rounded-full"></div>
                  <div className="w-1 h-1 bg-white/60 rounded-full"></div>
                  <div className="w-1 h-1 bg-white/60 rounded-full"></div>
                </div>
              </div>
              <div className="flex-1 bg-gradient-to-b from-orange-100 to-orange-200 p-1">
                <div className="w-full h-full bg-white/80 rounded-sm"></div>
              </div>
            </div>
            <span className="text-sm text-gray-900 dark:text-gray-100">{t('settings.clipboard.centerWindow')}</span>
            {clipboardWindowStyle === 'center-window' && (
              <CheckCircle className="w-4 h-4 text-blue-500 dark:text-blue-400 absolute top-1 right-1" />
            )}
          </div>
        </div>
      </div>

      {/* 手机同步 */}
      <div className="py-4">
        <div className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-4">{t('settings.clipboard.phoneSync')}：</div>
        
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 space-y-3 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-2 text-sm">
            <Smartphone className="w-4 h-4 text-gray-600 dark:text-gray-400" />
            <span className="text-blue-600 dark:text-blue-400 underline cursor-pointer hover:text-blue-800 dark:hover:text-blue-300">
              {t('settings.clipboard.linkPhoneSync')}
            </span>
            <span className="text-xs text-gray-500 dark:text-gray-400">{t('settings.clipboard.phoneSyncDemo')}</span>
          </div>
          
          <p className="text-xs text-gray-600 dark:text-gray-400 leading-relaxed">
            {t('settings.clipboard.phoneSyncDescription')}
          </p>
        </div>
      </div>
    </div>
  );
};

export default ClipboardSettings; 