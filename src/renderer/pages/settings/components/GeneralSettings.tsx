import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../../components/theme-provider';
import { Sun, Moon, Monitor, CheckCircle2 } from 'lucide-react';
import { toast } from '../../../components/ui/use-toast';
import { MainWindowStyle, DisplayScreenType, LanguageType } from '../../../../shared/types';
import SettingItem from '../../../components/SettingItem';
// 导入基础UI组件
import { Button, Switch, Select, HotkeyButton } from '../../../components/ui';
import { supportedLanguages, changeLanguage, getCurrentLanguage } from '../../../i18n';

interface GeneralSettingsProps {
  settings: any;
  onUpdate: (settings: any) => void;
}

const GeneralSettings: React.FC<GeneralSettingsProps> = ({ settings, onUpdate }) => {
  const { t } = useTranslation();
  const { theme, setTheme } = useTheme();
  // 记录用户是否手动更改过主题
  const [userHasManuallyChanged, setUserHasManuallyChanged] = useState(false);
  
  // 权限状态
  const [permissions, setPermissions] = useState({
    accessibilityAccess: false,
    screenRecording: false,
    fullDiskAccess: false
  });
  
  // 从 props 中获取当前设置
  const currentSettings = settings?.general || {};
  
  // 检查用户是否手动设置过主题
  useEffect(() => {
    const storedTheme = localStorage.getItem("aido-theme");
    // 如果localStorage中有值且不是'system'，说明已经手动设置过
    if (storedTheme && storedTheme !== 'system') {
      setUserHasManuallyChanged(true);
    }
  }, []);
  
  // 初始化权限状态（这里暂时硬编码，后续可以通过API获取）
  useEffect(() => {
    setPermissions({
      accessibilityAccess: false,
      screenRecording: false,
      fullDiskAccess: false
    });
  }, []);
  
  // 处理自动启动变更
  const handleAutoStartChange = async (checked: boolean) => {
    try {
      onUpdate({general: {launchAtLogin: checked}});
    } catch (error) {
      console.error('设置自动启动失败:', error);
    }
  };
  
  // 处理菜单栏图标变更
  const handleMenuBarChange = async (checked: boolean) => {
    try {
      onUpdate({general: {showTray: checked}});
    } catch (error) {
      console.error('设置托盘图标失败:', error);
    }
  };
  
  // 处理语言变更
  const handleLanguageChange = (value: string) => {
    try {
      changeLanguage(value);
      onUpdate({general: {language: value as LanguageType}});
    } catch (error) {
      console.error('设置语言失败:', error);
    }
  };
  
  // 处理窗口显示屏幕变更
  const handleWindowScreenChange = async (value: string) => {
    try {
      onUpdate({general: {displayScreen: value as DisplayScreenType}});
    } catch (error) {
      console.error('设置窗口屏幕失败:', error);
    }
  };
  
  // 处理主题变更
  const handleThemeChange = (value: 'light' | 'dark' | 'system') => {
    try {
      // 如果当前是system模式且未手动改过，将转为light或dark取决于系统当前模式
      if (value === 'system' && userHasManuallyChanged) {
        // 直接设置为system
        setTheme('system');
      } else if (value !== 'system') {
        // 设置为light或dark
        setTheme(value);
        setUserHasManuallyChanged(true);
      } else {
        // 首次从system切换
        const isDarkMode = window.matchMedia("(prefers-color-scheme: dark)").matches;
        const newTheme = isDarkMode ? "light" : "dark";
        setTheme(newTheme);
        setUserHasManuallyChanged(true);
      }
      
      // 同步到主进程settingsService，触发多窗口主题同步
      onUpdate({general: {theme: value}});
    } catch (error) {
      console.error('设置主题失败:', error);
    }
  };
  
  // 处理权限请求
  const handleRequestPermission = async (permission: string) => {
    try {
      // 需要实现权限请求功能
      const granted = false; // 示例，需要实际实现
      
      if (granted) {
        setPermissions(prev => ({
          ...prev,
          [permission === 'accessibility' ? 'accessibilityAccess' : 
           permission === 'screen' ? 'screenRecording' : 'fullDiskAccess']: true
        }));
      }
    } catch (error) {
      console.error('请求权限失败:', error);
    }
  };
  
  // 处理窗口样式变更
  const handleWindowStyleChange = async (value: string) => {
    try {
      onUpdate({general: {mainWindowStyle: value as MainWindowStyle}});
    } catch (error) {
      console.error('设置窗口样式失败:', error);
    }
  };

  // 准备选项数据
  const languageOptions = supportedLanguages.map(lang => ({
    value: lang.code,
    label: lang.name
  }));

  const screenOptions = [
    { value: 'mouse', label: t('settings.general.mouseScreen') },
    { value: 'main', label: t('settings.general.mainScreen') }
  ];

  const windowStyleOptions = [
    { value: 'complete', label: t('settings.general.complete') },
    { value: 'simple', label: t('settings.general.simple') },
    { value: 'left-bar', label: t('settings.general.leftBar') },
    { value: 'right-bar', label: t('settings.general.rightBar') }
  ];

  // 权限项组件
  const PermissionItem: React.FC<{
    label: string;
    granted: boolean;
    onRequest: () => void;
  }> = ({ label, granted, onRequest }) => (
    <div className="flex items-center justify-between text-xs">
      <span className="text-gray-600 dark:text-gray-400">{label}</span>
      {granted ? (
        <div className="flex items-center text-green-600 dark:text-green-500">
          <CheckCircle2 className="w-3 h-3 mr-1" />
          {t('settings.general.authorized')}
        </div>
      ) : (
        <Button 
          variant="outline" 
          size="sm" 
          className="text-xs h-6 px-2 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
          onClick={onRequest}
        >
          {t('settings.general.authorize')}
        </Button>
      )}
    </div>
  );

  return (
    <div className="px-4 space-y-0 dark:bg-gray-900 dark:text-gray-100">
      {/* 开机时自动运行 */}
      <SettingItem label={t('settings.general.autoStart')}>
        <Switch 
          checked={currentSettings.launchAtLogin || false}
          onChange={handleAutoStartChange}
        />
      </SettingItem>

      {/* 显示系统托盘图标 */}
      <SettingItem label={t('settings.general.showTray')}>
        <Switch 
          checked={currentSettings.showTray || false}
          onChange={handleMenuBarChange}
        />
      </SettingItem>

      {/* 唤起快捷键 */}
      <SettingItem label={`${t('settings.general.activationHotkey')}：`}>
        <HotkeyButton
          shortcutId="mainWindow-hotkey"
          value={currentSettings.hotkey || 'Option + Space'}
          onChange={(newShortcut) => onUpdate({ general: { hotkey: newShortcut } })}
        />
      </SettingItem>

      {/* 语言 */}
      <SettingItem label={t('settings.general.interfaceLanguage')}>
        <Select 
          options={languageOptions}
          value={currentSettings.language || 'zh'}
          onChange={handleLanguageChange}
          minWidth="min-w-[120px]"
        />
      </SettingItem>

      {/* 窗口出现屏幕 */}
      <SettingItem label={t('settings.general.windowScreen')}>
        <Select 
          options={screenOptions}
          value={currentSettings.displayScreen || 'mouse'}
          onChange={handleWindowScreenChange}
          minWidth="min-w-[150px]"
        />
      </SettingItem>

      {/* 外观主题 */}
      <SettingItem label={t('settings.general.appearance')}>
        <div className="flex space-x-2">
          <button
            type="button"
            className={`flex items-center px-3 py-1.5 text-xs font-medium rounded-md border ${
              theme === 'light'
                ? 'bg-blue-100 text-blue-700 border-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:border-blue-800'
                : 'bg-gray-100 text-gray-600 border-gray-200 hover:bg-gray-150 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-700 dark:hover:bg-gray-700'
            }`}
            onClick={() => handleThemeChange('light')}
          >
            <Sun className="w-3 h-3 mr-1" />
            {t('settings.appearance.light')}
          </button>
          <button
            type="button"
            className={`flex items-center px-3 py-1.5 text-xs font-medium rounded-md border ${
              theme === 'dark'
                ? 'bg-blue-100 text-blue-700 border-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:border-blue-800'
                : 'bg-gray-100 text-gray-600 border-gray-200 hover:bg-gray-150 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-700 dark:hover:bg-gray-700'
            }`}
            onClick={() => handleThemeChange('dark')}
          >
            <Moon className="w-3 h-3 mr-1" />
            {t('settings.appearance.dark')}
          </button>
          <button
            type="button"
            className={`flex items-center px-3 py-1.5 text-xs font-medium rounded-md border ${
              theme === 'system'
                ? 'bg-blue-100 text-blue-700 border-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:border-blue-800'
                : 'bg-gray-100 text-gray-600 border-gray-200 hover:bg-gray-150 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-700 dark:hover:bg-gray-700'
            }`}
            onClick={() => handleThemeChange('system')}
          >
            <Monitor className="w-3 h-3 mr-1" />
            {t('settings.appearance.system')}
          </button>
        </div>
      </SettingItem>

      {/* 系统权限 */}
      <div className="py-4 border-b border-gray-100 dark:border-gray-800">
        <div className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">{t('settings.general.permissions')}</div>
        <div className="space-y-1">
          <PermissionItem
            label={t('settings.general.accessibilityAccess')}
            granted={permissions.accessibilityAccess}
            onRequest={() => handleRequestPermission('accessibility')}
          />
          <PermissionItem
            label={t('settings.general.screenRecording')}
            granted={permissions.screenRecording}
            onRequest={() => handleRequestPermission('screen')}
          />
          <PermissionItem
            label={t('settings.general.fullDiskAccess')}
            granted={permissions.fullDiskAccess}
            onRequest={() => handleRequestPermission('fullDisk')}
          />
        </div>
      </div>

      {/* 窗口样式 */}
      <SettingItem label={t('settings.general.windowStyle')} noBorder>
        <Select 
          options={windowStyleOptions}
          value={currentSettings.mainWindowStyle || 'complete'}
          onChange={handleWindowStyleChange}
          minWidth="min-w-[120px]"
        />
      </SettingItem>
    </div>
  );
};

export default GeneralSettings; 