import React from 'react';
import { useTranslation } from 'react-i18next';

interface CloudSettingsProps {
  settings: any;
  onUpdate: (settings: any) => void;
}

const CloudSettings: React.FC<CloudSettingsProps> = ({ settings, onUpdate }) => {
  const { t } = useTranslation();

  return (
    <div className="p-4 space-y-4">
      <h2 className="text-xl font-semibold">{t('cloud.title')}</h2>
      {/* 云设置内容 */}
      <p className="text-gray-600">云设置页面</p>
    </div>
  );
};

export default CloudSettings; 