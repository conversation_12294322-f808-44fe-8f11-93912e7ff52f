import React from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '../../../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Crown, Star, Zap, Users, Cloud, Bot, Palette, Monitor, Smartphone, X } from 'lucide-react';

interface VipComponetsProps {
  settings: any;
  onUpdate: (settings: any) => void;
}

const VipComponets: React.FC<VipComponetsProps> = () => {
  const { t } = useTranslation();

  const freeFeatures = [
    { icon: <Bot className="w-4 h-4" />, text: '所有基础功能', level: 'basic' },
    { icon: <Bot className="w-4 h-4" />, text: '每日赠送30体力 (不累加)', level: 'basic' },
    { icon: <Zap className="w-4 h-4" />, text: 'AI对话消耗体力 (不同模型消耗不同)', level: 'basic' },
    { icon: <Monitor className="w-4 h-4" />, text: '300条剪贴板历史', level: 'basic' },
    { icon: <Star className="w-4 h-4" />, text: '30条快速片段 (Snippet)', level: 'basic' },
    { icon: <Users className="w-4 h-4" />, text: '翻译消耗体力 (200字符1体力)', level: 'basic' },
    { icon: <Palette className="w-4 h-4" />, text: '悬浮球桌面助手', level: 'basic' },
    // { icon: <Cloud className="w-4 h-4" />, text: '多电脑云同步', level: 'none' },
    // { icon: <Crown className="w-4 h-4" />, text: 'AI可控制多台电脑执行任务', level: 'none' },
  ];

  const proFeatures = [
    { icon: <Bot className="w-4 h-4" />, text: '所有免费版功能', level: 'pro' },
    { icon: <Zap className="w-4 h-4" />, text: '每日赠送3000体力', level: 'pro' },
    { icon: <Zap className="w-4 h-4" />, text: '无限普通AI对话 (不消耗体力)', level: 'pro' },
    { icon: <Monitor className="w-4 h-4" />, text: '无限剪贴板历史', level: 'pro' },
    { icon: <Star className="w-4 h-4" />, text: '无限快速片段 (Snippet)', level: 'pro' },
    { icon: <Users className="w-4 h-4" />, text: '无限翻译 (不消耗体力)', level: 'pro' },
    { icon: <Palette className="w-4 h-4" />, text: '动态桌面助手/无限换肤', level: 'pro' },
    { icon: <Cloud className="w-4 h-4" />, text: '多电脑云同步', level: 'pro' },
    { icon: <Crown className="w-4 h-4" />, text: 'AI可控制多台电脑执行任务', level: 'pro' },
  ];

  return (
    <div className="w-full space-y-8">
      {/* 版本对比卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
      {/* 免费版 */}
      <Card className="relative overflow-hidden border-2 border-gray-200/60 dark:border-gray-700/60 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm hover:shadow-lg transition-all duration-300">
        {/* 装饰性背景 */}
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-gray-100/50 to-gray-200/50 dark:from-gray-700/50 dark:to-gray-800/50 rounded-full -translate-y-16 translate-x-16"></div>
        
        <CardHeader className="relative pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white">免费版</CardTitle>
            {/* <div className="text-3xl font-bold text-gray-900 dark:text-white"></div> */}
          </div>
        </CardHeader>
        
        <CardContent className="space-y-6">
          <div className="space-y-3">
            {freeFeatures.map((feature, index) => (
              <div key={index} className="flex items-center gap-2">
                <div className={`flex items-center justify-center w-4 h-4 rounded-full ${
                  feature.level === 'basic' 
                    ? 'bg-blue-100 dark:bg-blue-900/30' 
                    : feature.level === 'pro'
                    ? 'bg-purple-100 dark:bg-purple-900/30'
                    : 'bg-gray-100 dark:bg-gray-700'
                }`}>
                  {feature.level === 'basic' ? (
                    <div className="w-1.5 h-1.5 rounded-full bg-blue-500 dark:bg-blue-400"></div>
                  ) : feature.level === 'pro' ? (
                    <div className="w-1.5 h-1.5 rounded-full bg-purple-500 dark:bg-purple-400"></div>
                  ) : (
                    <X className="w-2.5 h-2.5 text-gray-400" />
                  )}
                </div>
                <div className="flex items-center gap-2 flex-1">
                  <span className={`text-sm ${
                    feature.level === 'none'
                      ? 'text-gray-400 dark:text-gray-500 line-through' 
                      : 'text-gray-700 dark:text-gray-300'
                  }`}>
                    {feature.text}
                  </span>
                </div>
              </div>
            ))}
          </div>
          
          {/* <div className="pt-4">
            <Button 
              className="w-full h-12 bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
            >
              当前版本
            </Button>
          </div> */}
        </CardContent>
      </Card>

      {/* 专业版 */}
      <Card className="relative overflow-hidden border-2 border-gradient-to-r from-blue-500 via-purple-500 to-pink-500 bg-gradient-to-br from-white via-blue-50 to-purple-50 dark:from-gray-900 dark:via-blue-900/20 dark:to-purple-900/20 backdrop-blur-sm hover:shadow-xl hover:shadow-blue-500/20 dark:hover:shadow-purple-500/20">
        {/* 专业版角标 */}
        <div className="absolute top-0 right-0 z-10">
          <div className="relative">
            <div className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-xs font-bold px-3 py-1.5 rounded-sm">
              <div className="flex items-center gap-1">
                {/* <Crown className="w-3 h-3" /> */}
                Pro
              </div>
            </div>
            <div className="absolute inset-0 bg-gradient-to-r from-yellow-400 to-orange-500 blur-sm opacity-30"></div>
          </div>
        </div>
        
        {/* 装饰性背景元素 */}
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-full -translate-y-16 translate-x-16"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-purple-400/10 to-pink-400/10 rounded-full translate-y-12 -translate-x-12"></div>
        
        <CardHeader className="relative pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Crown className="w-6 h-6 text-yellow-500" />
              <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white">专业版</CardTitle>
            </div>
            {/* <div className="text-right">
              <span className="text-3xl font-bold text-gray-900 dark:text-white">¥99</span>
              <span className="text-sm text-gray-500 dark:text-gray-400">/年</span>
            </div> */}
          </div>
        </CardHeader>
        
        <CardContent className="space-y-6">
          <div className="space-y-3">
            {proFeatures.map((feature, index) => (
              <div key={index} className="flex items-center gap-2">
                <div className="flex items-center justify-center w-4 h-4 rounded-full bg-purple-100 dark:bg-purple-900/30">
                  <div className="w-1.5 h-1.5 rounded-full bg-purple-500 dark:bg-purple-400"></div>
                </div>
                <div className="flex items-center gap-2 flex-1">
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    {feature.text}
                  </span>
                </div>
              </div>
            ))}
          </div>
{/*             
          <div className="pt-4">
            <Button 
              className="w-full h-12 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 hover:from-blue-600 hover:via-purple-600 hover:to-pink-600 text-white font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
            >
              立即升级
            </Button>
          </div> */}
        </CardContent>
      </Card>
    </div>
  </div>);
};

export default VipComponets; 