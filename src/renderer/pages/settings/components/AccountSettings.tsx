import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuthStore } from '../../../stores/authStore';
import { Button } from '../../../components/ui/button';
import { Avatar } from 'antd';
import { Crown, Edit2, Check, X, Monitor, Smartphone, Laptop, LogOut, User, Mail, Calendar, CrownIcon } from 'lucide-react';
import deviceApi from '../../../api/device';
import { useToast } from '../../../components/ui/use-toast';
import VipComponets from './VipComponets';
import membershipApi from '../../../api/membership';

const AccountSettings: React.FC = () => {
  const { t } = useTranslation();
  const { user, isLoggedIn, openLoginDialog, logout } = useAuthStore();
  const [devices, setDevices] = useState<any[]>([]);
  const [editingDeviceId, setEditingDeviceId] = useState<string | null>(null);
  const [editingDeviceName, setEditingDeviceName] = useState<string>('');
  const [vipStatus, setVipStatus] = useState<any>(null);
  const { toast } = useToast();
  useEffect(() => {
    if (isLoggedIn) {
      deviceApi.myDevices().then((res) => {
        setDevices(res.data.list);
      });
    }
  }, [isLoggedIn]);
  useEffect(() => {
    if (isLoggedIn && user.vip_level > 0) {
      membershipApi.vipStatus().then((res) => {
        setVipStatus(res.data);
      });
    }
  }, [isLoggedIn]);
  const handleEditClick = (device: any) => {
    setEditingDeviceId(device.deviceId);
    setEditingDeviceName(device.deviceName || '');
  };

  const handleSaveEdit = async (deviceId: string) => {
    try {
      deviceApi.updateDeviceName({ deviceName: editingDeviceName, deviceId }).then((res: any) => {
        if (res.code === 0) {
          toast({
            title: t('settings.account.updateDeviceNameSuccess'),
            variant: 'success'
          });
        }
      });
      setDevices(devices.map(device =>
        device.deviceId === deviceId
          ? { ...device, deviceName: editingDeviceName }
          : device
      ));

      setEditingDeviceId(null);
      setEditingDeviceName('');
    } catch (error) {
      console.error(t('settings.account.updateDeviceNameFailed'), error);
    }
  };

  const handleCancelEdit = () => {
    setEditingDeviceId(null);
    setEditingDeviceName('');
  };

  const handleDeviceLogout = (deviceId: string) => {
    deviceApi.deviceLogout({ deviceId }).then((res: any) => {
      if (res.code === 0) {
        toast({
          title: t('settings.account.logoutSuccess'),
          variant: 'success'
        });
        setDevices(devices.filter(device => device.deviceId !== deviceId));
      }
    });
  };

  const getDeviceIcon = (osName: string) => {
    switch (osName) {
      case 'darwin':
        return <Monitor className="w-5 h-5 text-blue-500" />;
      case 'win32':
        return <Laptop className="w-5 h-5 text-green-500" />;
      default:
        return <Monitor className="w-5 h-5 text-blue-500" />;
    }
  };

  const getOSDisplayName = (osName: string) => {
    switch (osName) {
      case 'darwin':
        return t('settings.account.macOS');
      case 'win32':
        return t('settings.account.windows');
      default:
        return t('settings.account.other');
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      {/* 用户信息卡片 */}
      <div className={`relative overflow-hidden rounded-2xl border border-gray-200/50 dark:border-gray-700/50 ${isLoggedIn
        ? 'bg-gradient-to-br from-white via-blue-50 to-purple-50 dark:from-gray-900 dark:via-blue-900/20 dark:to-purple-900/20'
        : 'bg-white dark:bg-gray-800'
        }`}>
        {/* 装饰性背景元素 */}
        {isLoggedIn && user.vip_level > 0 && (
          <>
            <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-full -translate-y-16 translate-x-16"></div>
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-purple-400/10 to-pink-400/10 rounded-full translate-y-12 -translate-x-12"></div>
          </>
        )}

        <div className="relative p-4 pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-6">
              <div className="relative">
                <div className="relative">
                  <Avatar
                    size={64}
                    src={user?.headerImg}
                    className="border-4 border-white/80 dark:border-gray-800/80 shadow-lg"
                  />
                  {/* 专业版角标 - 定位到头像左上角 */}
                  {isLoggedIn && user.vip_level > 0 && (
                    <div className="absolute -top-3 -left-3">
                      <Crown className="w-8 h-8 text-yellow-500 fill-current transform -rotate-45" />
                    </div>
                  )}
                </div>
              </div>
              <div className="space-y-2">
                {isLoggedIn ? (
                  <>
                    <div className="flex items-center gap-2">
                      {/* <User className="w-4 h-4 text-gray-400" /> */}
                      <div className="font-bold text-xl text-gray-900 dark:text-white">
                        {user?.userName || t('settings.account.unknownUser')}
                      </div>
                    </div>

                  </>
                ) : (
                  <>
                    <div className="font-bold text-xl text-gray-900 dark:text-white">{t('settings.account.loginFirst')}</div>
                    <div className="text-gray-500 dark:text-gray-400 text-sm">{t('settings.account.loginFirstDescription')}</div>
                  </>
                )}
              </div>
            </div>

            {/* 会员信息/登录按钮 */}
            <div className="flex flex-col items-end gap-3">
              {isLoggedIn ? (
                user.vip_level > 0 ? <>
                  <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400 text-sm">
                    {/* <Calendar className="w-4 h-4" /> */}
                    <span>{vipStatus?.currentMembership?.vipExpireAt && t('settings.account.expiresOn', { date: vipStatus?.currentMembership?.vipExpireAt }) || '到期日期'}</span>
                  </div>
                  <Button
                    size="sm"
                    className="bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white font-medium"
                  >
                    {t('settings.account.renewNow')}
                  </Button>
                </>
                :
                <Button
                  size="sm"
                  className="bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white font-medium"
                >
                  {t('settings.account.upgradeToPro')}
                </Button>
              ) : (
                <Button
                  size="sm"
                  className="bg-gray-800 hover:bg-gray-900 text-white font-medium"
                  onClick={openLoginDialog}
                >
                  {t('settings.account.loginNow')}
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* 我的设备 */}
      {
        isLoggedIn ?
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              {/* <div className="w-1 h-6 bg-gradient-to-b from-blue-500 to-purple-500 rounded-full"></div> */}
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">{t('settings.account.myDevices')}</h2>
            </div>

            <div className="space-y-4">
              {devices.map((device, idx) => (
                <div
                  key={device.deviceId}
                  className="group relative overflow-hidden rounded-xl border border-gray-200/60 dark:border-gray-700/60 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm hover:shadow-lg hover:shadow-blue-500/10 dark:hover:shadow-purple-500/10 transition-all duration-300 hover:border-blue-300 dark:hover:border-purple-300"
                >
                  {/* 设备状态指示器 */}
                  <div className={`absolute top-0 left-0 w-1 h-full ${device.isActive ? 'bg-gradient-to-b from-green-400 to-green-600' : 'bg-gray-300'
                    }`}></div>

                  <div className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 border border-gray-200 dark:border-gray-600">
                          {getDeviceIcon(device.osName)}
                        </div>

                        <div className="space-y-2">
                          {editingDeviceId === device.deviceId ? (
                            <div className="flex items-center gap-2">
                              <input
                                type="text"
                                value={editingDeviceName}
                                onChange={(e) => setEditingDeviceName(e.target.value)}
                                className="px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700"
                                autoFocus
                              />
                              <button
                                onClick={() => handleSaveEdit(device.deviceId)}
                                className="p-1.5 text-green-600 hover:text-green-700 hover:bg-green-50 dark:hover:bg-green-900/20 rounded-lg transition-colors"
                              >
                                <Check className="w-4 h-4" />
                              </button>
                              <button
                                onClick={handleCancelEdit}
                                className="p-1.5 text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                              >
                                <X className="w-4 h-4" />
                              </button>
                            </div>
                          ) : (
                            <div className="flex items-center gap-2">
                              <div className="font-semibold text-gray-900 dark:text-white truncate max-w-[200px]">
                                {device.deviceName || t('settings.account.unnamedDevice')}
                              </div>
                              <button
                                onClick={() => handleEditClick(device)}
                                className="p-1.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-all duration-200 opacity-0 group-hover:opacity-100"
                              >
                                <Edit2 className="w-4 h-4" />
                              </button>
                            </div>
                          )}
                          <div className="text-xs text-gray-500 dark:text-gray-400 font-mono truncate max-w-[200px]">
                            ID: {device.deviceId}
                          </div>
                          <div className="text-xs text-gray-400 dark:text-gray-500 truncate max-w-[200px]">
                            {getOSDisplayName(device.osName)}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-3">
                        {localStorage.getItem('deviceId') && localStorage.getItem('deviceId') === device.deviceId && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-md text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 border border-blue-200 dark:border-blue-700 whitespace-nowrap">
                            {t('settings.account.currentDevice')}
                          </span>
                        )}

                        <div className="flex items-center gap-2">
                          <div className={`w-2 h-2 rounded-full ${device.isActive ? 'bg-green-500 shadow-lg shadow-green-500/50' : 'bg-gray-400'
                            }`}></div>
                          <span className="text-xs text-gray-500 dark:text-gray-400 whitespace-nowrap">
                            {device.isActive ? t('settings.account.online') : t('settings.account.offline')}
                          </span>
                        </div>

                        {!(localStorage.getItem('deviceId') && localStorage.getItem('deviceId') === device.deviceId) && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDeviceLogout(device.deviceId)}
                          >
                            <LogOut className="w-3 h-3 mr-1" />
                            {t('settings.account.logout')}
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            {/* <div className="flex flex-col items-center justify-center p-12 rounded-xl border-2 border-dashed border-gray-300 dark:border-gray-600 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900">
            <div className="w-16 h-16 rounded-full bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/20 dark:to-purple-900/20 flex items-center justify-center mb-4">
              <Monitor className="w-8 h-8 text-gray-400" />
            </div>
            <div className="text-center space-y-2">
              <div className="text-gray-600 dark:text-gray-400 font-medium">请先登录查看设备信息</div>
              <div className="text-gray-500 dark:text-gray-500 text-sm">登录后可以管理您的所有设备</div>
            </div>
          </div> */}
          </div>
          :
          <div className='w-full'>
            <VipComponets settings={user} onUpdate={() => { }} />
          </div>
      }

      {/* 退出登录 */}
      {isLoggedIn && (
        <div className="flex justify-center pt-6">
          <Button
            variant="destructive"
            onClick={() => logout()}
          >
            <LogOut className="w-4 h-4 mr-2" />
            {t('settings.account.logoutAccount')}
          </Button>
        </div>
      )}
    </div>
  );
};

export default AccountSettings; 