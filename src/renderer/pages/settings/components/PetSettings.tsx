import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { <PERSON><PERSON>, Card, CardContent, CardHeader, CardTitle } from '../../../components/ui';
import { Plus, ShoppingCart, Info } from 'lucide-react';

interface PetSettingsProps {
  settings: any;
  onUpdate: (settings: any) => void;
}

interface PetData {
  id: string;
  name: string;
  voiceCharacter: string;
  lastConversation: string;
  serialNumber: string;
}

const PetSettings: React.FC<PetSettingsProps> = ({ settings, onUpdate }) => {
  const { t } = useTranslation();
  
  // 模拟宠物数据数组
  const [pets, setPets] = useState<PetData[]>([
    {
      id: '1',
      name: '哆啦A梦',
      voiceCharacter: '弯弯小河',
      lastConversation: '2小时3分钟前',
      serialNumber: '34a43sw'
    },
    {
      id: '2',
      name: '皮卡丘',
      voiceCharacter: '可爱小皮',
      lastConversation: '1小时前',
      serialNumber: 'pika123'
    },
    {
      id: '3',
      name: '小猫咪',
      voiceCharacter: '喵喵小咪',
      lastConversation: '30分钟前',
      serialNumber: 'cat456'
    }
  ]);

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* 顶部按钮区域 */}
      <div className="flex gap-4">
        <Button 
          className="flex items-center gap-2 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white font-medium"
        >
          <Plus className="w-4 h-4" />
          {t('settings.pet.addPet')}
        </Button>
        <Button 
          className="flex items-center gap-2 bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white font-medium"
        >
          <ShoppingCart className="w-4 h-4" />
          {t('settings.pet.purchase')}
        </Button>
      </div>

      {/* 宠物信息卡片 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {pets.map((pet) => (
          <Card key={pet.id} className="w-full bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-lg">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-base font-semibold text-gray-900 dark:text-white">
                  {pet.name}
                </CardTitle>
                <div className="flex items-center justify-center w-5 h-5">
                  <Info className="w-3 h-3 text-gray-400 dark:text-gray-500" />
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="text-xs text-gray-600 dark:text-gray-400">
                <span className="font-medium">{t('settings.pet.characterVoice')}: </span>
                {pet.voiceCharacter}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">
                <span className="font-medium">{t('settings.pet.lastConversation')}: </span>
                {pet.lastConversation}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">
                <span className="font-medium">{t('settings.pet.serialNumber')}: </span>
                {pet.serialNumber}
              </div>
              <div className="pt-1">
                <button className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium">
                  {t('settings.pet.modifySettings')} {'>'}
                </button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default PetSettings; 