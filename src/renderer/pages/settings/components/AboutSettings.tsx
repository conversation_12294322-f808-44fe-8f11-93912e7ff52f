import React from 'react';
import { useTranslation } from 'react-i18next';

interface AboutSettingsProps {
  settings: any;
  onUpdate: (settings: any) => void;
}

const AboutSettings: React.FC<AboutSettingsProps> = ({ settings, onUpdate }) => {
  const { t } = useTranslation();

  const handleCheckUpdate = () => {
    // TODO: 实现检查更新逻辑
    console.log('检查更新');
  };

  const handleSocialMediaClick = (platform: string) => {
    // TODO: 实现社交媒体链接跳转
    console.log(`打开${platform}`);
  };

  return (
    <div className="flex flex-col justify-between items-center p-6 space-y-6 max-w-md mx-auto h-full">
      {/* 应用图标 */}
      <div className="flex flex-col justify-center items-cente h-full">
        <div className="flex justify-center mb-5">
          <img
            src="../../assets/images/icon_64x64.png"
            alt="Aido"
            className="object-contain rounded-full w-16 h-16"
            draggable={false}
          />
        </div>

        {/* 应用名称 */}
        <div className="text-center mb-2">
          <h1 className="text-2xl font-bold text-gray-900">Aido</h1>
        </div>

        {/* 描述和版本信息 */}
        <div className="text-center space-y-2 mb-2">
          <p className="text-gray-600 text-sm">您的贴身AI电脑助手</p>
          <p className="text-gray-500 text-xs">版本: 1.0.0</p>
        </div>

        {/* 检查更新按钮 */}
        <div className="flex justify-center">
          <button
            onClick={handleCheckUpdate}
            className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm"
          >
            检查更新
          </button>
        </div>
      </div>

      {/* 社交媒体部分 */}
      <div className="text-center space-y-4 flex-grow-0">
        <p className="text-gray-600 text-xs">
          关注我们,了解您的AI电脑助手都能做些什么
        </p>
        
        {/* 社交媒体图标 */}
        <div className="flex justify-center space-x-6">
          <button
            onClick={() => handleSocialMediaClick('discord')}
            className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center hover:bg-blue-600 transition-colors"
          >
            <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515a.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0a12.64 12.64 0 0 0-.617-1.25a.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057a19.9 19.9 0 0 0 5.993 3.03a.078.078 0 0 0 .084-.028a14.09 14.09 0 0 0 1.226-1.994a.076.076 0 0 0-.041-.106a13.107 13.107 0 0 1-1.872-.892a.077.077 0 0 1-.008-.128a10.2 10.2 0 0 0 .372-.292a.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127a12.299 12.299 0 0 1-1.873.892a.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028a19.839 19.839 0 0 0 6.002-3.03a.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419c0-1.333.956-2.419 2.157-2.419c1.21 0 2.176 1.096 2.157 2.42c0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419c0-1.333.955-2.419 2.157-2.419c1.21 0 2.176 1.096 2.157 2.42c0 1.333-.946 2.418-2.157 2.418z"/>
            </svg>
          </button>

          <button
            onClick={() => handleSocialMediaClick('tiktok')}
            className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center hover:bg-blue-600 transition-colors"
          >
            <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
            </svg>
          </button>

          <button
            onClick={() => handleSocialMediaClick('camera')}
            className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center hover:bg-blue-600 transition-colors"
          >
            <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H19C20.11 23 21 22.11 21 21V9ZM19 21H5V3H13V9H19V21Z"/>
            </svg>
          </button>

          <button
            onClick={() => handleSocialMediaClick('weibo')}
            className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center hover:bg-blue-600 transition-colors"
          >
            <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M10.098 20.323c-3.977.391-7.414-1.406-7.672-4.02-.259-2.609 2.759-5.047 6.74-5.441 3.979-.394 7.413 1.404 7.671 4.018.259 2.6-2.759 5.049-6.739 5.443zm.01-10.601c-.338.034-.658.099-.96.193 1.804-.773 3.88-1.021 5.96-.721-.205-.906-.88-1.653-1.85-2.012-1.21-.451-2.551-.23-3.15.54zm-.01 8.601c-.338-.034-.658-.099-.96-.193-1.804.773-3.88 1.021-5.96.721.205.906.88 1.653 1.85 2.012 1.21.451 2.551.23 3.15-.54z"/>
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

export default AboutSettings; 