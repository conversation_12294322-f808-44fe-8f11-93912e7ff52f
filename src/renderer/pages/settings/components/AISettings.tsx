import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '../../../components/ui/button';
import { Switch } from '../../../components/ui/switch';
import { Input } from '../../../components/ui/input';
import SettingItem from '../../../components/SettingItem';
import { Folder, X, Plus } from 'lucide-react';
import { fileManagerClient } from '../../../services/api/file-manager';

interface AISettingsProps {
  settings: any;
  onUpdate: (settings: any) => void;
}

interface FolderPermission {
  id: string;
  path: string;
}

const AISettings: React.FC<AISettingsProps> = ({ settings, onUpdate }) => {
  const { t } = useTranslation();
  
  // 从props获取当前设置，如果没有则使用默认值
  const currentSettings = settings?.ai || {};
  
  // 文件夹权限列表
  const [folderPermissions, setFolderPermissions] = useState<FolderPermission[]>(
    currentSettings.allowedFolders || [
      { id: '1', path: '/User/maliang/Desktop' },
      { id: '2', path: '/User/maliang/Document' }
    ]
  );
  
  // AI确认开关状态
  const [toolConfirmation, setToolConfirmation] = useState(
    currentSettings.toolConfirmation !== false
  );
  const [installConfirmation, setInstallConfirmation] = useState(
    currentSettings.installConfirmation !== false
  );

  // 处理文件夹权限变化
  const handleFolderChange = (id: string, newPath: string) => {
    const updatedFolders = folderPermissions.map(folder =>
      folder.id === id ? { ...folder, path: newPath } : folder
    );
    setFolderPermissions(updatedFolders);
    onUpdate({ ai: { ...currentSettings, allowedFolders: updatedFolders } });
  };

  // 添加新文件夹
  const handleAddFolder = () => {
    const newFolder: FolderPermission = {
      id: Date.now().toString(),
      path: ''
    };
    const updatedFolders = [...folderPermissions, newFolder];
    setFolderPermissions(updatedFolders);
    onUpdate({ ai: { ...currentSettings, allowedFolders: updatedFolders } });
  };

  // 删除文件夹
  const handleRemoveFolder = (id: string) => {
    const updatedFolders = folderPermissions.filter(folder => folder.id !== id);
    setFolderPermissions(updatedFolders);
    onUpdate({ ai: { ...currentSettings, allowedFolders: updatedFolders } });
  };

  // 处理工具确认开关
  const handleToolConfirmationChange = (checked: boolean) => {
    setToolConfirmation(checked);
    onUpdate({ ai: { ...currentSettings, toolConfirmation: checked } });
  };

  // 处理安装确认开关
  const handleInstallConfirmationChange = (checked: boolean) => {
    setInstallConfirmation(checked);
    onUpdate({ ai: { ...currentSettings, installConfirmation: checked } });
  };

  // 选择文件夹
  const handleSelectFolder = async (id: string) => {
    try {
      console.log('开始选择文件夹...');
      console.log('window.electron:', window.electron);
      console.log('window.electron.files:', window.electron?.files);
      console.log('window.electron.files.selectFolder:', window.electron?.files?.selectFolder);
      
      if (!window.electron?.files?.selectFolder) {
        console.error('selectFolder 方法不存在');
        return;
      }
      
      const selectedPath = await fileManagerClient.selectFolder();
      if (selectedPath) {
        handleFolderChange(id, selectedPath);
      }
    } catch (error) {
      console.error('选择文件夹失败:', error);
    }
  };

  return (
    <div className="px-4 space-y-0 dark:bg-gray-900 dark:text-gray-100">
      {/* 文件夹权限设置 */}
      <div className="py-4 border-b border-gray-100 dark:border-gray-800">
        
        <div className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-4">
          {t('ai.allowedFolders')}
        </div>
        
        <div className="space-y-3">
          {folderPermissions.map((folder) => (
            <div key={folder.id} className="flex items-center gap-3">
              <div className="flex-1">
                <Input
                  value={folder.path}
                  onChange={(e) => handleFolderChange(folder.id, e.target.value)}
                  placeholder={t('ai.folderPathPlaceholder')}
                  className="dark:bg-gray-800 dark:border-gray-700 dark:text-gray-100"
                />
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleSelectFolder(folder.id)}
                className="flex items-center gap-1"
              >
                <Folder className="w-3 h-3" />
                {t('ai.select')}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleRemoveFolder(folder.id)}
                className="text-gray-500 hover:text-red-500 dark:text-gray-400 dark:hover:text-red-400"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          ))}
        </div>
        
        <div className="mt-4">
          <Button
            variant="outline"
            size="sm"
            onClick={handleAddFolder}
            className="flex items-center gap-1"
          >
            <Plus className="w-3 h-3" />
            {t('ai.addDirectory')}
          </Button>
        </div>
      </div>

      {/* AI确认设置 */}
      <div className="py-4 border-b border-gray-100 dark:border-gray-800">
        <div className="space-y-4">
          <SettingItem label={t('ai.toolConfirmation')}>
            <Switch
              checked={toolConfirmation}
              onChange={handleToolConfirmationChange}
            />
          </SettingItem>
          
          <SettingItem label={t('ai.installConfirmation')} noBorder>
            <Switch
              checked={installConfirmation}
              onChange={handleInstallConfirmationChange}
            />
          </SettingItem>
        </div>
      </div>
    </div>
  );
};

export default AISettings; 