import React, { FC, useEffect, useLayoutEffect, useRef, useState } from 'react'

export type CurrentType = {
  modelPath: string
  width: number
  height: number
}

const parseModelPath = (p: string) => {
  const paths = p.split('/')
  paths.pop()

  const modelName = paths.pop()
  const basePath = paths.join('/')

  return {
    basePath,
    modelName,
  }
}

const Current: FC<CurrentType> = ({ modelPath, width, height }) => {
  const live2dRef = useRef<HTMLDivElement>(null)
  const viewerInstanceRef = useRef<any>(null)
  const [isReady, setIsReady] = useState(false)
  
  // 清理函数
  const cleanupViewer = () => {
    if (viewerInstanceRef.current) {
      try {
        // 尝试调用销毁方法（如果存在）
        if (typeof viewerInstanceRef.current.destroy === 'function') {
          viewerInstanceRef.current.destroy()
        }
        // 清除引用
        viewerInstanceRef.current = null
        
        // 清空容器
        if (live2dRef.current) {
          live2dRef.current.innerHTML = ''
        }
      } catch (error) {
        console.error('清理l2dViewer实例失败:', error)
      }
    }
  }
  
  // 确保宽高有效后再初始化
  useEffect(() => {
    if (width <= 0 || height <= 0) return
    
    // 延迟初始化，确保DOM已经渲染完毕
    const timer = setTimeout(() => {
      setIsReady(true)
    }, 500)
    
    return () => clearTimeout(timer)
  }, [width, height])
  
  useEffect(() => {
    if (!isReady) return
    
    const { basePath, modelName } = parseModelPath(modelPath)
    console.log('初始化模型:', basePath, modelName, width, height)
    
    // 先清理旧实例
    cleanupViewer()
    
    try {
      // 创建新实例
      viewerInstanceRef.current = new (window as any).l2dViewer({
        el: live2dRef.current,
        basePath,
        modelName,
        width,
        height,
        autoMotion: true,
      })
    } catch (error) {
      console.error('初始化Live2D模型失败:', error)
    }
    
    // 组件卸载时清理
    return cleanupViewer
  }, [modelPath, width, height, isReady])

  const onMouseEnter = () => {
    console.log('onMouseEnter')
    window.electron.window.setIgnoreMouseEvents(true)
  }

  const onMouseLeave = () => {
    console.log('onMouseLeave')
    window.electron.window.setIgnoreMouseEvents(false)
  }

  const onDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    console.log('onDragEnter')
    // window.electron.window.setIgnoreMouseEvents(true)
  }

  const onDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    console.log('onDragLeave')
    // window.electron.window.setIgnoreMouseEvents(false)
  }

  const onDrop = (e: React.DragEvent<HTMLDivElement>) => {
    console.log('onDrop')
    // window.electron.window.setIgnoreMouseEvents(false)
  }

  return (
    <div
      className="live2d"
      ref={live2dRef}
      key={`${modelPath}-${width}-${height}`}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      onDragEnter={onDragEnter}
      onDragLeave={onDragLeave}
      onDrop={onDrop}
    ></div>
  )
}

export default React.memo(Current)
