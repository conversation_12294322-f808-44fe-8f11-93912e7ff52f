import React, { FC } from 'react'
import styled from 'styled-components'
import { useConfigStore } from '../../stores/ConfigStore'
import { useWinStore } from '../../stores/WinStore'
import { TipsType } from './Tips'
import { thirdPartyApi } from '../../api/thirdParty'

const Wrapper = styled.div`
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 50px;
  color: #aaa;
  opacity: 0;
  position: absolute;
  right: 0px;
  top: 50%;
  transform: translateY(-50%);
  transition: opacity 1s;

  & span {
    color: #6a6a6a;
    display: block;
    line-height: 30px;
    text-align: center;
    transition: color 0.3s;
    cursor: pointer;

    &:hover {
      color: #fa0;
      opacity: 1;
    }
  }

  &:hover {
    opacity: 1;
  }
`

const MoveIcon = styled.span`
  -webkit-app-region: drag;
`

const Toolbar: FC<{
  onShowMessage: (tips: TipsType) => void
}> = ({ onShowMessage }) => {
  const modelPath = useConfigStore((state) => state.config.modelPath)
  const nextModel = useConfigStore((state) => state.nextModel)
  const resizable = useWinStore((state) => state.win.resizable)
  const setResizable = useWinStore((state) => state.setResizable)
  
  const showMessage = (text: string, timeout: number, priority: number, title?: string) => {
    onShowMessage({ title, text, priority, timeout })
  }
  
  const showHitokoto = async () => {
    try {
      const response = await thirdPartyApi.hitokoto()
      const result = response.data

      showMessage(result.hitokoto, 6000, 10)

      const text = `这句一言来自 <span>「${result.from}」</span>，是 <span>${result.creator}</span> 在 hitokoto.cn 投稿的。`

      window.setTimeout(() => {
        showMessage(text, 6000, 10)
      }, 6000)
    } catch (error) {
      console.error('获取一言失败:', error)
      showMessage('获取一言失败', 3000, 10)
    }
  }
  
  const loadOtherModel = () => {
    nextModel()
  }
  
  const toggleResizable = () => {
    setResizable(!resizable)
  }
  
  const showInfo = () => {
    const text = `${modelPath}`
    showMessage(text, 8000, 11)
  }

  const toolList = [
    { name: 'comment', icon: 'comment', call: showHitokoto },
    {
      name: 'user',
      icon: 'user-circle',
      call: loadOtherModel,
    },
    // { name: 'camera', icon: 'camera-retro', call: capture },
    { name: 'square', icon: 'square-o', call: toggleResizable },
    { name: 'info', icon: 'info-circle', call: showInfo },
  ]

  return (
    <Wrapper>
      {toolList.map((item) => {
        const { name, icon, call } = item
        return (
          <span
            onClick={() => {
              call()
            }}
            key={name}
            className={`fa fa-lg fa-${icon}`}
          ></span>
        )
      })}
      <MoveIcon className="fa fa-lg fa-arrows"></MoveIcon>
    </Wrapper>
  )
}

export default Toolbar
