import React, { FC, useEffect, useRef, useState } from 'react'

export type LegacyType = { modelPath: string; width: number; height: number }

const Legacy: FC<LegacyType> = ({ modelPath, height, width }) => {
  const canvasContainerRef = useRef<HTMLDivElement>(null)
  const [canvasId, setCanvasId] = useState(`live2d-${Date.now()}`)
  const [forceReload, setForceReload] = useState(0)
  
  // 当模型路径或尺寸变化时重置刷新状态
  useEffect(() => {
    setForceReload(0)
  }, [modelPath, width, height])
  
  // 每次模型或尺寸变化时，重新创建canvas
  useEffect(() => {
    // 确保尺寸有效
    if (width <= 0 || height <= 0) return
    
    // 生成新的canvas ID
    const newCanvasId = `live2d-${Date.now()}`
    setCanvasId(newCanvasId)
    
    // 延迟处理，确保DOM已更新
    const timer = setTimeout(() => {
      try {
        console.log('加载Legacy模型:', modelPath, width, height, newCanvasId)
        void (window as any).loadlive2d(newCanvasId, modelPath)
        
        // 只在首次加载时设置刷新，避免重复刷新
        if (forceReload === 0) {
            setForceReload(1)
        }
      } catch (error) {
        console.error('加载Legacy模型失败:', error)
      }
    }, 100)
    
    return () => clearTimeout(timer)
  }, [modelPath, width, height, forceReload])

  const onMouseEnter = () => {
    console.log('onMouseEnter')
    window.electron.window.setIgnoreMouseEvents(true)
  }

  const onMouseLeave = () => {
    console.log('onMouseLeave')
    window.electron.window.setIgnoreMouseEvents(false)
  }

  const onDragEnter = (e: React.DragEvent<HTMLCanvasElement>) => {
    console.log('onDragEnter')
    // window.electron.window.setIgnoreMouseEvents(true)
  }

  const onDragLeave = (e: React.DragEvent<HTMLCanvasElement>) => {
    console.log('onDragLeave')
    // window.electron.window.setIgnoreMouseEvents(false)
  }

  const onDrop = (e: React.DragEvent<HTMLCanvasElement>) => {
    console.log('onDrop')
    // window.electron.window.setIgnoreMouseEvents(false)
  }
  
  return (
    <div ref={canvasContainerRef} style={{ width, height }} key={`legacy-${forceReload}`}>
      <canvas
        id={canvasId}
        className="live2d"
        width={width}
        height={height}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
        onDragEnter={onDragEnter}
        onDragLeave={onDragLeave}
        onDrop={onDrop}
      ></canvas>
    </div>
  )
}

export default React.memo(Legacy)
