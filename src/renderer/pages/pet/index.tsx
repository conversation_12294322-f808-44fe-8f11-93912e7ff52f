import React, { useEffect, useLayoutEffect, useRef, useState } from 'react'
import styled from 'styled-components'

import { debounce } from '../../utils'
import LegacyRender from './Legacy'
import CurrentRender from './Current'
import Toolbar from './Toolbar'
import Tips, { TipsType } from './Tips'
import zhTips from './tips/zh.json'
import enTips from './tips/en.json'
import { useConfigStore } from '../../stores/ConfigStore'
import { useWinStore } from '../../stores/WinStore'
import { changeLanguage } from '../../i18n'
import 'font-awesome/css/font-awesome.css'
import { NotificationData } from '@shared/types/notification';
import { WindowType } from '../../../shared/ipc';
interface ITips {
  mouseover: Mouseover[]
  click: Mouseover[]
}

interface Season {
  date: string
  text: string
}

interface Mouseover {
  selector: string
  text: string[]
}

const Wrapper = styled.div<{ border: boolean }>`
  ${(props) => (props.border ? 'border: 2px dashed #ccc;' : 'padding: 2px;')}
  height: 100vh;
  width: 100vw;
  overflow: hidden;
`

const RenderWrapper = styled.div`
  margin-top: 20px;
`

const getCavSize = () => {
  const width = Math.max(window.innerWidth, 1)
  const height = Math.max(window.innerHeight - 20, 1)
  return { width, height }
}

const PetPage = () => {
  // 使用ConfigStore
  const modelPath = useConfigStore((state) => state.config.modelPath)
  const setModelList = useConfigStore((state) => state.setModelList)
  const setModelPath = useConfigStore((state) => state.setModelPath)
  const nextModel = useConfigStore((state) => state.nextModel)
  const prevModel = useConfigStore((state) => state.prevModel)
  
  // 使用WinStore
  const resizable = useWinStore((state) => state.win.resizable)
  const language = useWinStore((state) => state.win.language)
  const showTool = useWinStore((state) => state.win.showTool)
  const setSwitchTool = useWinStore((state) => state.setSwitchTool)
  const setLanguage = useWinStore((state) => state.setLanguage)
  const setResizable = useWinStore((state) => state.setResizable)

  const [tips, setTips] = useState<TipsType>({
    title: '',
    text: '',
    priority: -1,
    timeout: 0,
  })

  const [cavSize, setCavSize] =
    useState<{ width: number; height: number }>(getCavSize)

  useEffect(() => {
    const handleNotificationShow = (data: NotificationData) => {
      console.log('Pet window received notification:', data);
      setTips({
        title: data.title,
        text: data.detail,
        timeout: data.duration || 4000,
        priority: 10 // High priority for external notifications
      });
    };

    // 使用新的跨窗口通信服务监听通知事件
    const unsubscribe = window.electron.crossWindow.on('notification:show', handleNotificationShow);

    return () => {
      unsubscribe();
    };
  }, []);

  useEffect(() => {
    // 将全局方法绑定到window对象
    Object.assign(window, {
      setSwitchTool,
      setLanguage,
      nextModel,
      prevModel
    });
  }, [])

  // 监听语言变化事件
  useEffect(() => {
    const handleLanguageChange = (language: string) => {
      console.log(`宠物窗口收到语言变化事件: ${language}`);
      // 更新 i18n 语言
      changeLanguage(language);
      // 更新 WinStore 中的语言状态
      setLanguage(language as 'zh' | 'en');
    };

    // 监听来自主进程的语言变化事件
    const removeListener = window.electron.system.onLanguageChanged(handleLanguageChange);

    return () => {
      removeListener();
    };
  }, [setLanguage])

  useEffect(() => {
    const handleDragOver = (evt: DragEvent): void => {
      evt.preventDefault()
    }

    const handleDragEnter = (evt: DragEvent): void => {
      evt.preventDefault()
    }

    const handleDrop = async (evt: DragEvent) => {
      evt.preventDefault()

      //获取拖拽上来的文字，有可能是文本、url
      const text = evt.dataTransfer?.getData('text/plain');

      //获取拖拽上来的文件列表
      const files = evt.dataTransfer?.files

      console.log('text: ', text)
      console.log('files: ', files)
      if (files) {
        let pathList = Array.from(files).map((file) => file.path)
        const eventData = {
          filesPath: pathList
        };
        // 给主窗口发送emit事件
        window.electron.crossWindow.emit('pet:files-dropped', eventData, WindowType.MAIN);

        // 主动显示主窗口
        try {
          await window.electron.window.showWindow();
          console.log('桌宠拖入文件后已显示主窗口');
        } catch (error) {
          console.error('显示主窗口失败:', error);
        }

        // 使用新的跨窗口通信服务发送工具调用通知
        const notification: NotificationData = {
          id: Math.random().toString(), // 临时ID
          type: 'info',
          title: '提示',
          detail: `发现待处理文件`,
          duration: 3000
        };

        window.electron.crossWindow.emit('notification:show', notification, [
          WindowType.FLOATING_BALL,
          WindowType.PET
        ]);
      }

      //暂时注释掉加载live2d模型的逻辑，这个后续做到设置里
      // if(files){
      //   const paths = []
      //   for (let i = 0; i < files.length; i++) {
      //     const result = await window.electron.pet.getModels(files[i])
      //     paths.push(...result)
      //   }
  
      //   console.log('modelList: ', paths)
  
      //   if (paths.length > 0) {
      //     const models = paths.map((p) => `live2d://${p}`)
  
      //     setModelList(models)
      //     setModelPath(models[0])
      //   }
      // }
    }

    document.body.addEventListener('dragenter', handleDragEnter)
    document.body.addEventListener('dragover', handleDragOver)
    document.body.addEventListener('drop', handleDrop)

    return () => {
      document.body.removeEventListener('dragenter', handleDragEnter)
      document.body.removeEventListener('dragover', handleDragOver)
      document.body.removeEventListener('drop', handleDrop)
    }
  }, [])

  useLayoutEffect(() => {
    const resizeCanvas = debounce(() => {
      setCavSize(getCavSize())
    })

    window.addEventListener('resize', resizeCanvas, false)

    return () => {
      window.removeEventListener('resize', resizeCanvas)
    }
  }, [])

  useEffect(() => {
    const handleBlur = () => {
      if (resizable) {
        setResizable(false)
      }
    }

    window.addEventListener('blur', handleBlur)
    return () => {
      window.removeEventListener('blur', handleBlur)
    }
  }, [resizable])

  const isMoc3 = modelPath.endsWith('.model3.json')

  const Render = isMoc3 ? CurrentRender : LegacyRender

  const handleMessageChange = (nextTips: TipsType) => {
    setTips(nextTips)
  }

  const handleMouseOver: React.MouseEventHandler<HTMLDivElement> = (event) => {
    const tips = tipJSONs.mouseover.find((item) =>
      (event.target as any).matches(item.selector),
    )

    if (!tips) {
      return
    }

    let text = Array.isArray(tips.text)
      ? tips.text[Math.floor(Math.random() * tips.text.length)]
      : tips.text
    text = text.replace('{text}', (event.target as HTMLDivElement).innerText)
    handleMessageChange({
      title: '', // 鼠标悬停事件暂时不设置title
      text,
      timeout: 4000,
      priority: 8,
    })
  }

  const handleClick: React.MouseEventHandler<HTMLDivElement> = (event) => {
    const tips = tipJSONs.click.find((item) =>
      (event.target as any).matches(item.selector),
    )

    if (!tips) {
      return
    }

    let text = Array.isArray(tips.text)
      ? tips.text[Math.floor(Math.random() * tips.text.length)]
      : tips.text
    text = text.replace('{text}', (event.target as HTMLDivElement).innerText)

    handleMessageChange({
      title: '', // 点击事件暂时不设置title
      text,
      timeout: 4000,
      priority: 8,
    })
  }

  const tipJSONs = language === 'en' ? enTips : zhTips

  return (
    <Wrapper
      border={resizable}
      onMouseOver={isMoc3 ? undefined : handleMouseOver}
      onClick={isMoc3 ? undefined : handleClick}
    >
      <Tips {...tips}></Tips>
      {showTool && <Toolbar onShowMessage={handleMessageChange}></Toolbar>}
      <RenderWrapper>
        <Render {...cavSize} modelPath={modelPath}></Render>
      </RenderWrapper>
    </Wrapper>
  )
}

export default PetPage
