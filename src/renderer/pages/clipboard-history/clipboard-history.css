/* 隐藏滚动条的通用类 */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* 多行文本截断 */
.line-clamp-6 {
  display: -webkit-box;
  -webkit-line-clamp: 6;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.bg-selected {
  /* background-color: var(--accent-color, #3b82f6) !important; */
  border-color: var(--accent-color, #3b82f6) !important;
  /* color: white !important; */
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3) !important;
}

.bg-selected * {
  /* color: white !important; */
}

.bg-selected .text-muted-foreground {
  /* color: rgba(255, 255, 255, 0.8) !important; */
}

/* 剪贴板历史容器 */
.clipboard-history-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: var(--background-color);
  color: var(--text-color);
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.clipboard-history-container.center-window {
  width: 600px;
  height: 500px;
  border: 1px solid var(--border-color);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.clipboard-history-container.bottom-bar {
  height: 100vh;
  width: 100vw;
  border-radius: 0;
  border: none;
  position: fixed;
  bottom: 0;
  left: 0;
  background: var(--background-color);
}

/* 头部搜索栏 */
.clipboard-history-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
  background: var(--header-background);
}

.search-container {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  background: var(--input-background);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 8px 12px;
  transition: all 0.2s ease;
}

.search-container:focus-within {
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px rgba(var(--accent-rgb), 0.1);
}

.search-container.compact {
  padding: 6px 10px;
  border-radius: 4px;
}

.search-icon {
  color: var(--text-secondary);
  margin-right: 8px;
}

.search-input {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  color: var(--text-color);
  font-size: 14px;
}

.search-input.compact {
  font-size: 12px;
}

.search-input::placeholder {
  color: var(--text-secondary);
}

.clear-search-btn {
  background: none;
  border: none;
  padding: 4px;
  border-radius: 4px;
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.clear-search-btn:hover {
  background: var(--hover-background);
  color: var(--text-color);
}

.header-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  background: none;
  border: none;
  padding: 8px;
  border-radius: 6px;
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: var(--hover-background);
  color: var(--text-color);
}

/* 底部栏搜索栏 */
.search-bar {
  padding: 8px 16px;
  border-bottom: 1px solid var(--border-color);
  background: var(--header-background);
}

/* 内容区域 */
.clipboard-history-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.clipboard-history-content.vertical {
  overflow-y: auto;
}

/* 卡片容器布局 */
.cards-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  min-height: 140px;
  max-height: 160px;
}

.cards-container {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px 16px;
  overflow-x: auto;
  overflow-y: hidden;
  scroll-behavior: smooth;
  scrollbar-width: none;
  -ms-overflow-style: none;
  height: 100%;
}

.cards-container::-webkit-scrollbar {
  display: none;
}

/* 滚动按钮 */
.scroll-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
  background: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-color);
  cursor: pointer;
  transition: all 0.2s ease;
}

.scroll-btn:hover {
  background: var(--hover-background);
  transform: translateY(-50%) scale(1.05);
}

.scroll-btn.left {
  left: 8px;
}

.scroll-btn.right {
  right: 8px;
}

/* 剪贴板卡片 */
.clipboard-card {
  flex-shrink: 0;
  width: 120px;
  height: 100px;
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  overflow: hidden;
}

.clipboard-card.selected {
  border-color: var(--accent-color);
  background: rgba(var(--accent-rgb), 0.05);
  box-shadow: 0 0 0 2px rgba(var(--accent-rgb), 0.2);
}

/* 剪贴板项目卡片 */
.clipboard-item-card {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.clipboard-item-card.horizontal {
  flex-direction: row;
  align-items: center;
}

.clipboard-item-card.vertical {
  flex-direction: column;
  justify-content: space-between;
  align-items: stretch;
}

.clipboard-item-card.selected {
  background: rgba(var(--accent-rgb), 0.1);
}

/* 卡片内容 */
.card-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.content-preview {
  flex: 1;
  overflow: hidden;
}

.clipboard-item-card.horizontal .content-preview {
  display: flex;
  align-items: center;
  gap: 8px;
}

.clipboard-item-card.vertical .content-preview {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.content-icon {
  color: var(--text-secondary);
  font-size: 20px;
  flex-shrink: 0;
}

.clipboard-item-card.horizontal .content-icon {
  font-size: 24px;
}

.content-text {
  flex: 1;
  overflow: hidden;
}

.content-value {
  font-size: 12px;
  line-height: 1.3;
  color: var(--text-color);
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  word-break: break-word;
}

.clipboard-item-card.horizontal .content-value {
  -webkit-line-clamp: 2;
}

.content-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.file-name {
  font-size: 11px;
  font-weight: 500;
  color: var(--text-color);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.clipboard-item-card.horizontal .file-name {
  font-size: 12px;
}

.file-details {
  display: flex;
  gap: 4px;
  align-items: center;
}

.file-type,
.file-size {
  font-size: 10px;
  color: var(--text-secondary);
  background: var(--tag-background);
  padding: 1px 4px;
  border-radius: 3px;
}

.image-thumbnail {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  overflow: hidden;
  background: var(--image-placeholder);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.clipboard-item-card.horizontal .image-thumbnail {
  width: 50px;
  height: 50px;
}

.image-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 4px;
  border-top: 1px solid var(--border-light);
}

.clipboard-item-card.vertical .card-footer {
  position: absolute;
  bottom: 4px;
  left: 8px;
  right: 8px;
  padding-top: 2px;
  margin-top: 0;
  background: var(--card-background);
}

.timestamp {
  font-size: 10px;
  color: var(--text-secondary);
  font-weight: 400;
}

.item-count {
  font-size: 10px;
  color: var(--text-secondary);
  background: var(--tag-background);
  padding: 1px 4px;
  border-radius: 2px;
}

.selection-indicator {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--accent-color);
  display: none;
}

.clipboard-item-card.vertical .selection-indicator {
  top: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
}

/* 垂直列表布局 */
.history-list.vertical {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.history-item.vertical {
  padding: 12px;
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
}

.history-item.vertical.selected {
  background: rgba(var(--accent-rgb), 0.05);
  border-color: var(--accent-color);
}

/* 底部操作栏 */
.bottom-actions {
  display: flex;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  border-top: 1px solid var(--border-color);
  background: var(--header-background);
}

.action-btn-bottom {
  display: flex;
  align-items: center;
  gap: 6px;
  background: none;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 8px 12px;
  color: var(--text-color);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 13px;
}

.action-btn-bottom:hover {
  background: var(--hover-background);
  border-color: var(--accent-color);
}

/* 加载状态 */
.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  gap: 12px;
  color: var(--text-secondary);
  text-align: center;
}

.loading-card,
.empty-card {
  width: 120px;
  height: 100px;
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  font-size: 24px;
  animation: pulse 1.5s ease-in-out infinite;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--border-color);
  border-top: 2px solid var(--accent-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner.small {
  width: 16px;
  height: 16px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* CSS 变量 */
:root {
  --background-color: #ffffff;
  --header-background: #f8f9fa;
  --card-background: #ffffff;
  --input-background: #f8f9fa;
  --hover-background: #f1f3f4;
  --text-color: #333333;
  --text-secondary: #666666;
  --border-color: #e0e0e0;
  --border-light: #f0f0f0;
  --accent-color: #007aff;
  --accent-rgb: 0, 122, 255;
  --tag-background: #f0f0f0;
  --image-placeholder: #f5f5f5;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background-color: #1c1c1e;
    --header-background: #2c2c2e;
    --card-background: #2c2c2e;
    --input-background: #3a3a3c;
    --hover-background: #48484a;
    --text-color: #ffffff;
    --text-secondary: #98989a;
    --border-color: #48484a;
    --border-light: #3a3a3c;
    --accent-color: #007aff;
    --accent-rgb: 0, 122, 255;
    --tag-background: #48484a;
    --image-placeholder: #3a3a3c;
  }
}

/* 剪贴板项目预览 */
.clipboard-item-preview {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px;
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clipboard-item-content {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.clipboard-item-preview.compact .clipboard-item-content {
  gap: 8px;
}

.clipboard-item-content .content-icon {
  color: var(--text-secondary);
  font-size: 20px;
  flex-shrink: 0;
  margin-top: 2px;
}

.content-type-icon {
  font-size: 16px;
}

.clipboard-item-preview.compact .content-type-icon {
  font-size: 14px;
}

.clipboard-item-content .image-preview {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  overflow: hidden;
  flex-shrink: 0;
}

.clipboard-item-preview.compact .image-preview {
  width: 32px;
  height: 32px;
}

.clipboard-item-content .content-details {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 0;
}

.clipboard-item-content .content-preview {
  font-size: 14px;
  line-height: 1.4;
  color: var(--text-color);
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-word;
}

.clipboard-item-preview.compact .content-preview {
  font-size: 12px;
  -webkit-line-clamp: 1;
}

.clipboard-item-content .content-meta {
  display: flex;
  gap: 8px;
  align-items: center;
}

.clipboard-item-content .content-type,
.clipboard-item-content .content-size {
  font-size: 11px;
  color: var(--text-secondary);
  background: var(--tag-background);
  padding: 2px 6px;
  border-radius: 3px;
  white-space: nowrap;
}

.clipboard-item-preview.compact .content-type,
.clipboard-item-preview.compact .content-size {
  font-size: 10px;
  padding: 1px 4px;
}

.clipboard-item-preview .timestamp {
  font-size: 11px;
  color: var(--text-secondary);
  align-self: flex-end;
  white-space: nowrap;
}

.clipboard-item-preview.compact .timestamp {
  font-size: 10px;
}

/* 交互模式选择器 */
.interaction-mode-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mode-select {
  background: var(--input-background);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 4px 8px;
  color: var(--text-color);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.mode-select:hover {
  border-color: var(--accent-color);
}

.mode-select:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 2px rgba(var(--accent-rgb), 0.1);
}

.mode-indicator {
  font-size: 12px;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: 4px;
}

.mode-text {
  font-size: 11px;
  color: var(--text-secondary);
  padding: 2px 6px;
  background: var(--tag-background);
  border-radius: 3px;
  white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 600px) {
  .mode-text {
    display: none;
  }
  
  .mode-select {
    font-size: 11px;
    padding: 3px 6px;
  }
  
  .interaction-mode-selector {
    gap: 6px;
  }
  
  .action-btn-bottom {
    padding: 6px 8px;
    font-size: 12px;
  }
  
  .action-btn-bottom span {
    display: none;
  }
}

/* 自定义滑块样式 - 紧凑版本 */
.slider {
  background: linear-gradient(90deg, hsl(var(--primary)) 0%, hsl(var(--primary)) var(--progress, 25%), hsl(var(--muted)) var(--progress, 25%), hsl(var(--muted)) 100%);
}

.slider::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: hsl(var(--primary));
  border: 2px solid hsl(var(--background));
  box-shadow: 0 1px 4px hsl(var(--primary) / 0.3);
  cursor: pointer;
  transition: all 0.2s ease;
}

.slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 6px hsl(var(--primary) / 0.4);
}

.slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: hsl(var(--primary));
  border: 2px solid hsl(var(--background));
  box-shadow: 0 1px 4px hsl(var(--primary) / 0.3);
  cursor: pointer;
  transition: all 0.2s ease;
}

.slider::-moz-range-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 6px hsl(var(--primary) / 0.4);
}

/* 卡片悬停效果 */
.settings-card {
  transition: all 0.3s ease;
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
}

.settings-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px -5px hsl(var(--shadow) / 0.1);
  border-color: hsl(var(--primary) / 0.3);
}

/* 按钮样式增强 */
.btn-primary {
  background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary)) 100%);
  box-shadow: 0 2px 8px hsl(var(--primary) / 0.3);
  transition: all 0.2s ease;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px hsl(var(--primary) / 0.4);
}

/* 状态指示器动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.status-indicator {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 图标容器渐变背景 */
.icon-container {
  background: linear-gradient(135deg, var(--bg-color) 0%, var(--bg-color-light) 100%);
  transition: all 0.3s ease;
}

.icon-container:hover {
  transform: scale(1.05);
}

/* 单选按钮样式优化 */
input[type="radio"] {
  accent-color: hsl(var(--primary));
}

/* 键盘快捷键样式 */
kbd {
  background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(var(--muted)) 100%);
  border: 1px solid hsl(var(--border));
  box-shadow: 0 1px 3px hsl(var(--shadow) / 0.1), inset 0 1px 0 hsl(var(--background));
}

/* 窗口预览样式 */
.window-preview {
  transition: all 0.3s ease;
}

.window-preview:hover {
  transform: scale(1.1);
}

/* 紧凑设置布局样式 */
.settings-compact-card {
  transition: all 0.2s ease;
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
}

.settings-compact-card:hover {
  border-color: hsl(var(--primary) / 0.3);
  box-shadow: 0 2px 8px hsl(var(--shadow) / 0.1);
}

/* 紧凑按钮样式 */
.btn-compact {
  transition: all 0.2s ease;
}

.btn-compact:hover {
  transform: translateY(-1px);
}

/* 状态指示器紧凑版 */
.status-compact {
  transition: all 0.3s ease;
}

/* 图标容器紧凑版 */
.icon-compact {
  transition: all 0.2s ease;
}

.icon-compact:hover {
  transform: scale(1.05);
}

 