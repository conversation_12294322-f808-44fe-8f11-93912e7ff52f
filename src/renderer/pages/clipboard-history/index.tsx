import React, { useState, useRef, useEffect } from 'react';
import { Input, InputRef } from 'antd';
import { Button } from '../../components/ui/button';
import {
  Search,
  X,
  FileText,
  Image as ImageIcon,
} from 'lucide-react';
import { ClipboardItem } from '../../../shared/types/clipboard';
import { clipboardManagerClient } from '../../services/api/clipboard-manager';
import { ThemeProvider, useTheme } from '../../components/theme-provider';
import { changeLanguage } from '../../i18n';
import { useClipboardHistory } from '../../hooks/useClipboardHistory';
import {
  getItemContent,
  formatTime,
  getIcon,
  getFileInfo,
  isImageFile,
  isLowQualityImage
} from '../../utils/clipboard';
import './clipboard-history.css';

// 内部剪贴板历史组件，用于监听主题变化
const ClipboardHistoryContent: React.FC = () => {
  const { setTheme } = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const scrollRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<InputRef>(null);

  // 使用自定义hook管理剪贴板历史
  const {
    filteredData,
    selectedItemId,
    selectedIndex,
    isLoading,
    hasMoreData,
    totalCount,
    isVip,
    hasReachedFreeLimit,
    freeItemsUsed,
    maxFreeItems,
    handleItemClick,
    handleItemDoubleClick,
    setSelectedIndex,
    onScroll,
    resetSelection,
  } = useClipboardHistory({
    searchQuery,
    keyboardNavigation: 'horizontal',
    autoSelectFirst: true,
  });

  // 监听主题变化事件
  useEffect(() => {
    const handleThemeChange = (theme: string) => {
      console.log(`剪贴板历史窗口收到主题变化事件: ${theme}`);
      setTheme(theme as 'light' | 'dark' | 'system');
    };

    // 监听来自主进程的主题变化事件
    const removeListener = window.electron.system.onThemeChanged(handleThemeChange);

    return () => {
      removeListener();
    };
  }, [setTheme]);

  // 监听语言变化事件
  useEffect(() => {
    const handleLanguageChange = (language: string) => {
      console.log(`剪贴板历史窗口收到语言变化事件: ${language}`);
      changeLanguage(language);
    };

    // 监听来自主进程的语言变化事件
    const removeListener = window.electron.system.onLanguageChanged(handleLanguageChange);

    return () => {
      removeListener();
    };
  }, []);

  // 全局键盘事件处理
  useEffect(() => {
    const handleGlobalKeyDown = (e: KeyboardEvent) => {
      const inputElement = inputRef.current?.input;
      const isInputFocused = e.target === inputElement;

      // 处理左右键导航（水平布局）
      if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
        if (isInputFocused && inputElement) {
          // 当输入框聚焦时，检查光标位置
          const cursorPosition = inputElement.selectionStart || 0;
          const textLength = searchQuery.length;

          if (e.key === 'ArrowRight' && cursorPosition === textLength) {
            // 光标在最右边，右键触发切换选中
            e.preventDefault();
            const newIndex = selectedIndex + 1;
            if (newIndex < filteredData.length) {
              setSelectedIndex(newIndex);
            } else if (hasMoreData && !isLoading) {
              onScroll(scrollRef.current!);
            }
            // 让输入框失去焦点，进入导航模式
            inputRef.current?.blur();
            return;
          } else if (e.key === 'ArrowLeft' && cursorPosition === 0) {
            // 光标在最左边，左键触发切换选中
            e.preventDefault();
            setSelectedIndex(selectedIndex > 0 ? selectedIndex - 1 : selectedIndex);
            // 让输入框失去焦点，进入导航模式
            inputRef.current?.blur();
            return;
          }
          // 否则让输入框正常处理光标移动
          return;
        } else {
          // 输入框未聚焦，直接处理导航
          e.preventDefault();
          if (e.key === 'ArrowLeft') {
            setSelectedIndex(selectedIndex > 0 ? selectedIndex - 1 : selectedIndex);
          } else if (e.key === 'ArrowRight') {
            const newIndex = selectedIndex + 1;
            if (newIndex < filteredData.length) {
              setSelectedIndex(newIndex);
            } else if (hasMoreData && !isLoading) {
              onScroll(scrollRef.current!);
            }
          }
          return;
        }
      }

      // 回车键由 useClipboardHistory hook 统一处理，避免重复处理

      // 如果是可输入的字符（字母、数字、符号等），自动聚焦到搜索框
      if (e.key.length === 1 && !e.ctrlKey && !e.metaKey && !e.altKey && !isInputFocused) {
        e.preventDefault();
        if (inputRef.current) {
          inputRef.current.focus();
          // 将字符添加到搜索框
          setSearchQuery(prev => prev + e.key);
        }
        return;
      }

      // 处理ESC键清空搜索或关闭窗口
      if (e.key === 'Escape') {
        e.preventDefault();
        if (searchQuery) {
          setSearchQuery('');
          if (inputRef.current) {
            inputRef.current.blur();
          }
        } else {
          if (window.electron?.clipboardHistory?.hide) {
            window.electron.clipboardHistory.hide();
          }
        }
      }
    };

    document.addEventListener('keydown', handleGlobalKeyDown);

    return () => {
      document.removeEventListener('keydown', handleGlobalKeyDown);
    };
  }, [searchQuery, selectedIndex, filteredData, hasMoreData, isLoading, setSelectedIndex, handleItemDoubleClick, onScroll]);

  // 节流函数引用
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 处理鼠标滑轮横向滚动
  const handleWheel = (e: React.WheelEvent) => {
    if (scrollRef.current) {
      e.preventDefault();

      // 使用requestAnimationFrame优化滚动性能
      requestAnimationFrame(() => {
        if (scrollRef.current) {
          scrollRef.current.scrollLeft += e.deltaY;

          // 节流检查加载更多数据
          if (scrollTimeoutRef.current) {
            clearTimeout(scrollTimeoutRef.current);
          }

          scrollTimeoutRef.current = setTimeout(() => {
            if (scrollRef.current) {
              const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current;
              const scrollPercentage = (scrollLeft + clientWidth) / scrollWidth;

              // 提高阈值到95%，让加载更贴近实际滚动位置
              if (scrollPercentage >= 0.95 && hasMoreData && !isLoading) {
                onScroll(scrollRef.current);
              }
            }
          }, 100); // 100ms节流
        }
      });
    }
  };

  useEffect(() => {
    clipboardManagerClient.initialize();

    // 监听窗口重置事件（窗口关闭时重置状态）
    const handleWindowReset = () => {
      console.log('收到剪切板历史窗口重置事件，重置状态');
      // 清空搜索
      setSearchQuery('');
      // 重置选中项到第一个
      resetSelection();
      // 重置滚动位置到顶部
      if (scrollRef.current) {
        scrollRef.current.scrollTop = 0;
      }
    };

    // 监听来自主进程的窗口重置消息
    const removeListener = window.electron.clipboardHistory.onReset(handleWindowReset);

    return () => {
      clipboardManagerClient.destroy();
      removeListener();
      // 清理滚动节流定时器
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, [resetSelection]);

  // 选中项变化时自动滚动到可视区域
  useEffect(() => {
    const container = scrollRef.current;
    if (!container) return;

    // 使用requestAnimationFrame优化滚动性能
    requestAnimationFrame(() => {
      const selectedEl = container.querySelector(`[data-index='${selectedIndex}']`) as HTMLElement | null;
      if (!selectedEl) return;

      // 检查元素是否已经在可视区域内
      const containerRect = container.getBoundingClientRect();
      const elementRect = selectedEl.getBoundingClientRect();

      const isVisible = elementRect.left >= containerRect.left &&
                       elementRect.right <= containerRect.right;

      // 只有当元素不在可视区域时才滚动
      if (!isVisible) {
        selectedEl.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
      }
    });
  }, [selectedIndex]);

  return (
    <div className="flex flex-col h-screen bg-white dark:bg-gray-800">
      {/* 顶部搜索栏 */}
      <div className="flex items-center justify-between px-4 py-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
        <div className="flex items-center gap-3 flex-1 max-w-md">
          <Input
            ref={inputRef}
            placeholder="搜索剪贴板历史..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            prefix={<Search className="w-4 h-4 text-gray-500 dark:text-gray-400" />}
            suffix={
              searchQuery && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 hover:bg-gray-100 dark:hover:bg-gray-700"
                  onClick={() => setSearchQuery('')}
                >
                  <X className="w-3 h-3 text-gray-500 dark:text-gray-400" />
                </Button>
              )
            }
            className="h-8 flex-1 bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 !transition-none"
          />
        </div>
        
        {/* 会员状态指示器 */}
        {!isVip && (
          <div className="flex items-center gap-2 text-xs">
            <div className="flex items-center gap-1 text-gray-600 dark:text-gray-400">
              <span>{freeItemsUsed}/{maxFreeItems}</span>
              {hasReachedFreeLimit && (
                <span className="text-blue-600 dark:text-blue-400">💎 升级会员</span>
              )}
            </div>
          </div>
        )}
      </div>

      {/* 内容区域 */}
      <div className="flex-1 overflow-hidden">
        <div 
          ref={scrollRef}
          className="flex items-center gap-4 p-4 h-full overflow-x-auto overflow-y-hidden scrollbar-hide"
          onWheel={handleWheel}
        >
          {filteredData.length === 0 && !isLoading ? (
            <div className="flex-1 flex items-center justify-center text-gray-500 dark:text-gray-400 w-full h-full">
              <div className="text-center w-full">
                <FileText className="w-12 h-12 mx-auto mb-2 opacity-50 text-gray-400 dark:text-gray-500" />
                <p className="text-sm">
                  {searchQuery ? '没有找到匹配的内容' : '剪贴板历史为空'}
                </p>
              </div>
            </div>
          ) : (
            <>
              {filteredData.map((item, index) => {
                const content = getItemContent(item);
                const { fileType, size, thumbnail } = getFileInfo(item);
                
                return (
                  <div
                    key={item.id}
                    data-index={index}
                    className={`flex-shrink-0 w-48 h-48 rounded-lg border cursor-pointer ${
                      selectedItemId === item.id 
                        ? 'bg-blue-50 border-blue-200 shadow-md dark:bg-blue-900/20 dark:border-blue-700' 
                        : 'bg-white border-gray-200 shadow-sm hover:shadow-md hover:border-gray-300 dark:bg-gray-800 dark:border-gray-700 dark:hover:border-gray-600'
                    }`}
                    onClick={() => handleItemClick(item)}
                    onDoubleClick={() => handleItemDoubleClick(item)}
                  >
                    {/* 卡片头部 */}
                    <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700">
                      <div className="flex items-center gap-2">
                        <div className="flex flex-col">
                          <span className="text-xs font-medium text-gray-900 dark:text-gray-100">
                            {item.type === 'text' ? '文本' : 
                              isImageFile(item) ? '图片' :
                              item.type === 'file' ? '文件' : '文件夹'}
                          </span>
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {formatTime(item.timestamp)}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* 卡片内容 */}
                    {isImageFile(item) ? (
                      /* 图片文件类型：高度100%，宽度自适应 */
                      <div className="h-30 flex justify-center relative overflow-hidden pt-2">
                        {thumbnail && thumbnail.length > 0 ? (
                          <div className="h-28 relative flex items-center justify-center">
                            <img 
                              src={thumbnail} 
                              alt="图片" 
                              className={`max-h-full h-full w-auto ${
                                isLowQualityImage(item) ? 'border border-yellow-300 shadow-yellow-100' : ''
                              }`}
                            />
                            {/* 低质量图片警告标识 */}
                            {isLowQualityImage(item) && (
                              <div className="absolute top-2 right-2 w-5 h-5 bg-yellow-500 rounded-full flex items-center justify-center">
                                <span className="text-xs text-white font-bold">!</span>
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="w-full h-full flex items-center justify-center bg-orange-50 relative">
                            <ImageIcon className="w-12 h-12 text-orange-400" />
                            {/* 占位符标识 */}
                            <div className="absolute top-2 right-2 w-4 h-4 bg-orange-400 rounded-full flex items-center justify-center">
                              <span className="text-xs text-white">!</span>
                            </div>
                          </div>
                        )}
                      </div>
                    ) : (
                      /* 非图片类型：保持原有显示方式 */
                      <div className="p-3 flex-1 flex flex-col justify-center overflow-hidden">
                        {/* 显示文件缩略图（如果有） */}
                        {item.type === 'file' && thumbnail && (
                          <div className="flex justify-center mb-2">
                            <img 
                              src={thumbnail} 
                              alt="缩略图" 
                              className="w-16 h-16 object-cover rounded border"
                            />
                          </div>
                        )}
                        
                        {/* 文件名 */}
                        <div className="text-sm font-medium text-gray-900 dark:text-gray-100 break-words line-clamp-3">
                          {item.type === 'file' ? 
                            content.split('/').pop() : 
                            content
                          }
                        </div>
                        
                        {/* 文件完整路径（文件类型时显示） */}
                        {item.type === 'file' && (
                          <div className="text-xs text-gray-500 dark:text-gray-400 mt-1 truncate" title={content} style={{ direction: 'rtl', textAlign: 'left' }}>
                            {content}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                );
              })}
              
              {/* 加载中指示器 */}
              {isLoading && (
                <div className="flex-shrink-0 w-48 h-48 flex items-center justify-center">
                  <div className="flex flex-col items-center gap-2 text-gray-500 dark:text-gray-400">
                    <div className="w-8 h-8 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
                    <span className="text-sm">加载中...</span>
                  </div>
                </div>
              )}
              
              {/* 没有更多数据指示器 */}
              {!hasMoreData && filteredData.length > 0 && (
                <div className="flex-shrink-0 w-48 h-48 flex items-center justify-center">
                  <div className="text-center text-gray-500 dark:text-gray-400">
                    <FileText className="w-8 h-8 mx-auto mb-2 opacity-30" />
                    {hasReachedFreeLimit && !isVip ? (
                      <div className="space-y-1">
                        <span className="text-xs block">
                          非会员限制：{freeItemsUsed}/{maxFreeItems} 条
                        </span>
                        <div className="text-xs text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 px-2 py-1 rounded">
                          💎 升级会员可查看所有记录
                        </div>
                      </div>
                    ) : (
                      <span className="text-xs">
                        已显示全部 {totalCount} 条记录
                      </span>
                    )}
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

const ClipboardHistoryPage: React.FC = () => {
  return (
    <ThemeProvider defaultTheme="system">
      <ClipboardHistoryContent />
    </ThemeProvider>
  );
};

export default ClipboardHistoryPage; 