import React, { useState, useEffect } from 'react';
import { fileManagerClient } from '../../services/api/file-manager';
import { FileInfo } from '../../../shared/types/file';
import { Input } from '../../components/ui/input';
import { useTranslation } from 'react-i18next';
import { Button } from '../../components/ui/button';
import { ArrowLeft, Search, File, Folder } from 'lucide-react';

interface FileSearchViewProps {
  onBackToMain: () => void;
  initialQuery?: string;
}

/**
 * 文件搜索视图组件
 * 用于搜索文件和显示文件列表
 */
const FileSearchView: React.FC<FileSearchViewProps> = ({ 
  onBackToMain,
  initialQuery = '' 
}) => {
  const { t } = useTranslation();
  const [query, setQuery] = useState(initialQuery);
  const [files, setFiles] = useState<FileInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedFileIndex, setSelectedFileIndex] = useState(-1);
  const [previewContent, setPreviewContent] = useState<string | null>(null);

  // 初始加载或搜索
  useEffect(() => {
    if (!query) {
      loadRecentFiles();
    } else {
      searchFiles(query);
    }
  }, [query]);

  // 加载最近文件
  const loadRecentFiles = async () => {
    setLoading(true);
    try {
      const recentFiles = await fileManagerClient.getRecentFiles();
      setFiles(recentFiles);
    } catch (error) {
      console.error('加载最近文件失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 搜索文件
  const searchFiles = async (searchQuery: string) => {
    setLoading(true);
    try {
      const searchResults = await fileManagerClient.searchFiles(searchQuery);
      setFiles(searchResults);
    } catch (error) {
      console.error('搜索文件失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 打开文件
  const openFile = async (file: FileInfo) => {
    try {
      await fileManagerClient.openFile(file.path);
    } catch (error) {
      console.error('打开文件失败:', error);
    }
  };

  // 打开文件所在位置
  const openFileLocation = async (file: FileInfo) => {
    try {
      await fileManagerClient.openFileLocation(file.path);
    } catch (error) {
      console.error('打开文件位置失败:', error);
    }
  };

  // 加载文件预览
  const loadFilePreview = async (file: FileInfo) => {
    if (file.isDirectory) {
      setPreviewContent(null);
      return;
    }
    
    try {
      const preview = await fileManagerClient.readFilePreview(file.path);
      setPreviewContent(preview);
    } catch (error) {
      console.error('加载文件预览失败:', error);
      setPreviewContent(null);
    }
  };

  // 处理文件项点击
  const handleFileClick = (index: number) => {
    setSelectedFileIndex(index);
    if (index >= 0 && index < files.length) {
      loadFilePreview(files[index]);
    }
  };

  // 处理搜索输入变化
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setQuery(e.target.value);
  };

  return (
    <div className="w-full max-w-full">
      <div className="flex items-center space-x-2 bg-background/80 backdrop-blur-sm border rounded-t-lg p-2 shadow-lg">
        <Button
          variant="ghost"
          size="icon"
          onClick={onBackToMain}
          className="h-8 w-8"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <Input
          type="text"
          placeholder={t('fileSearch.placeholder')}
          className="flex-1"
          value={query}
          onChange={handleSearchChange}
          autoFocus
        />
        <Search className="h-4 w-4 text-muted-foreground" />
      </div>

      <div className="mt-2 flex bg-background border rounded-b-lg overflow-hidden shadow-lg">
        <div className="w-1/3 border-r max-h-80 overflow-y-auto">
          {loading ? (
            <div className="p-4 text-center text-muted-foreground">
              {t('fileSearch.loading')}
            </div>
          ) : files.length === 0 ? (
            <div className="p-4 text-center text-muted-foreground">
              {t('fileSearch.noResults')}
            </div>
          ) : (
            files.map((file, index) => (
              <div
                key={file.path}
                className={`flex items-center p-2 cursor-pointer hover:bg-secondary ${
                  selectedFileIndex === index ? 'bg-secondary' : ''
                }`}
                onClick={() => handleFileClick(index)}
              >
                {file.isDirectory ? (
                  <Folder className="h-4 w-4 mr-2 text-blue-500" />
                ) : (
                  <File className="h-4 w-4 mr-2 text-gray-500" />
                )}
                <div className="truncate flex-1">
                  <div className="font-medium truncate">{file.name}</div>
                  <div className="text-xs text-muted-foreground truncate">
                    {file.path}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        <div className="w-2/3 max-h-80 overflow-y-auto">
          {selectedFileIndex >= 0 && selectedFileIndex < files.length ? (
            <div className="p-4">
              <div className="flex justify-between items-center mb-4">
                <h3 className="font-medium">{files[selectedFileIndex].name}</h3>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => openFileLocation(files[selectedFileIndex])}
                  >
                    {t('filePreview.openLocation')}
                  </Button>
                  <Button
                    variant="default"
                    size="sm"
                    onClick={() => openFile(files[selectedFileIndex])}
                  >
                    {t('filePreview.open')}
                  </Button>
                </div>
              </div>

              {previewContent === null ? (
                <div className="p-4 text-center text-muted-foreground">
                  {files[selectedFileIndex].isDirectory
                    ? t('filePreview.isDirectory')
                    : t('filePreview.noPreview')}
                </div>
              ) : (
                <pre className="bg-muted p-4 rounded text-xs overflow-x-auto">
                  {previewContent}
                </pre>
              )}
            </div>
          ) : (
            <div className="h-full flex items-center justify-center text-muted-foreground">
              {t('filePreview.selectFile')}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FileSearchView; 