import React, { useEffect, useRef, useState, useCallback } from 'react';
import { floatingBallClient } from '../../services/api/floating-ball';
import { FloatingBallUI } from './FloatingBallUI';
import { AudioRecorder } from './audioRecorder';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '../../components/ui/dialog';
import { Button } from '../../components/ui/button';
import { changeLanguage } from '../../i18n';
/**
 * 悬浮球页面
 * 使用重构后的悬浮球UI组件
 */
const FloatingBallPage: React.FC = () => {
  // 状态
  const [isCapturing, setIsCapturing] = useState(false);
  const [isVoiceRecording, setIsVoiceRecording] = useState(false);
  const [voiceVolume, setVoiceVolume] = useState(0);
  const [snapSide, setSnapSide] = useState<'left' | 'right'>('right');
  const [isGlobalDragActive, setIsGlobalDragActive] = useState(false);
  
  // 引用
  const ballRef = useRef<HTMLDivElement>(null);

  // 音频录制器
  const audioRecorder = useRef<AudioRecorder>(new AudioRecorder());
  
  // 组件初始化时监听消息
  useEffect(() => {
    // 订阅语音录制状态变化
    const unsubscribeVoice = floatingBallClient.onVoiceRecordingChange?.(
      (recording, volume) => {
        console.log('语音录制状态变化', { recording, volume });
        setIsVoiceRecording(recording);
        if (volume !== undefined) {
          setVoiceVolume(volume);
        }
      }
    );
    
    // 订阅截图模式状态变化
    const unsubscribeCapture = floatingBallClient.onScreenCaptureChange?.(
      (capturing) => {
        setIsCapturing(capturing);
      }
    );
    
    // 订阅吸附侧边变化
    const unsubscribeSnapSide = floatingBallClient.onSnapSideChange?.(
      (side) => {
        console.log('吸附侧边变化', side);
        setSnapSide(side);
      }
    );

    // 订阅全局拖拽状态变化
    const unsubscribeGlobalDrag = floatingBallClient.onGlobalDragStateChange?.(
      (isActive) => {
        console.log('全局拖拽状态变化', isActive);
        setIsGlobalDragActive(isActive);
        // 全局拖拽状态变化时，更新穿透状态管理
        setPenetrationState(prevState => {
          const newState = { ...prevState, isDragActive: isActive };
          const shouldPenetrate = !newState.isMouseInFloatingBall && !newState.isDragActive;

          console.log('全局拖拽触发穿透状态更新:', {
            isMouseInFloatingBall: newState.isMouseInFloatingBall,
            isDragActive: newState.isDragActive,
            shouldPenetrate,
            globalDragActive: isActive
          });

          // 对于全局拖拽结束的情况，需要延迟一点设置穿透状态
          // 确保拖拽事件完全处理完毕
          if (!isActive && shouldPenetrate) {
            setTimeout(() => {
              if (floatingBallClient.setIgnoreMouseEvents) {
                console.log('延迟恢复穿透状态');
                floatingBallClient.setIgnoreMouseEvents(true);
              }
            }, 100);
          } else if (isActive) {
            // 全局拖拽开始时，主进程已经设置为不可穿透，这里不需要重复设置
            console.log('全局拖拽开始，主进程已设置为不可穿透');
          }

          return newState;
        });
      }
    );

    // 初始时设置鼠标事件穿透
    if (floatingBallClient.setIgnoreMouseEvents) {
      floatingBallClient.setIgnoreMouseEvents(true);
    }

    // 清理函数
    return () => {
      // 取消所有订阅
      if (unsubscribeVoice) unsubscribeVoice();
      if (unsubscribeCapture) unsubscribeCapture();
      if (unsubscribeSnapSide) unsubscribeSnapSide();
      if (unsubscribeGlobalDrag) unsubscribeGlobalDrag();
    };
  }, []);

  // 监听语言变化事件
  useEffect(() => {
    const handleLanguageChange = (language: string) => {
      console.log(`悬浮球窗口收到语言变化事件: ${language}`);
      changeLanguage(language);
    };

    // 监听来自主进程的语言变化事件
    const removeListener = window.electron.system.onLanguageChanged(handleLanguageChange);

    return () => {
      removeListener();
    };
  }, []);
  
  // 穿透状态管理
  const [penetrationState, setPenetrationState] = useState({
    isMouseInFloatingBall: false,
    isDragActive: false,
  });

  // 防抖定时器引用
  const penetrationTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 统一的穿透控制逻辑 - 添加防抖机制
  const updatePenetration = useCallback((newState: Partial<typeof penetrationState>) => {
    setPenetrationState(prevState => {
      const updatedState = { ...prevState, ...newState };

      // 判断是否应该穿透：只有当鼠标不在悬浮球内且没有拖拽时才穿透
      const shouldPenetrate = !updatedState.isMouseInFloatingBall && !updatedState.isDragActive;

      console.log('穿透状态更新:', {
        isMouseInFloatingBall: updatedState.isMouseInFloatingBall,
        isDragActive: updatedState.isDragActive,
        shouldPenetrate,
        source: newState
      });

      // 清除之前的定时器
      if (penetrationTimerRef.current) {
        clearTimeout(penetrationTimerRef.current);
      }

      // 如果是全局拖拽开始，立即设置为不穿透确保能接收文件拖拽
      if (newState.isDragActive === true) {
        console.log('全局拖拽开始，立即设置为不穿透');
        if (floatingBallClient.setIgnoreMouseEvents) {
          floatingBallClient.setIgnoreMouseEvents(false);
        }
        return updatedState;
      }

      // 如果是全局拖拽结束，延迟恢复穿透状态
      if (newState.isDragActive === false && updatedState.isDragActive) {
        console.log('全局拖拽结束，延迟恢复穿透状态');
        setTimeout(() => {
          // 重新获取当前状态，确保使用最新的状态
          setPenetrationState(currentState => {
            const currentShouldPenetrate = !currentState.isMouseInFloatingBall && !currentState.isDragActive;
            console.log('🔄 检查是否需要恢复穿透:', {
              isMouseInFloatingBall: currentState.isMouseInFloatingBall,
              isDragActive: currentState.isDragActive,
              shouldPenetrate: currentShouldPenetrate
            });

            if (currentShouldPenetrate && floatingBallClient.setIgnoreMouseEvents) {
              console.log('🔄 恢复穿透状态');
              floatingBallClient.setIgnoreMouseEvents(true);
            }
            return currentState;
          });
        }, 300); // 增加延迟时间到300ms
        return updatedState;
      }

      // 对于设置为不穿透的情况，立即执行；对于设置为穿透的情况，稍微延迟
      if (!shouldPenetrate) {
        // 立即设置为不穿透，确保交互响应及时
        if (floatingBallClient.setIgnoreMouseEvents) {
          floatingBallClient.setIgnoreMouseEvents(false);
        }
      } else {
        // 延迟设置为穿透，避免鼠标快速移动时的闪烁
        penetrationTimerRef.current = setTimeout(() => {
          if (floatingBallClient.setIgnoreMouseEvents) {
            floatingBallClient.setIgnoreMouseEvents(true);
          }
          penetrationTimerRef.current = null;
        }, 50); // 50ms延迟
      }

      return updatedState;
    });
  }, []);

  // 处理鼠标进入悬浮球（基于id="floating-ball"的精确判断）
  const handleMouseEnter = useCallback(() => {
    console.log('鼠标进入悬浮球区域 (id="floating-ball")');
    updatePenetration({ isMouseInFloatingBall: true });
  }, [updatePenetration]);

  // 处理鼠标离开悬浮球
  const handleMouseLeave = useCallback(() => {
    console.log('鼠标离开悬浮球区域 (id="floating-ball")');
    updatePenetration({ isMouseInFloatingBall: false });
  }, [updatePenetration]);

  // 处理拖拽进入悬浮球
  const handleDragEnter = useCallback(() => {
    console.log('拖拽进入悬浮球区域');
    updatePenetration({ isDragActive: true });
  }, [updatePenetration]);

  // 处理拖拽离开悬浮球
  const handleDragLeave = useCallback(() => {
    console.log('拖拽离开悬浮球区域');
    updatePenetration({ isDragActive: false });
  }, [updatePenetration]);

  // 初始化穿透状态 - 确保文件拖拽功能正常
  useEffect(() => {
    // 延迟一点时间，确保组件完全挂载和事件绑定完成
    const timer = setTimeout(() => {
      console.log('🔧 初始化悬浮球穿透状态');
      // 检查当前状态，如果鼠标不在悬浮球内且没有拖拽，则设置为穿透
      const shouldPenetrate = !penetrationState.isMouseInFloatingBall && !penetrationState.isDragActive;
      console.log('🔧 初始穿透状态:', { shouldPenetrate, penetrationState });

      if (floatingBallClient.setIgnoreMouseEvents) {
        console.log('🔧 设置窗口穿透状态为:', shouldPenetrate);
        floatingBallClient.setIgnoreMouseEvents(shouldPenetrate);
      }

      // 测试窗口是否能接收鼠标事件
      console.log('🔧 请尝试将鼠标移动到悬浮球上，看是否有鼠标进入日志');
    }, 200); // 延迟200ms，确保所有事件监听器都已设置

    return () => {
      clearTimeout(timer);
      // 清理穿透防抖定时器
      if (penetrationTimerRef.current) {
        clearTimeout(penetrationTimerRef.current);
        penetrationTimerRef.current = null;
      }
    };
  }, []);

  // 单击处理
  const handleSingleClick = useCallback(() => {
    console.log('浮动球: 单击事件');
    floatingBallClient.click();
  }, []);
  
  // 双击处理 - 直接启动多屏截图
  const handleDoubleClick = useCallback(async () => {
    const startTime = performance.now();
    console.log(`[性能] 浮动球双击事件开始: ${startTime.toFixed(2)}ms`);

    if (!isCapturing) {
      try {
        // 检查是否为多显示器环境
        const displayCheckStart = performance.now();
        const displays = await floatingBallClient.getDisplays();
        const displayCheckEnd = performance.now();
        console.log(`[性能] 显示器检测耗时: ${(displayCheckEnd - displayCheckStart).toFixed(2)}ms`);

        if (displays.length > 1) {
          // 多显示器环境，直接启动所有屏幕截图
          console.log(`[性能] 检测到${displays.length}个显示器，启动多屏截图`);
          const captureStart = performance.now();
          floatingBallClient.startMultiScreenCapture();
          console.log(`[性能] 多屏截图启动调用耗时: ${(performance.now() - captureStart).toFixed(2)}ms`);
        } else {
          // 单显示器环境，使用原有逻辑
          console.log('[性能] 单显示器，启动传统截图模式');
          const captureStart = performance.now();
          floatingBallClient.doubleClick();
          console.log(`[性能] 传统截图启动调用耗时: ${(performance.now() - captureStart).toFixed(2)}ms`);
        }

        const totalTime = performance.now() - startTime;
        console.log(`[性能] 浮动球双击处理总耗时: ${totalTime.toFixed(2)}ms`);
      } catch (error) {
        console.error('检测显示器失败，使用传统截图模式:', error);
        floatingBallClient.doubleClick();
      }
    } else {
      // 如果已经在截图模式，则取消
      floatingBallClient.cancelCapture?.();
      console.log('浮动球: 取消截图模式');
    }
  }, [isCapturing]);
  
  // 长按处理
  const handleLongPress = useCallback(() => {
    if (!isVoiceRecording) {
      // 开始录音
      console.log('浮动球: 开始录音');
      audioRecorder.current.start();
    } else {
      // 结束录音
      console.log('浮动球: 结束录音');
      audioRecorder.current.stop();
    }
  }, [isVoiceRecording]);
  
  // 拖拽开始处理
  const handleDragStart = useCallback((e: React.MouseEvent) => {
    console.log('浮动球: 拖拽开始', { x: e.screenX, y: e.screenY });
    floatingBallClient.dragStart({ x: e.screenX, y: e.screenY });
  }, []);
  
  // 拖拽过程处理
  const handleDrag = useCallback((e: MouseEvent) => {
    floatingBallClient.dragMove({ x: e.screenX, y: e.screenY });
  }, []);
  
  // 拖拽结束处理
  const handleDragEnd = useCallback(() => {
    console.log('浮动球: 拖拽结束');
    floatingBallClient.dragEnd?.();
  }, []);
  
  // 屏幕镜像模式自动结束处理
  const handleCaptureEnd = useCallback(() => {
    console.log('浮动球: 屏幕镜像模式自动结束');
    if (isCapturing) {
      // 确保调用cancelCapture来通知主进程屏幕镜像已取消
      if (floatingBallClient.cancelCapture) {
        console.log('浮动球: 调用API取消屏幕镜像模式');
        floatingBallClient.cancelCapture();
      }
      setIsCapturing(false);
    }
  }, [isCapturing]);
  
  
  return (
    <div className="w-screen h-screen overflow-hidden">
      <FloatingBallUI
        ref={ballRef}
        isCapturing={isCapturing}
        isVoiceRecording={isVoiceRecording}
        voiceVolume={voiceVolume}
        snapSide={snapSide}
        isGlobalDragActive={isGlobalDragActive}
        onSingleClick={handleSingleClick}
        onDoubleClick={handleDoubleClick}
        onLongPress={handleLongPress}
        onDragStart={handleDragStart}
        onDrag={handleDrag}
        onDragEnd={handleDragEnd}
        onCaptureEnd={handleCaptureEnd}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onExternalDragEnter={handleDragEnter}
        onExternalDragLeave={handleDragLeave}
      />
    </div>
  );
};

export default FloatingBallPage; 