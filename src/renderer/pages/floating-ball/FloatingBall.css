/* 悬浮球动画和样式 */

/* 呼吸动画效果 */
@keyframes breath {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(66, 133, 244, 0.4);
  }
  50% {
    box-shadow: 0 0 0 15px rgba(66, 133, 244, 0);
  }
}

.floating-ball-breath:hover {
  animation: breath 2s infinite ease-in-out;
}

/* 录音状态的脉冲动画 */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(234, 67, 53, 0.7);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 0 10px rgba(234, 67, 53, 0);
  }
}

.floating-ball-recording {
  animation: pulse 1.5s infinite ease-in-out;
  background-color: #EA4335 !important; /* 录音状态变为红色 */
  transition: all 0.2s ease-out;
}

/* 确保状态恢复时的过渡效果 */
.floating-ball:not(.floating-ball-recording):not(.floating-ball-capturing) {
  background-color: #4285F4;
  transition: all 0.3s ease-out;
}

/* 音频波动效果容器 */
.audio-wave-container {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  overflow: hidden;
}

/* 音频波动效果 */
.audio-wave {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(234,67,53,0.3) 0%, rgba(234,67,53,0) 70%);
  opacity: 0;
  animation: wave-fade 1.5s ease-out forwards;
}

@keyframes wave-fade {
  0% {
    transform: scale(0.8);
    opacity: 0.7;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* 状态提示文本 */
.status-text {
  position: absolute;
  bottom: -25px;
  width: 100%;
  text-align: center;
  font-size: 12px;
  color: #555;
  opacity: 0;
  transition: opacity 0.3s;
}

.floating-ball:hover .status-text {
  opacity: 1;
}

/* 截图状态样式 */
.floating-ball-capturing {
  background-color: #34A853 !important; /* 截图状态为绿色 */
  transform: scale(0.95);
  transition: all 0.2s ease-out;
}

/* 截图图标样式 */
.floating-ball-capturing svg {
  color: white;
  height: 24px;
  width: 24px;
  display: block;
}

/* 截图图标动画 */
@keyframes crop-animate {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.floating-ball-capturing svg {
  animation: crop-animate 2s infinite ease-in-out;
}

/* SVG高亮处理 */
.floating-ball-capturing svg rect {
  fill: #FF4500;
}

/* 截图图标确保在悬浮球中居中 */
.floating-ball-capturing .w-full {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

/* 长按提示效果 - 立即开始显示反应 */
@keyframes long-press-hint {
  0% {
    transform: scale(1);
    background-color: rgba(234, 67, 53, 0.5);
  }
  50% {
    transform: scale(1.05);
    background-color: rgba(234, 67, 53, 0.8);
  }
  100% {
    transform: scale(1);
    background-color: rgba(234, 67, 53, 1);
  }
}

/* 长按麦克风立即变红效果 */
.floating-ball:active:not(.floating-ball-recording):not(.floating-ball-capturing) {
  background-color: rgba(234, 67, 53, 0.5) !important;
}

/* 强制恢复默认状态 */
.floating-ball:not(:active):not(.floating-ball-recording):not(.floating-ball-capturing) img {
  opacity: 1;
  display: block;
  transition: opacity 0.2s ease-out;
}

/* 双击提示效果 */
@keyframes double-click-hint {
  0%, 100% {
    transform: scale(1);
  }
  33% {
    transform: scale(0.9);
  }
  66% {
    transform: scale(0.9);
  }
}

/* 按下时的阴影效果 - 已被下方的吸附样式替代 */
/* .floating-ball:active {
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
} */

/* 悬停时的提升效果 - 已被下方的吸附样式替代 */
/* .floating-ball:hover:not(.floating-ball-recording):not(.floating-ball-capturing) {
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.25);
  transform: translateY(-2px);
} */

/* 提醒用户可交互 */
.floating-ball {
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
}

/* 吸附在屏幕右侧的样式 */
.floating-ball {
  /* 左侧添加阴影，增强吸附感 */
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15), 0 2px 8px rgba(0, 0, 0, 0.1);
  /* 默认半圆形样式 */
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
  border-top-left-radius: 40px;
  border-bottom-left-radius: 40px;
}

/* 悬停时增强吸附效果 */
.floating-ball:hover:not(.floating-ball-recording):not(.floating-ball-capturing) {
  box-shadow: -4px 0 15px rgba(0, 0, 0, 0.25), 0 4px 15px rgba(0, 0, 0, 0.15);
  transform: translateX(-2px); /* 向左轻微移动，模拟从屏幕边缘弹出的效果 */
}

/* 悬停时的拉长效果 */
.floating-ball-hovered {
  /* 拉长时改为胶囊形状 */
  border-top-right-radius: 0px !important;
  border-bottom-right-radius: 0px !important;
  border-top-left-radius: 40px !important;
  border-bottom-left-radius: 40px !important;
  box-shadow: -4px 0 15px rgba(0, 0, 0, 0.25), 0 4px 15px rgba(0, 0, 0, 0.15) !important;
}

/* 左侧吸附时的悬停效果 */
.floating-ball-hovered-left {
  /* 从左侧中心点展开 */
  transform-origin: left center;
}

/* 按下时的样式，拖拽时恢复圆形 */
.floating-ball:active {
  transition: all 0.1s ease-in-out;
  box-shadow: -1px 0 5px rgba(0, 0, 0, 0.2), 0 1px 5px rgba(0, 0, 0, 0.15);
  transform: translateX(1px); /* 向右轻微移动，模拟被按入屏幕的效果 */
}

/* 拖拽时的视觉反馈 */
.floating-ball.scale-95 {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3); /* 拖拽时恢复圆形阴影 */
  border-radius: 50% !important; /* 拖拽时恢复圆形 */
}

/* 录音状态下保持吸附样式 */
.floating-ball-recording {
  box-shadow: -3px 0 12px rgba(234, 67, 53, 0.4), 0 3px 12px rgba(234, 67, 53, 0.2);
  background-color: #EA4335 !important; /* 录音状态红色背景 */
}

/* 截图状态下保持吸附样式 */
.floating-ball-capturing {
  box-shadow: -3px 0 12px rgba(52, 168, 83, 0.4), 0 3px 12px rgba(52, 168, 83, 0.2);
  background-color: #34A853 !important; /* 截图状态绿色背景 */
}

/* 白色背景的边框和阴影优化 */
.floating-ball.bg-white {
  border: 1px solid rgba(0, 0, 0, 0.1); /* 添加浅边框 */
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15), 0 2px 8px rgba(0, 0, 0, 0.1), 
              inset 0 1px 0 rgba(255, 255, 255, 0.5); /* 内阴影增加立体感 */
}

/* 悬停时白色背景的效果 */
.floating-ball.bg-white:hover {
  border-color: rgba(0, 0, 0, 0.2);
  box-shadow: -4px 0 15px rgba(0, 0, 0, 0.25), 0 4px 15px rgba(0, 0, 0, 0.15),
              inset 0 1px 0 rgba(255, 255, 255, 0.7);
}

/* 左侧吸附基础样式 */
.floating-ball-left {
  /* 右侧投影，增强吸附感 */
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15), 0 2px 8px rgba(0, 0, 0, 0.1);
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
  border-top-right-radius: 40px;
  border-bottom-right-radius: 40px;
}

/* 左侧吸附悬停样式 */
.floating-ball-left:hover:not(.floating-ball-recording):not(.floating-ball-capturing) {
  box-shadow: 4px 0 15px rgba(0, 0, 0, 0.25), 0 4px 15px rgba(0, 0, 0, 0.15);
  transform: translateX(2px); /* 向右轻微移动 */
}

/* 右侧吸附基础样式（覆盖默认） */
.floating-ball-right {
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15), 0 2px 8px rgba(0, 0, 0, 0.1);
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
  border-top-left-radius: 40px;
  border-bottom-left-radius: 40px;
}

.floating-ball-right:hover:not(.floating-ball-recording):not(.floating-ball-capturing) {
  box-shadow: -4px 0 15px rgba(0, 0, 0, 0.25), 0 4px 15px rgba(0, 0, 0, 0.15);
  transform: translateX(-2px);
}

/* 悬停拉长效果，根据方向决定 */
.floating-ball-hovered-left {
  /* 左侧拉长时保证右边圆角保持 */
  border-top-left-radius: 0px !important;
  border-bottom-left-radius: 0px !important;
  border-top-right-radius: 40px !important;
  border-bottom-right-radius: 40px !important;
  box-shadow: 4px 0 15px rgba(0, 0, 0, 0.25), 0 4px 15px rgba(0, 0, 0, 0.15) !important;
}

.floating-ball-hovered {
  /* 右侧拉长已存在定义，这里保持原样，仅确保类存在 */
}

/* 修复：悬浮球 hover 时不再横向位移，避免与屏幕边缘出现缝隙 */
.floating-ball-right:hover:not(.floating-ball-recording):not(.floating-ball-capturing),
.floating-ball-left:hover:not(.floating-ball-recording):not(.floating-ball-capturing) {
  transform: none;
} 

/* 通知相关样式 */
@keyframes slideIn {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  0% {
    transform: translateX(0);
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes slideOutLeft {
  0% {
    transform: translateX(0);
    opacity: 1;
  }
  100% {
    transform: translateX(-100%);
    opacity: 0;
  }
}

/* 通知容器样式 */
.notification-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 通知项样式 */
.notification-item {
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* 左侧吸附时的通知动画 */
.floating-ball-left + .notification-container .notification-item {
  animation: slideInLeft 0.3s ease-out;
}

/* 通知项悬停效果 */
.notification-item:hover {
  transform: scale(1.02);
  transition: transform 0.2s ease;
}

/* 通知标题样式 */
.notification-title {
  font-weight: 600;
  letter-spacing: 0.025em;
}

/* 通知详情样式 */
.notification-detail {
  word-wrap: break-word;
  word-break: break-word;
}

/* 成功通知特殊样式 */
.notification-item.success {
  background: linear-gradient(135deg, #10B981 0%, #059669 100%);
}

/* 失败通知特殊样式 */
.notification-item.fail {
  background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
}

/* 信息通知特殊样式 */
.notification-item.info {
  background: linear-gradient(135deg, #6B7280 0%, #4B5563 100%);
}

/* 自定义通知样式 */
.notification-item.custom {
  /* 背景色通过内联样式设置 */
}

/* 通知模式下的悬浮球样式 */
.floating-ball.notification-mode {
  /* 确保通知模式下的悬浮球有正确的过渡效果 */
  transition: all 0.3s ease;
}

/* 通知模式下的文本样式 */
.notification-content {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.notification-content .notification-title {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.notification-content .notification-detail {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 通知图标样式 */
.notification-icon {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.notification-icon:hover {
  transform: scale(1.05);
}

/* 通知计数器样式 */
.notification-count {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
} 