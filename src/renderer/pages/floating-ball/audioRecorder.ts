import { floatingBallClient } from '../../services/api/floating-ball';
import { getWebSocketManager } from '../../llm/services/WebSocketChatService';
import { setWebSocketDisableAutoReconnect } from '../../llm/services/WebSocketChatService';
import { AudioQueueManager } from '../../components/voice-manager';

// 防止重复注册AudioWorkletProcessor
let audioWorkletRegistered = false;

export class AudioRecorder {
  private mediaRecorder: MediaRecorder | null = null;
  private audioChunks: Blob[] = [];
  private stream: MediaStream | null = null;
  private opusEncoder: any = null;
  private opusDecoder: any = null;
  private SAMPLE_RATE = 16000;     // 采样率
  private CHANNELS = 1;            // 声道数
  private FRAME_SIZE = 960;        // 帧大小
  private audioContext: AudioContext | null = null;
  private isRecording: boolean = false;
  private pcmDataBuffer: Int16Array = new Int16Array();
  private audioBuffers: any[] = []; // 用于存储接收到的所有音频数据
  private totalAudioSize = 0; // 用于存储接收到的所有音频数据的总大小
  private websocket: any = null;
  private audioProcessor: any = null;
  private audioProcessorType: string = '';
  private audioSource: any = null;
  private audioQueue: AudioQueueManager | null = null;
  private inactivityTimer: any = null;
  private INACTIVITY_TIMEOUT = 5 * 60 * 1000; // 5分钟
  checkOpusLoaded() {
    try {
      // 检查Module是否存在（本地库导出的全局变量）
      // @ts-ignore
      if (typeof (window as any).Module === 'undefined') {
        throw new Error('Opus库未加载，Module对象不存在');
      }

      // 尝试先使用Module.instance（libopus.js最后一行导出方式）
      if (typeof (window as any).Module.instance !== 'undefined' && typeof (window as any).Module.instance._opus_decoder_get_size === 'function') {
        // 使用Module.instance对象替换全局Module对象
        (window as any).ModuleInstance = (window as any).Module.instance;
        // 3秒后隐藏状态
        return;
      }

      // 如果没有Module.instance，检查全局Module函数
      if (typeof (window as any).Module._opus_decoder_get_size === 'function') {
        (window as any).ModuleInstance = (window as any).Module;
        return;
      }

      throw new Error('Opus解码函数未找到，可能Module结构不正确');
    } catch (err) {
    }
  }
  initOpusEncoder() {
    try {
      if (this.opusEncoder) {
        return true; // 已经初始化过
      }

      if (!(window as any).ModuleInstance) {
        console.log('无法创建Opus编码器：ModuleInstance不可用', 'error');
        return false;
      }

      // 初始化一个Opus编码器
      const mod = (window as any).ModuleInstance;
      const sampleRate = 16000; // 16kHz采样率
      const channels = 1;       // 单声道
      const application = 2048; // OPUS_APPLICATION_VOIP = 2048

      // 创建编码器
      this.opusEncoder = {
        channels: channels,
        sampleRate: sampleRate,
        frameSize: 960, // 60ms @ 16kHz = 60 * 16 = 960 samples
        maxPacketSize: 4000, // 最大包大小
        module: mod,

        // 初始化编码器
        init: function () {
          try {
            // 获取编码器大小
            const encoderSize = mod._opus_encoder_get_size(this.channels);
            console.log(`Opus编码器大小: ${encoderSize}字节`, 'info');

            // 分配内存
            this.encoderPtr = mod._malloc(encoderSize);
            if (!this.encoderPtr) {
              throw new Error("无法分配编码器内存");
            }

            // 初始化编码器
            const err = mod._opus_encoder_init(
              this.encoderPtr,
              this.sampleRate,
              this.channels,
              application
            );

            if (err < 0) {
              throw new Error(`Opus编码器初始化失败: ${err}`);
            }

            // 设置位率 (16kbps)
            mod._opus_encoder_ctl(this.encoderPtr, 4002, 16000); // OPUS_SET_BITRATE

            // 设置复杂度 (0-10, 越高质量越好但CPU使用越多)
            mod._opus_encoder_ctl(this.encoderPtr, 4010, 5);     // OPUS_SET_COMPLEXITY

            // 设置使用DTX (不传输静音帧)
            mod._opus_encoder_ctl(this.encoderPtr, 4016, 1);     // OPUS_SET_DTX

            console.log("Opus编码器初始化成功", 'success');
            return true;
          } catch (error) {
            if (this.encoderPtr) {
              mod._free(this.encoderPtr);
              this.encoderPtr = null;
            }
            console.log(`Opus编码器初始化失败: ${error.message}`, 'error');
            return false;
          }
        },

        // 编码PCM数据为Opus
        encode: function (pcmData: any) {
          if (!this.encoderPtr) {
            if (!this.init()) {
              return null;
            }
          }

          try {
            const mod = this.module;

            // 为PCM数据分配内存
            const pcmPtr = mod._malloc(pcmData.length * 2); // 2字节/int16

            // 将PCM数据复制到HEAP
            for (let i = 0; i < pcmData.length; i++) {
              mod.HEAP16[(pcmPtr >> 1) + i] = pcmData[i];
            }

            // 为输出分配内存
            const outPtr = mod._malloc(this.maxPacketSize);

            // 进行编码
            const encodedLen = mod._opus_encode(
              this.encoderPtr,
              pcmPtr,
              this.frameSize,
              outPtr,
              this.maxPacketSize
            );

            if (encodedLen < 0) {
              throw new Error(`Opus编码失败: ${encodedLen}`);
            }

            // 复制编码后的数据
            const opusData = new Uint8Array(encodedLen);
            for (let i = 0; i < encodedLen; i++) {
              opusData[i] = mod.HEAPU8[outPtr + i];
            }

            // 释放内存
            mod._free(pcmPtr);
            mod._free(outPtr);

            return opusData;
          } catch (error) {
            console.log(`Opus编码出错: ${error.message}`, 'error');
            return null;
          }
        },

        // 销毁编码器
        destroy: function () {
          if (this.encoderPtr) {
            this.module._free(this.encoderPtr);
            this.encoderPtr = null;
          }
        }
      };

      const result = this.opusEncoder.init();
      return result;
    } catch (error) {
      console.log(`创建Opus编码器失败: ${error.message}`, 'error');
      return false;
    }
  }
  async initOpusDecoder() {
    if (this.opusDecoder) return this.opusDecoder; // 已经初始化

    try {
      // 检查ModuleInstance是否存在
      if (typeof (window as any).ModuleInstance === 'undefined') {
        if (typeof (window as any).Module !== 'undefined') {
          // 使用全局Module作为ModuleInstance
          (window as any).ModuleInstance = (window as any).Module;
          console.log('使用全局Module作为ModuleInstance', 'info');
        } else {
          throw new Error('Opus库未加载，ModuleInstance和Module对象都不存在');
        }
      }

      const mod = (window as any).ModuleInstance;

      // 创建解码器对象
      this.opusDecoder = {
        channels: this.CHANNELS,
        rate: this.SAMPLE_RATE,
        frameSize: this.FRAME_SIZE,
        module: mod,
        decoderPtr: null, // 初始为null

        // 初始化解码器
        init: function () {
          if (this.decoderPtr) return true; // 已经初始化

          // 获取解码器大小
          const decoderSize = mod._opus_decoder_get_size(this.channels);
          console.log(`Opus解码器大小: ${decoderSize}字节`, 'debug');

          // 分配内存
          this.decoderPtr = mod._malloc(decoderSize);
          if (!this.decoderPtr) {
            throw new Error("无法分配解码器内存");
          }

          // 初始化解码器
          const err = mod._opus_decoder_init(
            this.decoderPtr,
            this.rate,
            this.channels
          );

          if (err < 0) {
            this.destroy(); // 清理资源
            throw new Error(`Opus解码器初始化失败: ${err}`);
          }

          console.log("Opus解码器初始化成功", 'success');
          return true;
        },

        // 解码方法
        decode: function (opusData: any) {
          if (!this.decoderPtr) {
            if (!this.init()) {
              throw new Error("解码器未初始化且无法初始化");
            }
          }

          try {
            const mod = this.module;

            // 为Opus数据分配内存
            const opusPtr = mod._malloc(opusData.length);
            mod.HEAPU8.set(opusData, opusPtr);

            // 为PCM输出分配内存
            const pcmPtr = mod._malloc(this.frameSize * 2); // Int16 = 2字节

            // 解码
            const decodedSamples = mod._opus_decode(
              this.decoderPtr,
              opusPtr,
              opusData.length,
              pcmPtr,
              this.frameSize,
              0 // 不使用FEC
            );

            if (decodedSamples < 0) {
              mod._free(opusPtr);
              mod._free(pcmPtr);
              throw new Error(`Opus解码失败: ${decodedSamples}`);
            }

            // 复制解码后的数据
            const decodedData = new Int16Array(decodedSamples);
            for (let i = 0; i < decodedSamples; i++) {
              decodedData[i] = mod.HEAP16[(pcmPtr >> 1) + i];
            }

            // 释放内存
            mod._free(opusPtr);
            mod._free(pcmPtr);

            return decodedData;
          } catch (error) {
            console.log(`Opus解码错误: ${error.message}`, 'error');
            return new Int16Array(0);
          }
        },

        // 销毁方法
        destroy: function () {
          if (this.decoderPtr) {
            this.module._free(this.decoderPtr);
            this.decoderPtr = null;
          }
        }
      };

      // 初始化解码器
      if (!this.opusDecoder.init()) {
        throw new Error("Opus解码器初始化失败");
      }

      return this.opusDecoder;

    } catch (error) {
      console.log(`Opus解码器初始化失败: ${error.message}`, 'error');
      this.opusDecoder = null; // 重置为null，以便下次重试
      throw error;
    }
  }
  // 编码并发送Opus数据
  encodeAndSendOpus(pcmData: any = null) {
    if (!this.opusEncoder) {
      console.log('Opus编码器未初始化', 'error');
      return;
    }

    try {
      // 如果提供了PCM数据，则编码该数据
      if (pcmData) {
        // 使用已初始化的Opus编码器编码
        const opusData = this.opusEncoder.encode(pcmData);

        if (opusData && opusData.length > 0) {
          // 存储音频帧
          this.audioBuffers.push(opusData.buffer);
          this.totalAudioSize += opusData.length;

          // 如果WebSocket已连接，则发送数据
          if (this.websocket && this.websocket.ws && this.websocket.ws.readyState === WebSocket.OPEN) {
            try {
              // 服务端期望接收原始Opus数据，不需要任何额外包装
              this.websocket.ws.send(opusData.buffer);
            } catch (error) {
              console.log(`WebSocket发送错误: ${error.message}`, 'error');
            }
          }
        } else {
          console.log('Opus编码失败，无有效数据返回', 'error');
        }
      } else {
        // 处理剩余的PCM数据
        if (this.pcmDataBuffer.length > 0) {
          // 如果剩余的采样点不足一帧，用静音填充
          const samplesPerFrame = 960;
          if (this.pcmDataBuffer.length < samplesPerFrame) {
            const paddedBuffer = new Int16Array(samplesPerFrame);
            paddedBuffer.set(this.pcmDataBuffer);
            // 剩余部分为0（静音）
            this.encodeAndSendOpus(paddedBuffer);
          } else {
            this.encodeAndSendOpus(this.pcmDataBuffer.slice(0, samplesPerFrame));
          }
          this.pcmDataBuffer = new Int16Array(0);
        }
      }
    } catch (error) {
      console.log(`Opus编码错误: ${error.message}`, 'error');
    }
  }
  processPCMBuffer(buffer: any) {
    if (!this.isRecording) return;

    // 每次有音频数据流入都重置定时器
    this.resetInactivityTimer();

    // 将新的PCM数据追加到缓冲区
    const newBuffer = new Int16Array(this.pcmDataBuffer.length + buffer.length);
    newBuffer.set(this.pcmDataBuffer);
    newBuffer.set(buffer, this.pcmDataBuffer.length);
    this.pcmDataBuffer = newBuffer;

    // 检查是否有足够的数据进行Opus编码（16000Hz, 60ms = 960个采样点）
    const samplesPerFrame = 960; // 60ms @ 16kHz

    while (this.pcmDataBuffer.length >= samplesPerFrame) {
      // 从缓冲区取出一帧数据
      const frameData = this.pcmDataBuffer.slice(0, samplesPerFrame);
      this.pcmDataBuffer = this.pcmDataBuffer.slice(samplesPerFrame);

      // 编码为Opus
      this.encodeAndSendOpus(frameData);
    }
  }
  // 创建音频处理器
  async createAudioProcessor() {
    if (!this.audioContext) {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
        sampleRate: 16000,
        latencyHint: 'interactive'
      });
    }

    try {
      // 检查是否支持AudioWorklet
      if (this.audioContext.audioWorklet) {
        if (!audioWorkletRegistered) {
          // 注册音频处理器
          const blob = new Blob([`
              class AudioRecorderProcessor extends AudioWorkletProcessor {
                  constructor() {
                      super();
                      this.buffers = [];
                      this.frameSize = 960; // 60ms @ 16kHz = 960 samples
                      this.buffer = new Int16Array(this.frameSize);
                      this.bufferIndex = 0;
                      this.isRecording = false;

                      // 监听来自主线程的消息
                      this.port.onmessage = (event) => {
                          if (event.data.command === 'start') {
                              this.isRecording = true;
                              this.port.postMessage({ type: 'status', status: 'started' });
                          } else if (event.data.command === 'stop') {
                              this.isRecording = false;

                              // 发送剩余的缓冲区
                              if (this.bufferIndex > 0) {
                                  const finalBuffer = this.buffer.slice(0, this.bufferIndex);
                                  this.port.postMessage({
                                      type: 'buffer',
                                      buffer: finalBuffer
                                  });
                                  this.bufferIndex = 0;
                              }

                              this.port.postMessage({ type: 'status', status: 'stopped' });
                          }
                      };
                  }

                  process(inputs, outputs, parameters) {
                      if (!this.isRecording) return true;

                      const input = inputs[0][0]; // 获取第一个输入通道
                      if (!input) return true;

                      // 将浮点采样转换为16位整数并存储
                      for (let i = 0; i < input.length; i++) {
                          if (this.bufferIndex >= this.frameSize) {
                              // 缓冲区已满，发送给主线程并重置
                              this.port.postMessage({
                                  type: 'buffer',
                                  buffer: this.buffer.slice(0)
                              });
                              this.bufferIndex = 0;
                          }

                          // 转换为16位整数 (-32768到32767)
                          this.buffer[this.bufferIndex++] = Math.max(-32768, Math.min(32767, Math.floor(input[i] * 32767)));
                      }

                      return true;
                  }
              }

              registerProcessor('audio-recorder-processor', AudioRecorderProcessor);
          `], { type: 'application/javascript' });
          const url = URL.createObjectURL(blob);
          await this.audioContext.audioWorklet.addModule(url);
          URL.revokeObjectURL(url);
          audioWorkletRegistered = true;
        }
        // 创建音频处理节点
        this.audioProcessor = new AudioWorkletNode(this.audioContext, 'audio-recorder-processor');

        // 设置音频处理消息处理
        this.audioProcessor.port.onmessage = (event: any) => {
          if (event.data.type === 'buffer') {
            // 收到PCM缓冲区数据
            this.processPCMBuffer(event.data.buffer);
          }
        };

        console.log('使用AudioWorklet处理音频', 'success');
        return { node: this.audioProcessor, type: 'worklet' };
      } else {
        // 使用旧版ScriptProcessorNode作为回退方案
        console.log('AudioWorklet不可用，使用ScriptProcessorNode作为回退方案', 'warning');

        const frameSize = 4096; // ScriptProcessorNode缓冲区大小
        const scriptProcessor = this.audioContext.createScriptProcessor(frameSize, 1, 1);

        // 将audioProcess事件设置为处理音频数据
        scriptProcessor.onaudioprocess = (event) => {
          if (!this.isRecording) return;

          const input = event.inputBuffer.getChannelData(0);
          const buffer = new Int16Array(input.length);

          // 将浮点数据转换为16位整数
          for (let i = 0; i < input.length; i++) {
            buffer[i] = Math.max(-32768, Math.min(32767, Math.floor(input[i] * 32767)));
          }

          // 处理PCM数据
          this.processPCMBuffer(buffer);
        };

        // 需要连接输出，否则不会触发处理
        // 我们创建一个静音通道
        const silent = this.audioContext.createGain();
        silent.gain.value = 0;
        scriptProcessor.connect(silent);
        silent.connect(this.audioContext.destination);

        return { node: scriptProcessor, type: 'processor' };
      }
    } catch (error) {
      console.log(`创建音频处理器失败: ${error.message}，尝试回退方案`, 'error');

      // 最后回退方案：使用ScriptProcessorNode
      try {
        const frameSize = 4096; // ScriptProcessorNode缓冲区大小
        const scriptProcessor = this.audioContext.createScriptProcessor(frameSize, 1, 1);

        scriptProcessor.onaudioprocess = (event) => {
          if (!this.isRecording) return;

          const input = event.inputBuffer.getChannelData(0);
          const buffer = new Int16Array(input.length);

          for (let i = 0; i < input.length; i++) {
            buffer[i] = Math.max(-32768, Math.min(32767, Math.floor(input[i] * 32767)));
          }

          this.processPCMBuffer(buffer);
        };

        const silent = this.audioContext.createGain();
        silent.gain.value = 0;
        scriptProcessor.connect(silent);
        silent.connect(this.audioContext.destination);

        console.log('使用ScriptProcessorNode作为回退方案成功', 'warning');
        return { node: scriptProcessor, type: 'processor' };
      } catch (fallbackError) {
        console.log(`回退方案也失败: ${fallbackError.message}`, 'error');
        return null;
      }
    }
  }
  async start() {
    console.log('start 开始录音');
    try {
      // 检查libopus.js是否正确加载
      this.checkOpusLoaded();

      // 初始化Opus编码器
      this.initOpusEncoder();

      // 预加载Opus解码器
      console.log('预加载Opus解码器...', 'info');
      this.initOpusDecoder().then(() => {
        console.log('Opus解码器预加载成功', 'success');
      }).catch((error: any) => {
        console.log(`Opus解码器预加载失败: ${error.message}，将在需要时重试`, 'warning');
      });
      this.audioQueue = new AudioQueueManager();
      if(!this.websocket){
        this.websocket = getWebSocketManager('voice');
        await this.websocket.connect();
      }
      // 实时音频流播放：收到服务器音频帧直接播放
      if (this.websocket && this.websocket.ws) {
        this.websocket.ws.onmessage = (event: MessageEvent) => {
          if (event.data instanceof ArrayBuffer) {
            this.audioQueue?.addAudio(event.data);
          }
        };
      }
      this.resetInactivityTimer();
      this.stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 16000,
          channelCount: 1
        },
        video: false,
      });
      // 创建音频上下文和分析器
      if (!this.audioContext) {
        this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
          sampleRate: 16000,
          latencyHint: 'interactive'
        });
      }
      // 创建音频处理器
      const processorResult = await this.createAudioProcessor();
      if (!processorResult) {
        console.log('无法创建音频处理器', 'error');
        return;
      }
      this.audioProcessor = processorResult.node;
      this.audioProcessorType = processorResult.type;
      // 连接音频处理链
      this.audioSource = this.audioContext.createMediaStreamSource(this.stream);
      const analyser = this.audioContext.createAnalyser();
      analyser.fftSize = 2048;

      this.audioSource.connect(analyser);
      this.audioSource.connect(this.audioProcessor);

      // 启动录音
      this.pcmDataBuffer = new Int16Array();
      this.audioBuffers = [];
      this.totalAudioSize = 0;
      this.isRecording = true;

      // 启动音频处理器的录音 - 只有AudioWorklet才需要发送消息
      if (this.audioProcessorType === 'worklet' && this.audioProcessor.port) {
          this.audioProcessor.port.postMessage({ command: 'start' });
      }
      console.log('websocket', this.websocket);
      // 发送监听开始消息
      if (this.websocket && this.websocket.ws && this.websocket.ws.readyState === WebSocket.OPEN) {
          // 使用与服务端期望的listen消息格式
          const listenMessage = {
              type: 'listen',
              mode: 'manual',  // 使用手动模式，由我们控制开始/停止
              state: 'start'   // 表示开始录音
          };

          console.log(`发送录音开始消息: ${JSON.stringify(listenMessage)}`, 'info');
          this.websocket.ws.send(JSON.stringify(listenMessage));
      } else {
          console.log('WebSocket未连接，无法发送开始消息', 'error');
          return false;
      }
      // 通知主进程录音状态变化
      if (floatingBallClient.startVoiceInput) {
        floatingBallClient.startVoiceInput();
      }
      console.log('开始PCM直接录音', 'success');
    } catch (error) {
      console.error('麦克风访问失败:', error);
      throw error;
    }
  }

  stop() {
    console.log('stop 停止录音');
    this.mediaRecorder?.stop();
    this.stream?.getTracks().forEach(track => track.stop());
    this.audioQueue?.stop();
    // 通知主进程录音状态变化
    if (floatingBallClient.endVoiceInput) {
      floatingBallClient.endVoiceInput(new ArrayBuffer(0));
    }
    if (!this.isRecording) return;
    try {
        // 停止录音
        this.isRecording = false;

        // 停止音频处理器的录音
        if (this.audioProcessor) {
            // 只有AudioWorklet才需要发送停止消息
            if (this.audioProcessorType === 'worklet' && this.audioProcessor.port) {
                this.audioProcessor.port.postMessage({ command: 'stop' });
            }

            this.audioProcessor.disconnect();
            this.audioProcessor = null;
        }

        // 断开音频连接
        if (this.audioSource) {
            this.audioSource.disconnect();
            this.audioSource = null;
        }

        // 编码并发送剩余的数据
        this.encodeAndSendOpus();

        // 发送一个空的消息作为结束标志（模拟接收到空音频数据的情况）
        if (this.websocket && this.websocket.ws && this.websocket.ws.readyState === WebSocket.OPEN) {
            // 使用空的Uint8Array发送最后一个空帧
            const emptyOpusFrame = new Uint8Array(0);
            this.websocket.ws.send(emptyOpusFrame);

            // 发送监听结束消息
            const stopMessage = {
                type: 'listen',
                mode: 'manual',
                state: 'stop'
            };

            this.websocket.ws.send(JSON.stringify(stopMessage));
            console.log('已发送录音停止信号', 'info');
        }

        console.log('停止PCM直接录音', 'success');
        return true;
    } catch (error) {
        console.log(`直接录音停止错误: ${error.message}`, 'error');
        return false;
    }
    
  }
  private resetInactivityTimer() {
    if (this.inactivityTimer) {
      clearTimeout(this.inactivityTimer);
    }
    this.inactivityTimer = setTimeout(() => {
      this.handleInactivityTimeout();
    }, this.INACTIVITY_TIMEOUT);
  }

  private handleInactivityTimeout() {
    console.log('5分钟无说话，主动断开WebSocket连接', 'info');
    setWebSocketDisableAutoReconnect(true); // 禁止自动重连
    if (this.websocket && this.websocket.ws) {
      this.websocket.ws.close();
      this.websocket = null;
    }
    this.stop();
  }
}