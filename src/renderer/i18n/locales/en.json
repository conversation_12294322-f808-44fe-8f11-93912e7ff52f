{"app": {"title": "<PERSON><PERSON>", "description": "AI-powered quick launcher"}, "search": {"placeholder": "Search for apps/skills, or enter prompt to talk to AI/operate the computer", "noResults": "No results found", "loading": "Loading...", "fileSearch": "Search files...", "aiPrompt": "Ask Aido AI...", "appSearch": "Search applications...", "recentApps": "Recent applications"}, "apps": {"launch": "Launch", "openLocation": "Open location", "setShortcut": "Set shortcut", "showInFinder": "Show in Finder", "showInExplorer": "Show in Explorer", "uninstall": "Uninstall", "properties": "Properties"}, "tools": {"title": "Tools", "useTools": "Use tools", "network": "Network access", "deepThinking": "Deep thinking", "voice": "Voice", "pin": "Pin window", "settings": "Settings", "devTools": "Developer Tools"}, "settings": {"title": "Settings", "language": "Language", "theme": "Theme", "loading": "Loading settings...", "updateFailed": "Failed to update settings", "menu": {"general": "General", "clipboard": "Clipboard History", "ai": "AI", "desktop": "Desktop Assistant", "pet": "Desktop Pet", "cloud": "Cloud Sync", "account": "Account", "about": "About", "mcpcn": "Mcpcn"}, "general": {"autoStart": "Launch at startup", "showTray": "Show system tray icon", "activationHotkey": "Activation hotkey", "interfaceLanguage": "Interface language", "windowScreen": "Window display screen", "mouseScreen": "Mouse cursor screen", "mainScreen": "Main screen", "appearance": "Appearance theme", "permissions": "System permissions", "accessibilityAccess": "Accessibility access", "screenRecording": "Screen recording permission", "fullDiskAccess": "Full disk access permission", "authorized": "Authorized", "authorize": "Authorize", "windowStyle": "Window style", "complete": "Complete", "simple": "Simple", "leftBar": "Left sidebar", "rightBar": "Right sidebar"}, "clipboard": {"activationHotkey": "Activation hotkey", "historyCount": "History record count", "memberUnlimited": "Members can enable unlimited records", "freeTrial": "Free 14-day member trial", "historyCountTooltip": "{{count}} records", "displayPosition": "Display position", "bottomBar": "Bottom bar", "centerWindow": "Main window", "phoneSync": "Phone sync", "linkPhoneSync": "Click here to scan QR code and add clipboard service", "phoneSyncDemo": "(Feature demo)", "phoneSyncDescription": "Messages sent to clipboard service will be automatically synced to computer clipboard"}, "desktop": {"floatingBall": "Floating Ball Assistant", "floatingBallDemo": "(Feature demo)", "dynamicDesktop": "VIP Dynamic Desktop Assistant", "freeTrial": "Free 14-day member trial", "assistantSettings": "Assistant Character Settings", "assistantName": "Character Name:", "voiceCharacter": "Voice Character:", "modelSelect": "Model:", "lastInteraction": "Last Interaction:", "modifySettings": "Modify Settings", "characterPersonality": "Character Personality Settings:", "personalitySettings": "Personality Settings:", "characterSetting": "Character Settings", "characterMemory": "Character Memory:", "mainPersonality": "Owner identification:", "saveCharacterInfo": "Save Character Info", "timesAgo": "{{time}} ago", "minutes": "minutes", "hours": "hours", "days": "days", "customVoiceColors": "Customize Voice Colors >", "addMainPersonality": "Add Owner VocalPrint"}, "appearance": {"title": "Appearance", "light": "Light", "dark": "Dark", "system": "System"}, "hotkeys": {"title": "Hotkeys", "activation": "Activate", "hide": "<PERSON>de", "pressKey": "Press keys..."}, "about": {"title": "About", "version": "Version"}, "pet": {"title": "Desktop Pet", "addPet": "Add Pet", "purchase": "Purchase", "characterVoice": "Character Voice", "lastConversation": "Last Conversation", "serialNumber": "Serial Number", "modifySettings": "Modify Settings"}, "account": {"title": "Account <PERSON><PERSON>", "loginFirst": "Please login first", "loginFirstDescription": "Login to enjoy more features", "unknownUser": "Unknown User", "professionalVersion": "Pro", "expiresOn": "Pro expires on {{date}}", "renewNow": "Renew Now", "upgradeToPro": "Upgrade to Pro", "loginNow": "Login Now", "myDevices": "My Devices", "unnamedDevice": "Unnamed Device", "currentDevice": "Current Device", "online": "Online", "offline": "Offline", "logout": "Logout", "logoutAccount": "<PERSON><PERSON><PERSON> Account", "updateDeviceNameSuccess": "Device name updated successfully", "updateDeviceNameFailed": "Failed to update device name", "logoutSuccess": "Logout successful", "macOS": "macOS", "windows": "Windows", "other": "Other"}}, "filePreview": {"open": "Open", "openWith": "Open with", "openLocation": "Open location", "copy": "Copy path", "rename": "<PERSON><PERSON>", "delete": "Delete", "loading": "Loading...", "noFileSelected": "No file selected", "searching": "Searching...", "noResults": "No files found", "resultsCount": "Found {{count}} files", "searchFilesPlaceholder": "Search files...", "recentFiles": "Recent files", "fileSize": "File size", "modified": "Modified", "created": "Created", "fileType": "File type", "back": "Back"}, "ai": {"thinking": "Thinking...", "processing": "Processing...", "welcomeMessage": "Hello! How can I help you?", "errorMessage": "Sorry, I encountered an error. Please try again.", "rapidAI": "Rapid AI", "user": "Me", "assistant": "AI Assistant", "sending": "Sending...", "received": "Received", "allowedFolders": "Folders allowed for AI operations:", "addDirectory": "Add Directory", "select": "Select", "toolConfirmation": "AI needs my confirmation when using tools:", "installConfirmation": "AI needs my confirmation when installing tools:", "folderPathPlaceholder": "Enter folder path"}, "errors": {"appScan": "Failed to scan applications", "appLaunch": "Failed to launch application", "iconExtract": "Failed to extract application icon", "fileRead": "Failed to read file", "fileSearch": "Failed to search files", "fileOpen": "Failed to open file"}, "common": {"back": "Back", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "edit": "Edit", "delete": "Delete", "loading": "Loading..."}, "clipboard": {"history": "Clipboard History", "searchPlaceholder": "Search clipboard history...", "empty": "Clipboard history is empty", "noMatches": "No matching content found"}, "translate": {"title": "Translate", "description": "Translate text content", "sourceLanguage": "Source Language", "targetLanguage": "Target Language", "autoDetect": "Auto Detect", "selectLanguage": "Select Language", "inputPlaceholder": "Enter text to translate...", "outputPlaceholder": "Translation result will appear here", "translating": "Translating...", "uploadFile": "Upload File", "download": "Download", "copied": "<PERSON>pied", "copiedDescription": "Translation result copied to clipboard", "translateFailed": "Translation Failed", "translateFailedDescription": "Translation service is temporarily unavailable, please try again later", "getLanguagesFailed": "Error", "getLanguagesFailedDescription": "Failed to get language list, please try again later", "fileTooLarge": "File Too Large", "fileTooLargeDescription": "File content has been truncated to first 5000 characters"}, "screenshot": {"title": "Screenshot", "description": "Capture screen content"}}