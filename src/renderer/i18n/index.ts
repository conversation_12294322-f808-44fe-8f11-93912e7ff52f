import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// 导入语言包
import enTranslation from './locales/en.json';
import zhTranslation from './locales/zh.json';

const resources = {
  en: {
    translation: enTranslation
  },
  zh: {
    translation: zhTranslation
  }
};

// 从localStorage中获取语言设置，如果没有则使用浏览器语言
const getStoredLanguage = () => {
  const storedLang = localStorage.getItem('aido-language');
  return storedLang || navigator.language.split('-')[0];
};

i18n
  // 使用语言检测器
  .use(LanguageDetector)
  // 将i18next传递给react-i18next
  .use(initReactI18next)
  // 初始化i18next
  .init({
    resources,
    lng: getStoredLanguage(),
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false // 不需要转义HTML
    },
    detection: {
      order: ['localStorage', 'navigator'],
      lookupLocalStorage: 'aido-language',
      caches: ['localStorage']
    }
  });

// 导出语言切换函数
export const changeLanguage = (lng: string) => {
  return i18n.changeLanguage(lng);
};

// 获取当前语言
export const getCurrentLanguage = () => {
  return i18n.language;
};

// 支持的语言列表
export const supportedLanguages = [
  { code: 'en', name: 'English' },
  { code: 'zh', name: '中文' }
];

export default i18n; 