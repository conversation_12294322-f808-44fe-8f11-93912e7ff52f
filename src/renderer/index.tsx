import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON>hRouter } from 'react-router-dom';
import AppRoutes from './routes';
import './globals.css';
import './i18n';
import { ToastProvider } from './components/ui/use-toast';

/**
 * 应用入口
 * 渲染根组件
 */
const rootElement = document.getElementById('root');
if (!rootElement) {
  throw new Error('Root element not found');
}

// 使用HashRouter和AppRoutes组件
ReactDOM.createRoot(rootElement).render(
  <React.StrictMode>
    <HashRouter>
      <ToastProvider>
        <AppRoutes />
      </ToastProvider>
    </HashRouter>
  </React.StrictMode>
);