import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

/**
 * 合并类名工具函数，结合clsx和tailwind-merge
 * 允许有条件地应用类名，并解决tailwind类名冲突
 */
export function cn(...inputs: ClassValue[]) {
    return twMerge(clsx(inputs))
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
    fn: T,
    ms: number = 500
  ): (...args: Parameters<T>) => void {
    let timeoutId: ReturnType<typeof setTimeout>;
    
    return function(...args: Parameters<T>) {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => fn(...args), ms);
    };
  }
  
  /**
   * 节流函数
   */
  export function throttle<T extends (...args: any[]) => any>(
    fn: T,
    ms: number = 500
  ): (...args: Parameters<T>) => void {
    let inThrottle = false;
    let lastFn: ReturnType<typeof setTimeout>;
    let lastTime = 0;
    
    return function(...args: Parameters<T>) {
      if (!inThrottle) {
        fn(...args);
        lastTime = Date.now();
        inThrottle = true;
      } else {
        clearTimeout(lastFn);
        lastFn = setTimeout(() => {
          if (Date.now() - lastTime >= ms) {
            fn(...args);
            lastTime = Date.now();
          }
        }, Math.max(ms - (Date.now() - lastTime), 0));
      }
    };
  }