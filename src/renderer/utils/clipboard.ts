import React from 'react';
import { 
  FileText,
  Image as ImageIcon,
  File,
  Folder,
  Terminal,
} from 'lucide-react';
import { ClipboardItem } from '../../shared/types/clipboard';

/**
 * 获取剪贴板项目的内容文本
 */
export const getItemContent = (item: ClipboardItem): string => {
  if (item.items.length === 0) return '';
  const firstItem = item.items[0];
  
  // 图片类型
  if (item.type === 'image') {
    return (firstItem as any).name || '图片';
  }
  
  // 其他类型
  if ('content' in firstItem) {
    return firstItem.content || '';
  }
  if ('name' in firstItem) {
    return firstItem.name || '';
  }
  return '';
};

/**
 * 格式化时间显示
 */
export const formatTime = (timestamp: number): string => {
  const diff = Date.now() - timestamp;
  const minutes = Math.floor(diff / (1000 * 60));
  if (minutes < 1) return '刚刚';
  if (minutes < 60) return `${minutes}分钟前`;
  return `${Math.floor(minutes / 60)}小时前`;
};

/**
 * 获取剪贴板项目的图标
 */
export const getIcon = (item: ClipboardItem, iconClassName: string = 'w-5 h-5'): React.ReactElement => {
  const iconProps = { className: iconClassName };
  
  switch (item.type) {
    case 'text':
      return React.createElement(FileText, iconProps);
    case 'image':
      return React.createElement(ImageIcon, iconProps);
    case 'file':
      return React.createElement(File, iconProps);
    case 'directory':
      return React.createElement(Folder, iconProps);
    default:
      return React.createElement(Terminal, iconProps);
  }
};

/**
 * 获取文件/图片信息
 */
export const getFileInfo = (item: ClipboardItem) => {
  if (item.type === 'file' && item.items.length > 0) {
    const fileItem = item.items[0] as any;
    return {
      fileType: fileItem.type || '',
      size: fileItem.size || 0,
      thumbnail: fileItem.thumbnail
    };
  } else if (item.type === 'image' && item.items.length > 0) {
    const imageItem = item.items[0] as any;
    return {
      fileType: 'image',
      size: imageItem.size || 0,
      thumbnail: imageItem.dataURL // 图片类型直接使用dataURL作为缩略图
    };
  }
  return { fileType: '', size: 0, thumbnail: undefined };
};

/**
 * 判断是否为图片文件
 */
export const isImageFile = (item: ClipboardItem): boolean => {
  if (item.type === 'image') return true;
  
  if (item.type === 'file' && item.items.length > 0) {
    const fileItem = item.items[0] as any;
    const fileName = fileItem.name || fileItem.content || '';
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.ico', '.tiff', '.tif'];
    return imageExtensions.some(ext => fileName.toLowerCase().endsWith(ext));
  }
  
  return false;
};

/**
 * 检测图片是否为低质量
 */
export const isLowQualityImage = (item: ClipboardItem): boolean => {
  if ((item.type === 'image' || isImageFile(item)) && item.items.length > 0) {
    const imageItem = item.items[0] as any;
    // 检查文件名是否包含"低质量"标记
    if (imageItem.name && imageItem.name.includes('[低质量]')) {
      return true;
    }
    // 检查content是否为低质量提示
    if (imageItem.content && imageItem.content.includes('低质量图片')) {
      return true;
    }
  }
  return false;
};

/**
 * 过滤剪贴板历史数据
 */
export const filterClipboardData = (history: ClipboardItem[], searchQuery: string): ClipboardItem[] => {
  if (!searchQuery.trim()) return history;
  
  return history.filter(item => {
    const content = getItemContent(item);
    return content.toLowerCase().includes(searchQuery.toLowerCase());
  });
};

/**
 * 处理剪贴板项目双击选择
 */
export const handleClipboardItemSelect = async (item: ClipboardItem): Promise<void> => {
  try {
    await window.electron.clipboardHistory.selectItem(item);
  } catch (error) {
    console.error('选择剪贴板项目失败:', error);
    throw error;
  }
};