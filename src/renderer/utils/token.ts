import {
  encoding_for_model as encoding<PERSON>or<PERSON>ode<PERSON>,
  TiktokenModel,
  Tiktoken,
} from 'tiktoken';
import { get_encoding as getEncoding } from 'tiktoken/init';
import { IChatRequestMessage } from '../types/llm';
import { tokenCountApi } from '../api/thirdParty';

let llama3Tokenizer: any;
let llamaTokenizer: any;

(async () => {
  llama3Tokenizer = (await import('llama3-tokenizer-js')).default;
  llamaTokenizer = (await import('llama-tokenizer-js')).default;
})();

export function countGPTTokens(messages: IChatRequestMessage[], model: string) {
  let _model = model;
  if (model.startsWith('gpt-3.5') || model.startsWith('gpt-35')) {
    _model = 'gpt-3.5-turbo-0613';
  } else if (model.startsWith('gpt-4')) {
    _model = 'gpt-4-0613';
  }
  let encoding: Tiktoken;
  try {
    encoding = encodingForModel(_model as TiktokenModel);
  } catch (err) {
    console.warn('Model not found. Using cl100k_base encoding.');
    encoding = getEncoding('cl100k_base');
  }
  const tokensPerMessage = 3;
  const tokensPerName = 1;
  let numTokens = 0;

  messages.forEach((msg: any) => {
    numTokens += tokensPerMessage;
    Object.keys(msg).forEach((key: string) => {
      numTokens += encoding.encode(msg[key] as string).length;
      if (key === 'name') {
        numTokens += tokensPerName;
      }
    });
  });
  numTokens += 3; // For assistant prompt
  return numTokens;
}

export async function countTokensOfGemini(
  messages: IChatRequestMessage[],
  apiBase: string,
  apiKey: string,
  model: string,
) {
  try {
    const response = await tokenCountApi.gemini(apiBase, apiKey, model, messages);
    return response.data.totalTokens;
  } catch (err: any) {
    console.error('Gemini token count error:', err);
    return 0;
  }
}

export async function countTokensOfMoonshot(
  messages: IChatRequestMessage[],
  apiBase: string,
  apiKey: string,
  model: string,
) {
  try {
    const response = await tokenCountApi.moonshot(apiBase, apiKey, model, messages);
    return response.data.data.total_tokens;
  } catch (err: any) {
    console.error('Moonshot token count error:', err);
    return 0;
  }
}

export async function countTokenOfLlama(
  messages: IChatRequestMessage[],
  model: string,
) {
  const tokensPerMessage = 3;
  const tokensPerName = 1;
  let numTokens = 0;
  const tokenizer = model.startsWith('llama3')
    ? llama3Tokenizer
    : llamaTokenizer;
  messages.forEach((msg: any) => {
    numTokens += tokensPerMessage;
    Object.keys(msg).forEach((key: string) => {
      numTokens += tokenizer.encode(msg[key] as string).length;
      if (key === 'name') {
        numTokens += tokensPerName;
      }
    });
  });
  return numTokens;
}
