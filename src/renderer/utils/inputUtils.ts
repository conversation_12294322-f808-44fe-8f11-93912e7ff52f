/**
 * 输入处理工具函数
 * 统一处理颜色、数学计算、IP地址等特殊输入类型
 */

import * as math from 'mathjs';
import deviceApi from '../api/device';

// ==================== 类型定义 ====================
export type InputType = '颜色' | '手机号码' | 'IP地址' | '数学计算' | '其他';

export interface ColorFormats {
  rgb: string;
  css: string;
  cmyk: string;
  hsl: string;
  hsv: string;
}

export interface ColorResult {
  rgb: [number, number, number];
  formats: ColorFormats;
}

export interface MathResult {
  expression: string;
  result: number;
  resultText: string;
  chineseNumber: string;
  rmbText: string;
  isSpecial?: boolean;
  specialType?: 'integral' | 'derivative';
  specialResult?: string;
}

export interface IpLocationInfo {
  ip: string;
  country?: string;
  province?: string;
  city?: string;
  district?: string;
  isp?: string;
  location?: string;
  timezone?: string;
  zipcode?: string;
  lat?: number;
  lng?: number;
  areacode?: string;
}

export interface IpLocationResult {
  ip: string;
  locationText: string;
  detailInfo: IpLocationInfo;
}

// ==================== 输入类型识别 ====================
export function classifyInput(input: string): InputType {
  const str = input.trim();

  // 颜色检测（十六进制格式）
  if (/^#([A-Fa-f0-9]{3}|[A-Fa-f0-9]{6})$/.test(str)) return '颜色';

  // 颜色检测（RGB格式）
  if (/^rgb\(\s*(25[0-5]|2[0-4]\d|1\d{2}|\d{1,2})\s*,\s*(25[0-5]|2[0-4]\d|1\d{2}|\d{1,2})\s*,\s*(25[0-5]|2[0-4]\d|1\d{2}|\d{1,2})\s*\)$/i.test(str)) {
    return '颜色';
  }

  // 手机号码检测（中国）
  if (/^1[3-9]\d{9}$/.test(str)) return '手机号码';

  // IP地址检测（IPv4）
  if (/^(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)){3}$/.test(str)) {
    return 'IP地址';
  }

  // 数学表达式检测
  if (isMathExpression(str)) return '数学计算';

  return '其他';
}

// ==================== 颜色处理 ====================
/**
 * 解析十六进制颜色
 */
function parseHexColor(hex: string): [number, number, number] {
  const cleanHex = hex.replace('#', '');

  let r: number, g: number, b: number;
  if (cleanHex.length === 3) {
    r = parseInt(cleanHex[0] + cleanHex[0], 16);
    g = parseInt(cleanHex[1] + cleanHex[1], 16);
    b = parseInt(cleanHex[2] + cleanHex[2], 16);
  } else {
    r = parseInt(cleanHex.slice(0, 2), 16);
    g = parseInt(cleanHex.slice(2, 4), 16);
    b = parseInt(cleanHex.slice(4, 6), 16);
  }
  
  return [r, g, b];
}

/**
 * 解析 RGB 颜色字符串
 */
function parseRgbColor(rgb: string): [number, number, number] {
  const match = rgb.match(/rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/i);
  if (!match) return [0, 0, 0];
  
  return [parseInt(match[1]), parseInt(match[2]), parseInt(match[3])];
}

/**
 * RGB 转 HSL
 */
function rgbToHsl(r: number, g: number, b: number): [number, number, number] {
  r /= 255;
  g /= 255;
  b /= 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0, s = 0;
  const l = (max + min) / 2;

  if (max === min) {
    h = s = 0; // achromatic
  } else {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
    
    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
    }
    h /= 6;
  }

  return [Math.round(h * 360), Math.round(s * 100), Math.round(l * 100)];
}

/**
 * RGB 转 HSV
 */
function rgbToHsv(r: number, g: number, b: number): [number, number, number] {
  r /= 255;
  g /= 255;
  b /= 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0, s = 0;
  const v = max;

  const d = max - min;
  s = max === 0 ? 0 : d / max;

  if (max === min) {
    h = 0; // achromatic
  } else {
    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
    }
    h /= 6;
  }

  return [Math.round(h * 360), Math.round(s * 100), Math.round(v * 100)];
}

/**
 * RGB 转 CMYK
 */
function rgbToCmyk(r: number, g: number, b: number): [number, number, number, number] {
  if (r === 0 && g === 0 && b === 0) {
    return [0, 0, 0, 100];
  }

  const rPercent = r / 255;
  const gPercent = g / 255;
  const bPercent = b / 255;

  const k = 1 - Math.max(rPercent, Math.max(gPercent, bPercent));
  const c = (1 - rPercent - k) / (1 - k);
  const m = (1 - gPercent - k) / (1 - k);
  const y = (1 - bPercent - k) / (1 - k);

  return [
    Math.round(c * 100),
    Math.round(m * 100),
    Math.round(y * 100),
    Math.round(k * 100)
  ];
}

/**
 * 获取颜色的所有格式
 */
export function getColorFormats(color: string): ColorResult {
  let rgb: [number, number, number];
  
  if (color.startsWith('#')) {
    rgb = parseHexColor(color);
  } else if (color.startsWith('rgb')) {
    rgb = parseRgbColor(color);
  } else {
    rgb = [0, 0, 0];
  }

  const [r, g, b] = rgb;
  const [h, s, l] = rgbToHsl(r, g, b);
  const [hv, sv, v] = rgbToHsv(r, g, b);
  const [c, m, y, k] = rgbToCmyk(r, g, b);

  const formats: ColorFormats = {
    rgb: `RGB | ${r},${g},${b}`,
    css: `CSS | rgba(${r},${g},${b},1)`,
    cmyk: `CMYK | ${c},${m},${y},${k}`,
    hsl: `HSL | ${h},${s},${l}`,
    hsv: `HSV | ${hv},${sv},${v}`
  };

  return { rgb, formats };
}

// ==================== IP地址处理 ====================
/**
 * 查询IP地址归属地信息
 */
export async function getIpLocation(ip: string): Promise<IpLocationResult> {
  try {
    const response = await deviceApi.ipLocation(ip);
    
    console.log('IP归属地查询结果:', response.data);
    // API直接返回IP归属地信息，不需要检查code
    const data = response.data as IpLocationInfo;
      
      // 构建位置文本
      let locationText = '';
      const locationParts: string[] = [];
      
      if (data.country) locationParts.push(data.country);
      if (data.province && data.province !== data.country) locationParts.push(data.province);
      if (data.city && data.city !== data.province) locationParts.push(data.city);
      if (data.district && data.district !== data.city) locationParts.push(data.district);
      
      locationText = locationParts.join(' ');
      
      if (data.isp) {
        locationText += ` (${data.isp})`;
      }
      
      if (!locationText.trim()) {
        locationText = '未知位置';
      }
      
      return {
        ip,
        locationText,
        detailInfo: data
      };
  } catch (error) {
    console.error('IP归属地查询错误:', error);
    throw new Error(error instanceof Error ? error.message : 'IP归属地查询失败');
  }
}

// ==================== 数学表达式处理 ====================
function isMathExpression(s: string): boolean {
  // 排除纯数字（含符号和小数）
  if (/^[+-]?\d+(\.\d+)?$/.test(s)) return false;
  
  // 必须包含运算符或括号
  if (!/[+\-*/^()]/.test(s)) return false;
  
  // 基本语法检查：括号匹配
  if (!isValidSyntax(s)) return false;
  
  // 扩展的数学函数列表
  const mathFunctions = [
    'integrate', 'derivative', 'diff', 'sum', 'limit', 'lim',
    'sqrt', 'sin', 'cos', 'tan', 'sec', 'csc', 'cot',
    'asin', 'acos', 'atan', 'atan2', 'asec', 'acsc', 'acot',
    'sinh', 'cosh', 'tanh', 'sech', 'csch', 'coth',
    'asinh', 'acosh', 'atanh', 'asech', 'acsch', 'acoth',
    'log', 'ln', 'exp', 'pow', 'power',
    'abs', 'floor', 'ceil', 'round', 'max', 'min', 'sign', 'signum',
    'gamma', 'beta', 'factorial', 'fact', 'choose', 'binomial',
    'bessel', 'besselj', 'bessely', 'besseli', 'besselk',
    'erf', 'erfc', 'erfi', 'dawson',
    'pi', 'e', 'euler', 'inf', 'infinity', 'nan',
    'real', 'imag', 'conj', 'conjugate', 'arg', 'angle', 'phase',
    'mean', 'median', 'mode', 'std', 'var', 'variance',
    'solve', 'roots', 'fsolve', 'nsolve',
    'det', 'inv', 'transpose', 'trace', 'rank', 'norm',
    'series', 'taylor', 'fourier', 'laplace',
    'gcd', 'lcm', 'mod', 'remainder', 'floor_div'
  ];
  
  // 移除所有已知的数学函数名
  let cleanStr = s.toLowerCase();
  mathFunctions.forEach(func => {
    cleanStr = cleanStr.replace(new RegExp('\\b' + func + '\\b', 'g'), '');
  });
  
  // 检查剩余字符是否都是合法的
  return !/[^0-9+\-*/^()\s.,"'a-z_:;=<>!|&[\]]/.test(cleanStr);
}

// 检查基本语法是否正确
function isValidSyntax(s: string): boolean {
  // 检查括号匹配
  const brackets = { '(': ')', '[': ']', '{': '}' };
  const stack: string[] = [];
  
  for (let i = 0; i < s.length; i++) {
    const char = s[i];
    
    if (brackets[char as keyof typeof brackets]) {
      stack.push(char);
    } else if (Object.values(brackets).includes(char)) {
      if (stack.length === 0) return false;
      const lastOpen = stack.pop();
      if (brackets[lastOpen as keyof typeof brackets] !== char) {
        return false;
      }
    }
  }
  
  if (stack.length > 0) return false;
  
  const trimmed = s.trim();
  if (/[+\-*/^,]$/.test(trimmed)) return false;
  
  if (/[+*/^]{2,}/.test(s)) return false;
  if (/[+*/^]\s*[+*/^]/.test(s)) return false;
  
  return checkFunctionParameters(s);
}

// 检查数学函数是否有正确的参数格式
function checkFunctionParameters(s: string): boolean {
  const functionsNeedingParams = [
    'integrate', 'derivative', 'diff', 'sum', 'limit', 'lim',
    'sqrt', 'sin', 'cos', 'tan', 'sec', 'csc', 'cot',
    'asin', 'acos', 'atan', 'atan2', 'asec', 'acsc', 'acot',
    'sinh', 'cosh', 'tanh', 'sech', 'csch', 'coth',
    'asinh', 'acosh', 'atanh', 'asech', 'acsch', 'acoth',
    'log', 'ln', 'exp', 'pow', 'power',
    'abs', 'floor', 'ceil', 'round', 'max', 'min', 'sign', 'signum',
    'gamma', 'beta', 'factorial', 'fact', 'choose', 'binomial',
    'bessel', 'besselj', 'bessely', 'besseli', 'besselk',
    'erf', 'erfc', 'erfi', 'dawson',
    'real', 'imag', 'conj', 'conjugate', 'arg', 'angle', 'phase',
    'mean', 'median', 'mode', 'std', 'var', 'variance',
    'solve', 'roots', 'fsolve', 'nsolve',
    'det', 'inv', 'transpose', 'trace', 'rank', 'norm',
    'series', 'taylor', 'fourier', 'laplace',
    'gcd', 'lcm', 'mod', 'remainder', 'floor_div'
  ];
  
  for (const func of functionsNeedingParams) {
    const funcRegex = new RegExp('\\b' + func + '\\b', 'gi');
    const matches = s.match(funcRegex);
    
    if (matches) {
      const funcWithParamsRegex = new RegExp('\\b' + func + '\\s*\\(', 'gi');
      const paramMatches = s.match(funcWithParamsRegex);
      
      if (!paramMatches || matches.length !== paramMatches.length) {
        return false;
      }
    }
  }
  
  return true;
}

/**
 * 数值积分函数 - 使用辛普森规则
 */
function numericalIntegrate(expression: string, variable: string, a: number, b: number, n: number = 1000): number {
  if (n % 2 !== 0) n++;

  const h = (b - a) / n;
  let sum = 0;

  const scope: any = {};

  for (let i = 0; i <= n; i++) {
    const x = a + i * h;
    scope[variable] = x;

    const y = math.evaluate(expression, scope);

    if (i === 0 || i === n) {
      sum += y;
    } else if (i % 2 === 1) {
      sum += 4 * y;
    } else {
      sum += 2 * y;
    }
  }

  return (h / 3) * sum;
}

/**
 * 解析积分表达式
 */
function parseIntegrate(expression: string): { expression: string; variable: string; lower: number; upper: number } | null {
  const patterns = [
    /integrate\s*\(\s*"([^"]+)"\s*,\s*"([^"]+)"\s*,\s*(-?\d+(?:\.\d+)?)\s*,\s*(-?\d+(?:\.\d+)?)\s*\)/i,
    /integrate\s*\(\s*'([^']+)'\s*,\s*'([^']+)'\s*,\s*(-?\d+(?:\.\d+)?)\s*,\s*(-?\d+(?:\.\d+)?)\s*\)/i,
    /integrate\s*\(\s*([^,]+)\s*,\s*([^,]+)\s*,\s*(-?\d+(?:\.\d+)?)\s*,\s*(-?\d+(?:\.\d+)?)\s*\)/i,
    /∫\s*\[(-?\d+(?:\.\d+)?)\s*→\s*(-?\d+(?:\.\d+)?)\]\s*([^d]+)\s*d([a-zA-Z])/i
  ];

  for (const pattern of patterns) {
    const match = expression.match(pattern);
    if (match) {
      if (pattern.source.includes('∫')) {
        return {
          expression: match[3].trim(),
          variable: match[4].trim(),
          lower: parseFloat(match[1]),
          upper: parseFloat(match[2])
        };
      } else {
        return {
          expression: match[1].trim(),
          variable: match[2].trim(),
          lower: parseFloat(match[3]),
          upper: parseFloat(match[4])
        };
      }
    }
  }

  return null;
}

/**
 * 解析导数表达式
 */
function parseDerivative(expression: string): { expression: string; variable: string } | null {
  const patterns = [
    /derivative\s*\(\s*"([^"]+)"\s*,\s*"([^"]+)"\s*\)/i,
    /derivative\s*\(\s*'([^']+)'\s*,\s*'([^']+)'\s*\)/i,
    /derivative\s*\(\s*([^,]+)\s*,\s*([^,]+)\s*\)/i,
    /diff\s*\(\s*"([^"]+)"\s*,\s*"([^"]+)"\s*\)/i,
    /diff\s*\(\s*'([^']+)'\s*,\s*'([^']+)'\s*\)/i,
    /diff\s*\(\s*([^,]+)\s*,\s*([^,]+)\s*\)/i,
    /d\/d([a-zA-Z])\s*\(\s*([^)]+)\s*\)/i
  ];

  for (const pattern of patterns) {
    const match = expression.match(pattern);
    if (match) {
      if (pattern.source.includes('d\/d')) {
        return {
          expression: match[2].trim(),
          variable: match[1].trim()
        };
      } else {
        return {
          expression: match[1].trim(),
          variable: match[2].trim()
        };
      }
    }
  }

  return null;
}

/**
 * 计算符号导数
 */
function calculateDerivative(expression: string, variable: string): string {
  try {
    const expr = math.parse(expression);
    const derivative = math.derivative(expr, variable);
    return derivative.toString();
  } catch (error) {
    throw new Error(`导数计算失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

/**
 * 数字转中文
 */
function numberToChinese(num: number): string {
  if (num === 0) return '零';

  const digits = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
  const units = ['', '十', '百', '千', '万', '十万', '百万', '千万', '亿'];

  const numStr = Math.abs(Math.floor(num)).toString();
  let result = '';

  for (let i = 0; i < numStr.length; i++) {
    const digit = parseInt(numStr[i]);
    const unitIndex = numStr.length - 1 - i;

    if (digit !== 0) {
      result += digits[digit] + (unitIndex > 0 ? units[unitIndex] : '');
    } else if (result && !result.endsWith('零')) {
      result += '零';
    }
  }

  // 处理小数部分
  if (num % 1 !== 0) {
    const decimal = num.toString().split('.')[1];
    result += '点';
    for (const d of decimal) {
      result += digits[parseInt(d)];
    }
  }

  return (num < 0 ? '负' : '') + result;
}

/**
 * 数字转人民币大写
 */
function numberToRMB(num: number): string {
  if (num === 0) return '零元整';

  const digits = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
  const units = ['', '拾', '佰', '仟', '万', '拾万', '佰万', '仟万', '亿'];

  const yuan = Math.floor(Math.abs(num));
  const jiao = Math.floor((Math.abs(num) * 10) % 10);
  const fen = Math.floor((Math.abs(num) * 100) % 10);

  let result = '';

  // 处理元
  if (yuan > 0) {
    const yuanStr = yuan.toString();
    for (let i = 0; i < yuanStr.length; i++) {
      const digit = parseInt(yuanStr[i]);
      const unitIndex = yuanStr.length - 1 - i;

      if (digit !== 0) {
        result += digits[digit] + (unitIndex > 0 ? units[unitIndex] : '');
      } else if (result && !result.endsWith('零')) {
        result += '零';
      }
    }
    result += '元';
  }

  // 处理角分
  if (jiao > 0) {
    result += digits[jiao] + '角';
  }
  if (fen > 0) {
    result += digits[fen] + '分';
  }

  if (jiao === 0 && fen === 0) {
    result += '整';
  }

  return (num < 0 ? '负' : '') + result;
}

/**
 * 解析数学表达式（主函数）
 */
export async function parseMathExpression(expression: string): Promise<MathResult> {
  try {
    // 检查是否是特殊表达式（积分或导数）
    const derivativeParams = parseDerivative(expression);
    if (derivativeParams) {
      const derivativeResult = calculateDerivative(derivativeParams.expression, derivativeParams.variable);
      return {
        expression,
        result: 0,
        resultText: `导数：${derivativeResult}`,
        chineseNumber: '符号结果',
        rmbText: '导数为符号表达式',
        isSpecial: true,
        specialType: 'derivative',
        specialResult: derivativeResult
      };
    }

    const integralParams = parseIntegrate(expression);
    if (integralParams) {
      const result = numericalIntegrate(
        integralParams.expression,
        integralParams.variable,
        integralParams.lower,
        integralParams.upper
      );

      const chineseNumber = numberToChinese(result);
      const rmbText = numberToRMB(result);

      return {
        expression,
        result,
        resultText: `∫[${integralParams.lower}→${integralParams.upper}] ${integralParams.expression} d${integralParams.variable} = ${result}`,
        chineseNumber,
        rmbText,
        isSpecial: true,
        specialType: 'integral',
        specialResult: `积分值：${result}`
      };
    }

    // 普通表达式计算
    let cleanExpression = expression.trim();
    if (cleanExpression.startsWith("计算：")) {
      cleanExpression = cleanExpression.substring(3).trim();
    }

    if (!cleanExpression) {
      throw new Error("数学表达式不能为空");
    }

    const result = math.evaluate(cleanExpression);

    if (typeof result !== "number" || !isFinite(result)) {
      throw new Error('计算结果无效');
    }

    const chineseNumber = numberToChinese(result);
    const rmbText = numberToRMB(result);

    return {
      expression,
      result,
      resultText: `${expression} = ${result}`,
      chineseNumber,
      rmbText
    };
  } catch (error) {
    console.error('数学表达式解析错误:', error);
    throw error;
  }
}
