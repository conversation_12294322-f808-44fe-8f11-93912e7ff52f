/**
 * 计算距离现在多久的时间工具函数
 */

/**
 * 计算时间差并返回友好的时间描述
 * @param timeStr 时间字符串或时间戳
 * @returns 友好的时间描述，如"2小时前"、"3天前"等
 */
export const calculateTimeAgo = (timeStr: string | number): string => {
  if (!timeStr) return '未知时间';
  
  let timestamp: number;
  
  // 如果是数字类型的时间戳
  if (typeof timeStr === 'number') {
    timestamp = timeStr;
    // 如果是秒级时间戳，转换为毫秒
    if (timestamp < 10000000000) {
      timestamp *= 1000;
    }
  } else {
    // 如果是时间戳字符串，转换为数字
    if (/^\d+$/.test(timeStr)) {
      timestamp = parseInt(timeStr);
      // 如果是秒级时间戳，转换为毫秒
      if (timestamp < 10000000000) {
        timestamp *= 1000;
      }
    } else {
      // 如果是ISO时间字符串，直接转换
      timestamp = new Date(timeStr).getTime();
    }
  }
  
  // 检查时间戳是否有效
  if (isNaN(timestamp)) {
    console.warn('calculateTimeAgo: 无效的时间戳', { timeStr, timestamp });
    return '未知时间';
  }
  
  const now = new Date().getTime();
  const diff = now - timestamp;
  
  // 如果时间戳无效或未来时间（允许5分钟的误差）
  if (diff < -300000 || isNaN(diff)) {
    console.warn('calculateTimeAgo: 时间差异常', { 
      timeStr, 
      timestamp, 
      now, 
      diff, 
      isFuture: diff < 0,
      isNaN: isNaN(diff)
    });
    return '未知时间';
  }
  
  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  const months = Math.floor(days / 30);
  const years = Math.floor(months / 12);
  
  if (years > 0) {
    return `${years}年前`;
  } else if (months > 0) {
    return `${months}个月前`;
  } else if (days > 0) {
    return `${days}天前`;
  } else if (hours > 0) {
    return `${hours}小时前`;
  } else if (minutes > 0) {
    return `${minutes}分钟前`;
  } else {
    return '刚刚';
  }
};

/**
 * 格式化时间字符串，支持多种输入格式
 * @param timeStr 时间字符串
 * @returns 格式化后的时间描述
 */
export const formatTime = (timeStr: string): string => {
  // 如果已经是格式化好的时间字符串（包含"小时"、"分钟"等），直接返回
  if (timeStr.includes('小时') || timeStr.includes('分钟') || timeStr.includes('天') || timeStr.includes('月') || timeStr.includes('年')) {
    return timeStr;
  }
  
  // 否则计算时间差
  return calculateTimeAgo(timeStr);
};

/**
 * 获取详细的时间差信息
 * @param timeStr 时间字符串或时间戳
 * @returns 包含详细时间信息的对象
 */
export const getDetailedTimeAgo = (timeStr: string | number) => {
  if (!timeStr) return { text: '未知时间', seconds: 0, minutes: 0, hours: 0, days: 0 };
  
  let timestamp: number;
  
  if (typeof timeStr === 'number') {
    timestamp = timeStr;
    if (timestamp < 10000000000) {
      timestamp *= 1000;
    }
  } else {
    if (/^\d+$/.test(timeStr)) {
      timestamp = parseInt(timeStr);
      if (timestamp < 10000000000) {
        timestamp *= 1000;
      }
    } else {
      timestamp = new Date(timeStr).getTime();
    }
  }
  
  // 检查时间戳是否有效
  if (isNaN(timestamp)) {
    console.warn('getDetailedTimeAgo: 无效的时间戳', { timeStr, timestamp });
    return { text: '未知时间', seconds: 0, minutes: 0, hours: 0, days: 0 };
  }
  
  const now = new Date().getTime();
  const diff = now - timestamp;
  
  // 如果时间戳无效或未来时间（允许5分钟的误差）
  if (diff < -300000 || isNaN(diff)) {
    console.warn('getDetailedTimeAgo: 时间差异常', { 
      timeStr, 
      timestamp, 
      now, 
      diff, 
      isFuture: diff < 0,
      isNaN: isNaN(diff)
    });
    return { text: '未知时间', seconds: 0, minutes: 0, hours: 0, days: 0 };
  }
  
  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  
  let text = '';
  if (days > 0) {
    text = `${days}天前`;
  } else if (hours > 0) {
    text = `${hours}小时前`;
  } else if (minutes > 0) {
    text = `${minutes}分钟前`;
  } else {
    text = '刚刚';
  }
  
  return {
    text,
    seconds,
    minutes,
    hours,
    days
  };
}; 