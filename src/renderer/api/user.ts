import { request } from "../../core/request";

export default {
  /**
   * 获取用户信息
   * @returns
   * */
  getUserInfo() {
    return request.get("/api/user/getUserInfo");
  },

  /**
   * 重置密码
   * @param params - { phone: string, code: string, newPassword: string }
   * @returns
   */
  changePassword(params: { phone: string; code: string; newPassword: string }) {
    return request.post("/api/user/changePassword", params);
  },
};