import { request } from "../../core/request";

export default {
  /**
   * 获取支持的语言列表
   * @returns
   */
  getLanguages() {
    return request.get("/api/translate/languages");
  },

  /**
   * 百度翻译
   * @param params { text: string, from: string, to: string }
   * @returns
   */
  baiduTranslate(params: { text: string; from?: string; to?: string }) {
    // from 默认为 'auto'，to 默认为 'zh'
    const data = {
      from: 'auto',
      to: 'zh',
      ...params,
    };
    return request.post("/api/translate/baiduTranslate", data);
  },
};