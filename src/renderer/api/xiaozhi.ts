import { request } from "../../core/request";

export default {
  /**
   * 保存电脑信息
   * @param params
   * @returns
   * */
  ota(params: any) {
    return request.post("/xiaozhi/ota/", params);
  },
  
  /**
   * 助手列表
   * @param params
   * @returns
   * */
  assistantList() {
    return request.get(`/xiaozhi/agent/list/no-auth`, null);
  },

  /**
   * 助手信息
   * @param params
   * @returns
   * */
  assistantInfo(deviceId: string) {
    return request.get(`/xiaozhi/agent/${deviceId}/no-auth`, null);
  },

  /**
   * 更新助手信息
   * @param params
   * @returns
   * */
  updateAssistant(params: any) {
    return request.put(`/xiaozhi/agent/${params.id}/no-auth`, params);
  },

  /**
   * 模型音色列表
   * @param params
   * @returns
   * */
  modelVoices(modelId: string) {
    return request.get(`/xiaozhi/models/${modelId}/voices/external`, null);
  },

  /**
   * 模型列表
   * @param params
   * @returns
   * */
  modelList(modelType: string) {
    return request.get(`/xiaozhi/models/names/external?modelType=${modelType}`, null);
  },

  /**
   * 智能体模版列表
   * @param params
   * @returns
   * */
  agentTemplateList() {
    return request.get(`/xiaozhi/agent/template/external`, null);
  },


};