import { request } from "../../core/request";

export default {
  /**
   * 统一登录
   * @param params
   * @returns
   * */
  unifiedLogin(params: any) {
    return request.post("/api/base/unified-login", params);
  },
  
  /**
   * 兼容旧版登录接口
   * @param params
   * @returns
   * */
  login(params: any) {
    return this.unifiedLogin(params);
  },

  /**
   * 发送短信验证码
   * @param params - { phone: string, smsType?: number }
   * @returns
   */
  sendSMSCode(params: { phone: string; smsType?: number }) {
    return request.post("/api/base/sendSMSCode", params);
  },

  /**
   * 手机号注册
   * @param params - { phone: string, password: string, code: string }
   * @returns
   */
  phoneRegister(params: { phone: string; password: string; code: string; validate: string }) {
    return request.post("/api/base/phoneRegister", params);
  },

  /**
   * 绑定微信手机号
   * @param params
   * @returns
   */
  bindWechatPhone(params: {
    phone: string;
    code: string;
    openId: string;
    unionId: string;
    nickName: string;
    avatar: string;
  }) {
    return request.post("/api/base/bindWechatPhone", params);
  },
};