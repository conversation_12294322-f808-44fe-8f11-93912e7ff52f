import { request } from "../../core/request";

export default {
  /**
   * 设备信息上报
   * @param params
   * @returns
   * */
  deviceReport(params: any) {
    return request.post("/api/device/report", params);
  },
  /**
   * 获取mcp接入点
   * @param params
   * @returns
   * */
  assignAgent() {
    return request.get("/api/device/assignAgent", null);
  },
  /**
   * 我的设备
   * @param params
   * @returns
   * */
  myDevices() {
    return request.get("/api/device/myDevices", null);
  },
  /**
   * 修改设备名称
   * @param params
   * @returns
   * */
  updateDeviceName(params: any) {
    return request.post("/api/device/updateDeviceName", params);
  },
  /**
   * 指定设备登出
   * @param params
   * @returns
   * */
  deviceLogout(params: any) {
    return request.post("/api/device/deviceLogout", params);
  },
  /**
   * IP归属地查询
   * @param ip IP地址
   * @returns
   */
  ipLocation(ip: string) {
    return request.get(`/api/device/ipLocation?ip=${encodeURIComponent(ip)}`, null);
  },
};