import { createThirdPartyRequest } from "../../core/request";

// ==================== 第三方API封装 ====================

// Brave Search API封装
export const braveSearchApi = {
  // Web搜索
  webSearch: async (apiKey: string, query: string, count: number = 10, offset: number = 0) => {
    const braveRequest = createThirdPartyRequest();
    const url = new URL('https://api.search.brave.com/res/v1/web/search');
    url.searchParams.set('q', query);
    url.searchParams.set('count', Math.min(count, 20).toString());
    url.searchParams.set('offset', offset.toString());

    return braveRequest.get(url.toString(), {
      headers: {
        'Accept': 'application/json',
        'Accept-Encoding': 'gzip',
        'X-Subscription-Token': apiKey
      }
    });
  },

  // 本地搜索
  localSearch: async (apiKey: string, query: string, count: number = 5) => {
    const braveRequest = createThirdPartyRequest();
    const url = new URL('https://api.search.brave.com/res/v1/web/search');
    url.searchParams.set('q', query);
    url.searchParams.set('search_lang', 'en');
    url.searchParams.set('result_filter', 'locations');
    url.searchParams.set('count', Math.min(count, 20).toString());

    return braveRequest.get(url.toString(), {
      headers: {
        'Accept': 'application/json',
        'Accept-Encoding': 'gzip',
        'X-Subscription-Token': apiKey
      }
    });
  },

  // 获取POI数据
  getPoisData: async (apiKey: string, ids: string[]) => {
    const braveRequest = createThirdPartyRequest();
    const url = new URL('https://api.search.brave.com/res/v1/local/pois');
    ids.filter(Boolean).forEach((id) => url.searchParams.append('ids', id));

    return braveRequest.get(url.toString(), {
      headers: {
        'Accept': 'application/json',
        'Accept-Encoding': 'gzip',
        'X-Subscription-Token': apiKey
      }
    });
  },

  // 获取描述数据
  getDescriptionsData: async (apiKey: string, ids: string[]) => {
    const braveRequest = createThirdPartyRequest();
    const url = new URL('https://api.search.brave.com/res/v1/local/descriptions');
    ids.filter(Boolean).forEach((id) => url.searchParams.append('ids', id));

    return braveRequest.get(url.toString(), {
      headers: {
        'Accept': 'application/json',
        'Accept-Encoding': 'gzip',
        'X-Subscription-Token': apiKey
      }
    });
  }
};

// 通用第三方请求封装
export const thirdPartyApi = {
  // 通用fetch请求
  fetch: async (url: string, options: {
    method?: string;
    headers?: Record<string, string>;
    body?: any;
  } = {}) => {
    const thirdPartyRequest = createThirdPartyRequest();
    const { method = 'GET', headers = {}, body } = options;

    const config: any = {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        ...headers
      }
    };

    if (body) {
      config.data = body;
    }

    switch (method.toUpperCase()) {
      case 'GET':
        return thirdPartyRequest.get(url, config);
      case 'POST':
        return thirdPartyRequest.post(url, body, config);
      case 'PUT':
        return thirdPartyRequest.put(url, body, config);
      case 'DELETE':
        return thirdPartyRequest.delete(url, config);
      default:
        return thirdPartyRequest.request({ ...config, method, url });
    }
  },

  // 一言API
  hitokoto: async () => {
    return thirdPartyApi.fetch('https://v1.hitokoto.cn');
  }
};

// Token计数API封装
export const tokenCountApi = {
  // Gemini Token计数
  gemini: async (apiBase: string, apiKey: string, model: string, messages: any[]) => {
    return thirdPartyApi.fetch(`${apiBase}/v1beta/models/${model}:countTokens?key=${apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: { contents: messages }
    });
  },

  // Moonshot Token计数
  moonshot: async (apiBase: string, apiKey: string, model: string, messages: any[]) => {
    return thirdPartyApi.fetch(`${apiBase}/tokenizers/estimate-token-count`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: { model, messages }
    });
  }
};

// 模型相关API封装
export const modelApi = {
  // 获取模型列表
  getModels: async (apiBase: string, modelsEndpoint: string, headers: Record<string, string> = {}) => {
    return thirdPartyApi.fetch(`${apiBase}${modelsEndpoint}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    });
  }
};

export default {
  braveSearchApi,
  thirdPartyApi,
  tokenCountApi,
  modelApi
};
