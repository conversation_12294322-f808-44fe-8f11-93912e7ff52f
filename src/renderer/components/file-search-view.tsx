import React, { useState, useEffect, useRef } from 'react';
import { Input } from './ui/input';
import { ArrowLeft } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import FileSearchResults from './file-search-results';
import FilePreview from './file-preview';
import { FileInfo } from '../../shared/types/file';
import { fileManagerClient } from '../services/api/file-manager';

interface FileSearchViewProps {
  onBackToMain: () => void;
  initialQuery?: string;
}

const FileSearchView: React.FC<FileSearchViewProps> = ({ onBackToMain, initialQuery = '' }) => {
  const { t } = useTranslation();
  const [query, setQuery] = useState(initialQuery);
  const [selectedFile, setSelectedFile] = useState<FileInfo | null>(null);
  const inputRef = useRef<React.ElementRef<typeof Input>>(null);

  // 聚焦到输入框
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  // 处理文件查询输入
  const handleQueryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setQuery(e.target.value);
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Backspace' && query === '') {
      // 如果输入框为空且按下退格键，返回主界面
      onBackToMain();
    }
  };

  // 处理文件选择
  const handleFileSelect = (file: FileInfo) => {
    setSelectedFile(file);
  };

  // 打开文件
  const handleOpenFile = () => {
    if (selectedFile) {
      fileManagerClient.openFile(selectedFile.path);
    }
  };

  // 打开文件所在位置
  const handleOpenFileLocation = () => {
    if (selectedFile) {
      fileManagerClient.openFileLocation(selectedFile.path);
    }
  };

  return (
    <div className="w-full h-full flex flex-col">
      {/* 顶部搜索栏 */}
      <div className="flex items-center space-x-2 bg-background/80 backdrop-blur-sm border rounded-t-lg p-2 shadow-lg">
        <button
          onClick={onBackToMain}
          className="h-8 w-8 flex items-center justify-center rounded-full hover:bg-secondary"
          title={t('common.back')}
        >
          <ArrowLeft className="h-4 w-4" />
        </button>
        <Input
          ref={inputRef}
          type="text"
          placeholder={t('filePreview.searchFilesPlaceholder')}
          className="flex-1 border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
          value={query}
          onChange={handleQueryChange}
          onKeyDown={handleKeyDown}
          autoFocus
        />
      </div>

      {/* 主体内容 */}
      <div className="flex-1 flex border rounded-b-lg overflow-hidden">
        {/* 左侧文件列表 */}
        <div className="w-1/2 border-r overflow-hidden">
          <FileSearchResults 
            query={query} 
            onSelectFile={handleFileSelect} 
            selectedFilePath={selectedFile?.path || null}
          />
        </div>
        
        {/* 右侧文件预览 */}
        <div className="w-1/2 overflow-hidden">
          <FilePreview 
            filePath={selectedFile?.path || null}
            fileName={selectedFile?.name || null}
            fileType={selectedFile?.extension || null}
            fileSize={selectedFile?.size || null}
            onOpen={handleOpenFile}
            onOpenLocation={handleOpenFileLocation}
          />
        </div>
      </div>
    </div>
  );
};

export default FileSearchView; 