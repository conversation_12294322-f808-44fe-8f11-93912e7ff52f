import React, { useState } from 'react';
import { Settings } from 'lucide-react';
import { Button } from '../ui/button';
import { useTranslation } from 'react-i18next';
import { windowManagerClient } from '../../services/api/window-manager';

/**
 * 设置对话框组件
 * 包含应用设置选项
 */
export const SettingsDialog: React.FC = () => {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);

  const openSettings = () => {
    setIsOpen(true);
    // 后续实现设置对话框
    console.log('打开设置对话框');
  };

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={openSettings}
      className="h-8 w-8"
      title={t('settings.title')}
    >
      <Settings className="h-4 w-4" />
    </Button>
  );
}; 