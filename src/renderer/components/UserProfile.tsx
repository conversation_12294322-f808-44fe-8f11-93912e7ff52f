import React, { useCallback } from 'react';
import { Button } from './ui/button';
import { useAuthStore } from '../stores/authStore';
import { User, LogOut, LogIn, Settings } from 'lucide-react';

interface UserProfileProps {
  className?: string;
  variant?: 'compact' | 'full';
}

const UserProfile: React.FC<UserProfileProps> = ({ className = '', variant = 'compact' }) => {
  const {
    user,
    isLoggedIn,
    isLoading,
    openLoginDialog,
    logout,
  } = useAuthStore();

  // 安全的退出登录处理器
  const handleLogout = useCallback(() => {
    logout();
  }, [logout]);

  // 如果未登录，显示登录链接
  if (!isLoggedIn || !user) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <span
          className="flex items-center gap-1 text-blue-600 dark:text-blue-300 cursor-pointer hover:underline hover:text-blue-800 dark:hover:text-blue-200 text-sm transition-colors"
          onClick={openLoginDialog}
        >
          <LogIn className="h-4 w-4" />
          登录
        </span>
      </div>
    );
  }

  // 登录后显示用户信息
  if (variant === 'compact') {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        {/* 用户头像 */}
        <div className="flex items-center justify-center w-8 h-8 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 rounded-full">
          {user.headerImg ? (
            <img 
              src={user.headerImg} 
              alt={user.nickName || user.userName}
              className="w-8 h-8 rounded-full object-cover"
            />
          ) : (
            <User className="h-4 w-4" />
          )}
        </div>
        {/* 用户名 */}
        <span className="text-sm font-medium text-gray-700 dark:text-gray-100 max-w-20 truncate">
          {user.nickName || user.userName}
        </span>
        {/* 登出按钮 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={handleLogout}
          disabled={isLoading}
          className="h-8 w-8 p-0"
          title="登出"
        >
          <LogOut className="h-3 w-3" />
        </Button>
      </div>
    );
  }

  // 完整版本显示更多信息
  return (
    <div className={`p-4 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-lg shadow-sm ${className}`}>
      {/* 用户头像和基本信息 */}
      <div className="flex items-center gap-3 mb-4">
        <div className="flex items-center justify-center w-12 h-12 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 rounded-full">
          {user.headerImg ? (
            <img 
              src={user.headerImg} 
              alt={user.nickName || user.userName}
              className="w-12 h-12 rounded-full object-cover"
            />
          ) : (
            <User className="h-6 w-6" />
          )}
        </div>
        <div className="flex-1">
          <h3 className="font-medium text-gray-900 dark:text-gray-100">{user.nickName || user.userName}</h3>
          {user.email && (
            <p className="text-sm text-gray-500 dark:text-gray-400">{user.email}</p>
          )}
        </div>
      </div>
      {/* 用户详细信息 */}
      <div className="space-y-2 mb-4 text-sm">
        <div className="flex justify-between">
          <span className="text-gray-500 dark:text-gray-400">用户名：</span>
          <span className="text-gray-900 dark:text-gray-100">{user.userName}</span>
        </div>
        {user.createTime && (
          <div className="flex justify-between">
            <span className="text-gray-500 dark:text-gray-400">注册时间：</span>
            <span className="text-gray-900 dark:text-gray-100">
              {new Date(user.createTime).toLocaleDateString()}
            </span>
          </div>
        )}
        {user.lastLoginTime && (
          <div className="flex justify-between">
            <span className="text-gray-500 dark:text-gray-400">最后登录：</span>
            <span className="text-gray-900 dark:text-gray-100">
              {new Date(user.lastLoginTime).toLocaleString()}
            </span>
          </div>
        )}
      </div>
      {/* 操作按钮 */}
      <div className="flex gap-2">
        <Button
          variant="outline"
          size="sm"
          className="flex items-center gap-2 flex-1"
        >
          <Settings className="h-4 w-4" />
          设置
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={handleLogout}
          disabled={isLoading}
          className="flex items-center gap-2 flex-1"
        >
          <LogOut className="h-4 w-4" />
          登出
        </Button>
      </div>
    </div>
  );
};

export default UserProfile; 