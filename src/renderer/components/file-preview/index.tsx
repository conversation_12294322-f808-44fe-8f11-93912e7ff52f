import React from 'react';
import ImagePreview from './image-preview';
import VideoPreview from './video-preview';
import AudioPreview from './audio-preview';
import TextPreview from './text-preview';
import BinaryPreview from './binary-preview';

interface FilePreviewProps {
  /** mime 类型，例如 image/png */
  mimeType: string;
  /** 文件本地路径或 dataURL */
  src: string;
  /** 备用文本(文本文件内容) */
  textContent?: string;
}

// 处理 src，确保本地路径可在 <img>/<video> 正确加载
const normalizeSrc = (src: string): string => {
  if (!src) return '';
  // dataURL 或 http(s) 直接返回
  if (src.startsWith('data:') || src.startsWith('http://') || src.startsWith('https://')) {
    return src;
  }
  // 已有 file:// 前缀
  if (src.startsWith('file://')) {
    return src;
  }
  // 其它情况视为本地绝对路径
  return `file://${encodeURI(src)}`;
};

/**
 * 统一文件预览入口，根据 mime 类型选择具体呈现方式
 */
const UniversalFilePreview: React.FC<FilePreviewProps> = ({ mimeType, src, textContent }) => {
  const safeSrc = normalizeSrc(src);
  if (mimeType.startsWith('image/')) {
    return <ImagePreview src={safeSrc} />;
  }
  if (mimeType.startsWith('video/')) {
    return <VideoPreview src={safeSrc} />;
  }
  if (mimeType.startsWith('audio/')) {
    return <AudioPreview src={safeSrc} />;
  }
  if (mimeType.startsWith('text/') || mimeType === 'application/json' || mimeType === 'application/xml') {
    return <TextPreview text={textContent || ''} />;
  }
  // 后续可加入 application/pdf、application/zip 等特殊处理
  return <BinaryPreview mime={mimeType} />;
};

export default UniversalFilePreview; 