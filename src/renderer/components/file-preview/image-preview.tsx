import React from 'react';

interface ImagePreviewProps {
  src: string;
}

const ImagePreview: React.FC<ImagePreviewProps> = ({ src }) => {
  return (
    <div className="w-full h-full flex items-center justify-center overflow-auto bg-background">
      {/* eslint-disable-next-line @next/next/no-img-element */}
      <img src={src} alt="预览" className="max-h-[70vh] object-contain" />
    </div>
  );
};

export default ImagePreview; 