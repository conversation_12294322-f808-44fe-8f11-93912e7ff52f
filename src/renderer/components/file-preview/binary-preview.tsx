import React from 'react';
import { File } from 'lucide-react';

interface BinaryPreviewProps {
  mime: string;
}

const BinaryPreview: React.FC<BinaryPreviewProps> = ({ mime }) => {
  return (
    <div className="flex h-full w-full flex-col items-center justify-center gap-2 text-muted-foreground">
      <File className="h-10 w-10" />
      <span className="text-sm">无法预览此类型</span>
      <span className="text-xs">{mime}</span>
    </div>
  );
};

export default BinaryPreview; 