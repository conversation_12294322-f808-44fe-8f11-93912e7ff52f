import React, { useState } from 'react';
import { Button } from './ui/button';
import EnhancedStreamingAudioPlayer from './enhanced-streaming-audio-player';
import { Plus, Trash2 } from 'lucide-react';

/**
 * 音频播放器演示组件
 * 用于测试和展示增强版流式音频播放器的功能
 */
const AudioPlayerDemo: React.FC = () => {
  const [audioSegments, setAudioSegments] = useState<ArrayBuffer[]>([]);
  const [isGeneratingAudio, setIsGeneratingAudio] = useState(false);

  // 生成模拟音频数据（正弦波）
  const generateMockAudioBuffer = (frequency: number = 440, duration: number = 2): ArrayBuffer => {
    const sampleRate = 44100;
    const numSamples = sampleRate * duration;
    const arrayBuffer = new ArrayBuffer(44 + numSamples * 2);
    const view = new DataView(arrayBuffer);

    // WAV文件头
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };

    writeString(0, 'RIFF');
    view.setUint32(4, 36 + numSamples * 2, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, 1, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * 2, true);
    view.setUint16(32, 2, true);
    view.setUint16(34, 16, true);
    writeString(36, 'data');
    view.setUint32(40, numSamples * 2, true);

    // 生成正弦波音频数据
    let offset = 44;
    for (let i = 0; i < numSamples; i++) {
      const sample = Math.sin(2 * Math.PI * frequency * i / sampleRate) * 0.5;
      const intSample = Math.max(-32768, Math.min(32767, sample * 32767));
      view.setInt16(offset, intSample, true);
      offset += 2;
    }

    return arrayBuffer;
  };

  // 添加音频片段
  const addAudioSegment = () => {
    setIsGeneratingAudio(true);
    
    // 模拟音频生成延迟
    setTimeout(() => {
      const frequencies = [262, 294, 330, 349, 392, 440, 494, 523]; // C大调音阶
      const randomFreq = frequencies[Math.floor(Math.random() * frequencies.length)];
      const newSegment = generateMockAudioBuffer(randomFreq, 1.5);
      
      setAudioSegments(prev => [...prev, newSegment]);
      setIsGeneratingAudio(false);
    }, 500);
  };

  // 清空所有音频片段
  const clearAllSegments = () => {
    setAudioSegments([]);
  };

  // 模拟实时接收音频片段
  const simulateRealTimeStream = () => {
    const addSegmentWithDelay = (index: number) => {
      if (index >= 5) return; // 添加5个片段
      
      setTimeout(() => {
        const frequencies = [440, 493, 523, 587, 659]; // A、B、C、D、E
        const newSegment = generateMockAudioBuffer(frequencies[index], 2);
        setAudioSegments(prev => [...prev, newSegment]);
        
        addSegmentWithDelay(index + 1);
      }, 2000); // 每2秒添加一个片段
    };
    
    clearAllSegments();
    addSegmentWithDelay(0);
  };

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-2">音频播放器演示</h2>
        <p className="text-gray-600 dark:text-gray-400">
          演示增强版流式音频播放器的功能，支持多片段合并和实时播放
        </p>
      </div>

      {/* 控制按钮 */}
      <div className="flex flex-wrap gap-3 justify-center">
        <Button 
          onClick={addAudioSegment}
          disabled={isGeneratingAudio}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          {isGeneratingAudio ? '生成中...' : '添加音频片段'}
        </Button>
        
        <Button 
          onClick={simulateRealTimeStream}
          variant="outline"
          className="flex items-center gap-2"
        >
          播放实时流演示
        </Button>
        
        <Button 
          onClick={clearAllSegments}
          variant="destructive"
          className="flex items-center gap-2"
          disabled={audioSegments.length === 0}
        >
          <Trash2 className="h-4 w-4" />
          清空所有片段
        </Button>
      </div>

      {/* 音频播放器 */}
      <div className="border rounded-lg p-4 bg-white dark:bg-gray-800">
        <h3 className="text-lg font-semibold mb-3">增强版流式音频播放器</h3>
        
        {audioSegments.length > 0 ? (
          <EnhancedStreamingAudioPlayer
            audioSegments={audioSegments}
            autoPlay={false}
            enableQueuedPlayback={true}
            onSegmentAdded={(segmentIndex) => {
              console.log(`✅ 新音频片段已添加: ${segmentIndex + 1}/${audioSegments.length}`);
            }}
            onPlaybackProgress={(currentTime, duration) => {
              // 可以在这里处理播放进度
            }}
          />
        ) : (
          <div className="text-center py-8 text-gray-500">
            暂无音频片段，请点击上方按钮添加音频
          </div>
        )}
      </div>

      {/* 功能说明 */}
      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
        <h3 className="text-lg font-semibold mb-2 text-blue-900 dark:text-blue-100">功能特性</h3>
        <ul className="space-y-2 text-sm text-blue-800 dark:text-blue-200">
          <li>• <strong>实时音频合并</strong>：自动将多个音频片段合并为连续播放</li>
          <li>• <strong>队列式播放</strong>：支持自动播放下一个片段，无缝衔接</li>
          <li>• <strong>播放控制</strong>：提供播放/暂停、上一曲/下一曲、音量控制等功能</li>
          <li>• <strong>进度显示</strong>：显示当前片段进度和整体播放进度</li>
          <li>• <strong>自动播放</strong>：新片段添加时可自动开始播放</li>
          <li>• <strong>缓冲指示</strong>：显示音频加载和缓冲状态</li>
        </ul>
      </div>

      {/* 使用场景 */}
      <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
        <h3 className="text-lg font-semibold mb-2 text-green-900 dark:text-green-100">应用场景</h3>
        <ul className="space-y-2 text-sm text-green-800 dark:text-green-200">
          <li>• <strong>AI语音对话</strong>：实时播放AI生成的语音回复片段</li>
          <li>• <strong>流式音频</strong>：处理从服务器实时接收的音频流</li>
          <li>• <strong>分段语音</strong>：播放被分割的长音频内容</li>
          <li>• <strong>音频合成</strong>：将多个短音频片段组合播放</li>
        </ul>
      </div>
    </div>
  );
};

export default AudioPlayerDemo; 