import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Play, Pause, Volume2, VolumeX, SkipForward, SkipBack } from 'lucide-react';
import { Button } from './ui/button';

interface EnhancedStreamingAudioPlayerProps {
  audioSegments: ArrayBuffer[];
  autoPlay?: boolean;
  className?: string;
  onSegmentAdded?: (segmentIndex: number) => void;
  onPlaybackProgress?: (currentTime: number, duration: number) => void;
  enableQueuedPlayback?: boolean; // 是否启用队列式播放
}

/**
 * 增强版流式音频播放器组件
 * 支持实时播放音频片段队列，提供更流畅的用户体验
 */
const EnhancedStreamingAudioPlayer: React.FC<EnhancedStreamingAudioPlayerProps> = ({ 
  audioSegments, 
  autoPlay = true, 
  className = '',
  onSegmentAdded,
  onPlaybackProgress,
  enableQueuedPlayback = true
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentSegmentIndex, setCurrentSegmentIndex] = useState(0);
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [audioUrls, setAudioUrls] = useState<string[]>([]);
  const [isBuffering, setIsBuffering] = useState(false);
  
  const audioRef = useRef<HTMLAudioElement>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const playbackQueueRef = useRef<string[]>([]);
  const currentUrlsRef = useRef<string[]>([]);

  // 初始化AudioContext
  const initAudioContext = useCallback(() => {
    if (!audioContextRef.current) {
      audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();
    }
    return audioContextRef.current;
  }, []);

  // 将ArrayBuffer转换为Blob URL
  const arrayBufferToUrl = useCallback(async (audioBuffer: ArrayBuffer): Promise<string> => {
    const blob = new Blob([audioBuffer], { type: 'audio/wav' });
    return URL.createObjectURL(blob);
  }, []);

  // 处理新的音频片段
  useEffect(() => {
    if (audioSegments.length === 0) {
      // 清理所有URL
      currentUrlsRef.current.forEach(url => URL.revokeObjectURL(url));
      currentUrlsRef.current = [];
      setAudioUrls([]);
      setCurrentSegmentIndex(0);
      setDuration(0);
      setCurrentTime(0);
      return;
    }

    const processNewSegments = async () => {
      setIsLoading(true);
      
      try {
        const newUrls: string[] = [];
        
        // 处理新增的音频片段
        for (let i = currentUrlsRef.current.length; i < audioSegments.length; i++) {
          const url = await arrayBufferToUrl(audioSegments[i]);
          newUrls.push(url);
          currentUrlsRef.current.push(url);
        }
        
        if (newUrls.length > 0) {
          setAudioUrls([...currentUrlsRef.current]);
          
          // 如果启用队列播放且当前没有播放，开始播放第一个
          if (enableQueuedPlayback && !isPlaying && autoPlay) {
            playSegment(currentSegmentIndex);
          }
          
          // 通知有新片段添加
          if (onSegmentAdded) {
            onSegmentAdded(audioSegments.length - 1);
          }
        }
      } catch (error) {
        console.error('处理音频片段失败:', error);
      } finally {
        setIsLoading(false);
      }
    };

    processNewSegments();

    return () => {
      // 组件卸载时清理URL
      currentUrlsRef.current.forEach(url => URL.revokeObjectURL(url));
      currentUrlsRef.current = [];
    };
  }, [audioSegments, arrayBufferToUrl, enableQueuedPlayback, autoPlay, currentSegmentIndex, isPlaying, onSegmentAdded]);

  // 播放指定片段
  const playSegment = useCallback(async (segmentIndex: number) => {
    if (segmentIndex >= audioUrls.length || segmentIndex < 0) return;
    
    const audio = audioRef.current;
    if (!audio) return;

    setIsBuffering(true);
    setCurrentSegmentIndex(segmentIndex);
    
    try {
      audio.src = audioUrls[segmentIndex];
      await audio.load();
      
      if (autoPlay) {
        await audio.play();
        setIsPlaying(true);
      }
    } catch (error) {
      console.error('播放音频片段失败:', error);
      setIsPlaying(false);
    } finally {
      setIsBuffering(false);
    }
  }, [audioUrls, autoPlay]);

  // 播放下一个片段
  const playNextSegment = useCallback(() => {
    const nextIndex = currentSegmentIndex + 1;
    if (nextIndex < audioUrls.length) {
      playSegment(nextIndex);
    } else {
      setIsPlaying(false);
      setCurrentTime(0);
    }
  }, [currentSegmentIndex, audioUrls.length, playSegment]);

  // 播放上一个片段
  const playPrevSegment = useCallback(() => {
    const prevIndex = currentSegmentIndex - 1;
    if (prevIndex >= 0) {
      playSegment(prevIndex);
    }
  }, [currentSegmentIndex, playSegment]);

  // 音频事件处理
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleLoadedMetadata = () => {
      setDuration(audio.duration);
    };

    const handleTimeUpdate = () => {
      setCurrentTime(audio.currentTime);
      if (onPlaybackProgress) {
        onPlaybackProgress(audio.currentTime, audio.duration);
      }
    };

    const handleEnded = () => {
      if (enableQueuedPlayback) {
        // 自动播放下一个片段
        playNextSegment();
      } else {
        setIsPlaying(false);
        setCurrentTime(0);
      }
    };

    const handleCanPlay = () => {
      setIsBuffering(false);
    };

    const handleWaiting = () => {
      setIsBuffering(true);
    };

    const handleError = (e: Event) => {
      console.error('音频播放错误:', e);
      setIsPlaying(false);
      setIsBuffering(false);
    };

    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('canplay', handleCanPlay);
    audio.addEventListener('waiting', handleWaiting);
    audio.addEventListener('error', handleError);

    return () => {
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('canplay', handleCanPlay);
      audio.removeEventListener('waiting', handleWaiting);
      audio.removeEventListener('error', handleError);
    };
  }, [enableQueuedPlayback, playNextSegment, onPlaybackProgress]);

  // 播放/暂停控制
  const togglePlayPause = useCallback(() => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.pause();
      setIsPlaying(false);
    } else {
      audio.play().then(() => {
        setIsPlaying(true);
      }).catch(error => {
        console.error('播放音频失败:', error);
      });
    }
  }, [isPlaying]);

  // 进度条控制
  const handleSeek = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const audio = audioRef.current;
    if (!audio) return;

    const newTime = parseFloat(e.target.value);
    audio.currentTime = newTime;
    setCurrentTime(newTime);
  }, []);

  // 音量控制
  const handleVolumeChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(e.target.value);
    setVolume(newVolume);
    
    const audio = audioRef.current;
    if (audio) {
      audio.volume = newVolume;
    }
  }, []);

  // 静音切换
  const toggleMute = useCallback(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const newMuted = !isMuted;
    setIsMuted(newMuted);
    audio.muted = newMuted;
  }, [isMuted]);

  // 格式化时间
  const formatTime = useCallback((seconds: number): string => {
    if (isNaN(seconds)) return '0:00';
    
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }, []);

  // 计算总进度（所有片段）
  const getTotalProgress = useCallback(() => {
    if (audioUrls.length === 0) return 0;
    return ((currentSegmentIndex + (currentTime / (duration || 1))) / audioUrls.length) * 100;
  }, [currentSegmentIndex, currentTime, duration, audioUrls.length]);

  // 如果没有音频片段
  if (audioSegments.length === 0) {
    return (
      <div className={`flex items-center gap-2 p-2 bg-gray-100 dark:bg-gray-700 rounded ${className}`}>
        <Volume2 className="h-4 w-4 text-gray-400" />
        <span className="text-sm text-gray-500">等待音频数据...</span>
      </div>
    );
  }

  // 加载中状态
  if (isLoading && audioUrls.length === 0) {
    return (
      <div className={`flex items-center gap-2 p-2 bg-gray-100 dark:bg-gray-700 rounded ${className}`}>
        <Volume2 className="h-4 w-4 text-gray-400 animate-pulse" />
        <span className="text-sm text-gray-500">处理音频中...</span>
      </div>
    );
  }

  return (
    <div className={`flex flex-col gap-2 p-3 bg-white dark:bg-gray-800 rounded border ${className}`}>
      {/* 主控制面板 */}
      <div className="flex items-center gap-3">
        <audio ref={audioRef} preload="metadata" />
        
        {/* 上一曲按钮 */}
        {enableQueuedPlayback && audioUrls.length > 1 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={playPrevSegment}
            disabled={currentSegmentIndex === 0 || isBuffering}
            className="p-1 h-8 w-8"
          >
            <SkipBack className="h-4 w-4" />
          </Button>
        )}
        
        {/* 播放/暂停按钮 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={togglePlayPause}
          className="p-1 h-8 w-8"
          disabled={isLoading || isBuffering}
        >
          {isBuffering ? (
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600"></div>
          ) : isPlaying ? (
            <Pause className="h-4 w-4" />
          ) : (
            <Play className="h-4 w-4" />
          )}
        </Button>

        {/* 下一曲按钮 */}
        {enableQueuedPlayback && audioUrls.length > 1 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={playNextSegment}
            disabled={currentSegmentIndex >= audioUrls.length - 1 || isBuffering}
            className="p-1 h-8 w-8"
          >
            <SkipForward className="h-4 w-4" />
          </Button>
        )}

        {/* 当前片段进度条和时间 */}
        <div className="flex-1 flex items-center gap-2">
          <span className="text-xs text-gray-500 min-w-[35px]">
            {formatTime(currentTime)}
          </span>
          
          <div className="flex-1 relative">
            <input
              type="range"
              min="0"
              max={duration || 0}
              value={currentTime}
              onChange={handleSeek}
              className="w-full h-1 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
              style={{
                background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${(currentTime / duration) * 100}%, #e5e7eb ${(currentTime / duration) * 100}%, #e5e7eb 100%)`
              }}
            />
          </div>
          
          <span className="text-xs text-gray-500 min-w-[35px]">
            {formatTime(duration)}
          </span>
        </div>

        {/* 音量控制 */}
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleMute}
            className="p-1 h-6 w-6"
          >
            {isMuted ? (
              <VolumeX className="h-3 w-3 text-gray-400" />
            ) : (
              <Volume2 className="h-3 w-3 text-gray-400" />
            )}
          </Button>
          
          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            value={volume}
            onChange={handleVolumeChange}
            className="w-16 h-1 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
            style={{
              background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${volume * 100}%, #e5e7eb ${volume * 100}%, #e5e7eb 100%)`
            }}
          />
        </div>
      </div>

      {/* 片段信息和整体进度 */}
      {audioUrls.length > 1 && (
        <div className="flex items-center justify-between text-xs text-gray-500">
          <div className="flex items-center gap-2">
            <span>片段 {currentSegmentIndex + 1} / {audioUrls.length}</span>
            {isLoading && <span className="animate-pulse">加载中...</span>}
          </div>
          
          {/* 整体进度条 */}
          <div className="flex-1 mx-4">
            <div className="w-full bg-gray-200 rounded-full h-1 dark:bg-gray-700">
              <div 
                className="bg-blue-600 h-1 rounded-full transition-all duration-200" 
                style={{ width: `${getTotalProgress()}%` }}
              ></div>
            </div>
          </div>
          
          <span className="text-xs text-gray-400">总进度</span>
        </div>
      )}
    </div>
  );
};

export default EnhancedStreamingAudioPlayer; 