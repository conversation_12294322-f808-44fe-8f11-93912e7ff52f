import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Play, Pause, Volume2, VolumeX } from 'lucide-react';
import { Button } from './ui/button';

interface StreamingAudioPlayerProps {
  audioSegments: ArrayBuffer[];
  autoPlay?: boolean;
  className?: string;
  onSegmentAdded?: (segmentIndex: number) => void;
}

/**
 * 流式音频播放器组件
 * 支持多个音频片段的实时合并和播放
 */
const StreamingAudioPlayer: React.FC<StreamingAudioPlayerProps> = ({ 
  audioSegments, 
  autoPlay = true, 
  className = '',
  onSegmentAdded
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [mergedAudioUrl, setMergedAudioUrl] = useState<string>('');
  
  const audioRef = useRef<HTMLAudioElement>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const mergedBufferRef = useRef<AudioBuffer | null>(null);
  const currentUrlRef = useRef<string>('');

  // 初始化AudioContext
  const initAudioContext = useCallback(() => {
    if (!audioContextRef.current) {
      audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();
    }
    return audioContextRef.current;
  }, []);

  // 合并多个音频片段
  const mergeAudioBuffers = useCallback(async (buffers: ArrayBuffer[]): Promise<AudioBuffer | null> => {
    if (buffers.length === 0) return null;

    const audioContext = initAudioContext();
    const audioBuffers: AudioBuffer[] = [];

    try {
      // 解码所有音频片段
      for (const buffer of buffers) {
        try {
          const audioBuffer = await audioContext.decodeAudioData(buffer.slice(0));
          audioBuffers.push(audioBuffer);
        } catch (error) {
          console.error('解码音频片段失败:', error);
          continue;
        }
      }

      if (audioBuffers.length === 0) return null;

      // 计算总长度和通道数
      const totalLength = audioBuffers.reduce((sum, buffer) => sum + buffer.length, 0);
      const numberOfChannels = audioBuffers[0].numberOfChannels;
      const sampleRate = audioBuffers[0].sampleRate;

      // 创建合并后的音频缓冲区
      const mergedBuffer = audioContext.createBuffer(numberOfChannels, totalLength, sampleRate);

      // 合并所有音频数据
      let offset = 0;
      for (const buffer of audioBuffers) {
        for (let channel = 0; channel < numberOfChannels; channel++) {
          const channelData = buffer.getChannelData(channel);
          mergedBuffer.getChannelData(channel).set(channelData, offset);
        }
        offset += buffer.length;
      }

      return mergedBuffer;
    } catch (error) {
      console.error('合并音频失败:', error);
      return null;
    }
  }, [initAudioContext]);

  // 将AudioBuffer转换为Blob URL
  const audioBufferToBlob = useCallback(async (audioBuffer: AudioBuffer): Promise<string> => {
    const numberOfChannels = audioBuffer.numberOfChannels;
    const sampleRate = audioBuffer.sampleRate;
    const length = audioBuffer.length;

    // 创建WAV文件头
    const arrayBuffer = new ArrayBuffer(44 + length * numberOfChannels * 2);
    const view = new DataView(arrayBuffer);

    // WAV文件头
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };

    writeString(0, 'RIFF');
    view.setUint32(4, 36 + length * numberOfChannels * 2, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, numberOfChannels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * numberOfChannels * 2, true);
    view.setUint16(32, numberOfChannels * 2, true);
    view.setUint16(34, 16, true);
    writeString(36, 'data');
    view.setUint32(40, length * numberOfChannels * 2, true);

    // 写入音频数据
    let offset = 44;
    for (let i = 0; i < length; i++) {
      for (let channel = 0; channel < numberOfChannels; channel++) {
        const sample = Math.max(-1, Math.min(1, audioBuffer.getChannelData(channel)[i]));
        view.setInt16(offset, sample < 0 ? sample * 0x8000 : sample * 0x7FFF, true);
        offset += 2;
      }
    }

    const blob = new Blob([arrayBuffer], { type: 'audio/wav' });
    return URL.createObjectURL(blob);
  }, []);

  // 当音频片段更新时重新合并
  useEffect(() => {
    if (audioSegments.length === 0) {
      setMergedAudioUrl('');
      setDuration(0);
      setCurrentTime(0);
      return;
    }

    const processingAsync = async () => {
      setIsLoading(true);
      
      try {
        const mergedBuffer = await mergeAudioBuffers(audioSegments);
        
        if (mergedBuffer) {
          mergedBufferRef.current = mergedBuffer;
          
          // 清理旧的URL
          if (currentUrlRef.current) {
            URL.revokeObjectURL(currentUrlRef.current);
          }
          
          const newUrl = await audioBufferToBlob(mergedBuffer);
          currentUrlRef.current = newUrl;
          setMergedAudioUrl(newUrl);
          
          // 通知父组件有新片段添加
          if (onSegmentAdded) {
            onSegmentAdded(audioSegments.length - 1);
          }
        }
      } catch (error) {
        console.error('处理音频片段失败:', error);
      } finally {
        setIsLoading(false);
      }
    };

    processingAsync();

    return () => {
      if (currentUrlRef.current) {
        URL.revokeObjectURL(currentUrlRef.current);
        currentUrlRef.current = '';
      }
    };
  }, [audioSegments, mergeAudioBuffers, audioBufferToBlob, onSegmentAdded]);

  // 音频事件处理
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleLoadedMetadata = () => {
      setDuration(audio.duration);
      
      // 如果启用了自动播放，则播放音频
      if (autoPlay && !isPlaying) {
        audio.play().then(() => {
          setIsPlaying(true);
        }).catch(error => {
          console.error('自动播放失败:', error);
        });
      }
    };

    const handleTimeUpdate = () => {
      setCurrentTime(audio.currentTime);
    };

    const handleEnded = () => {
      setIsPlaying(false);
      setCurrentTime(0);
    };

    const handleError = (e: Event) => {
      console.error('音频播放错误:', e);
      setIsPlaying(false);
    };

    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('error', handleError);

    return () => {
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('error', handleError);
    };
  }, [mergedAudioUrl, autoPlay, isPlaying]);

  // 播放/暂停控制
  const togglePlayPause = useCallback(() => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.pause();
      setIsPlaying(false);
    } else {
      audio.play().then(() => {
        setIsPlaying(true);
      }).catch(error => {
        console.error('播放音频失败:', error);
      });
    }
  }, [isPlaying]);

  // 进度条控制
  const handleSeek = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const audio = audioRef.current;
    if (!audio) return;

    const newTime = parseFloat(e.target.value);
    audio.currentTime = newTime;
    setCurrentTime(newTime);
  }, []);

  // 音量控制
  const handleVolumeChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(e.target.value);
    setVolume(newVolume);
    
    const audio = audioRef.current;
    if (audio) {
      audio.volume = newVolume;
    }
  }, []);

  // 静音切换
  const toggleMute = useCallback(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const newMuted = !isMuted;
    setIsMuted(newMuted);
    audio.muted = newMuted;
  }, [isMuted]);

  // 格式化时间
  const formatTime = useCallback((seconds: number): string => {
    if (isNaN(seconds)) return '0:00';
    
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }, []);

  // 如果没有音频片段
  if (audioSegments.length === 0) {
    return (
      <div className={`flex items-center gap-2 p-2 bg-gray-100 dark:bg-gray-700 rounded ${className}`}>
        <Volume2 className="h-4 w-4 text-gray-400" />
        <span className="text-sm text-gray-500">等待音频数据...</span>
      </div>
    );
  }

  // 加载中状态
  if (isLoading || !mergedAudioUrl) {
    return (
      <div className={`flex items-center gap-2 p-2 bg-gray-100 dark:bg-gray-700 rounded ${className}`}>
        <Volume2 className="h-4 w-4 text-gray-400 animate-pulse" />
        <span className="text-sm text-gray-500">处理音频中...</span>
      </div>
    );
  }

  return (
    <div className={`flex items-center gap-3 p-2 bg-white dark:bg-gray-800 rounded border ${className}`}>
      <audio ref={audioRef} src={mergedAudioUrl} preload="metadata" />
      
      {/* 播放/暂停按钮 */}
      <Button
        variant="ghost"
        size="sm"
        onClick={togglePlayPause}
        className="p-1 h-8 w-8"
        disabled={isLoading}
      >
        {isPlaying ? (
          <Pause className="h-4 w-4" />
        ) : (
          <Play className="h-4 w-4" />
        )}
      </Button>

      {/* 进度条和时间 */}
      <div className="flex-1 flex items-center gap-2">
        <span className="text-xs text-gray-500 min-w-[35px]">
          {formatTime(currentTime)}
        </span>
        
        <div className="flex-1 relative">
          <input
            type="range"
            min="0"
            max={duration || 0}
            value={currentTime}
            onChange={handleSeek}
            className="w-full h-1 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
            style={{
              background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${(currentTime / duration) * 100}%, #e5e7eb ${(currentTime / duration) * 100}%, #e5e7eb 100%)`
            }}
          />
        </div>
        
        <span className="text-xs text-gray-500 min-w-[35px]">
          {formatTime(duration)}
        </span>
      </div>

      {/* 音量控制 */}
      <div className="flex items-center gap-1">
        <Button
          variant="ghost"
          size="sm"
          onClick={toggleMute}
          className="p-1 h-6 w-6"
        >
          {isMuted ? (
            <VolumeX className="h-3 w-3 text-gray-400" />
          ) : (
            <Volume2 className="h-3 w-3 text-gray-400" />
          )}
        </Button>
        
        <input
          type="range"
          min="0"
          max="1"
          step="0.1"
          value={volume}
          onChange={handleVolumeChange}
          className="w-16 h-1 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
          style={{
            background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${volume * 100}%, #e5e7eb ${volume * 100}%, #e5e7eb 100%)`
          }}
        />
      </div>

      {/* 片段数量指示器 */}
      <div className="text-xs text-gray-500 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
        {audioSegments.length} 片段
      </div>
    </div>
  );
};

export default StreamingAudioPlayer; 