import React, { useEffect, useState } from 'react';
import {
  File,
  FileText,
  Image as ImageIcon,
  Files,
  FileCode,
  FileArchive,
  FileSpreadsheet,
  Music,
  Video,
  Box,
  Folder,
  Link as LinkIcon,
  Palette as PaletteIcon,
} from 'lucide-react';
import type {
  ClipboardItem,
  ClipboardTextItem,
  ClipboardImageItem,
  ClipboardFileItem,
  ClipboardDirectoryItem,
} from '../../shared/types/clipboard';
import UniversalFilePreview from './file-preview/index';

// 文件类型映射到图标
const fileTypeToIcon: Record<string, React.ReactNode> = {
  default: <File className="h-8 w-8" />,
  directory: <Folder className="h-8 w-8" />,
  text: <FileText className="h-8 w-8" />,
  image: <ImageIcon className="h-8 w-8" />,
  code: <FileCode className="h-8 w-8" />,
  zip: <FileArchive className="h-8 w-8" />,
  rar: <FileArchive className="h-8 w-8" />,
  '7z': <FileArchive className="h-8 w-8" />,
  xls: <FileSpreadsheet className="h-8 w-8" />,
  xlsx: <FileSpreadsheet className="h-8 w-8" />,
  doc: <FileText className="h-8 w-8" />,
  docx: <FileText className="h-8 w-8" />,
  pdf: <FileText className="h-8 w-8" />,
  mp3: <Music className="h-8 w-8" />,
  wav: <Music className="h-8 w-8" />,
  mp4: <Video className="h-8 w-8" />,
  avi: <Video className="h-8 w-8" />,
  mov: <Video className="h-8 w-8" />,
  obj: <Box className="h-8 w-8" />,
  stl: <Box className="h-8 w-8" />,
};

// 根据文件后缀获取图标
const getFileIcon = (fileType: string): React.ReactNode => {
  const type = fileType.toLowerCase();
  // 代码文件
  if (['js', 'ts', 'jsx', 'tsx', 'py', 'java', 'c', 'cpp', 'cs', 'go', 'rb', 'php', 'html', 'css'].includes(type)) {
    return fileTypeToIcon.code;
  }
  // 图片
  if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(type)) {
    return fileTypeToIcon.image;
  }
  return fileTypeToIcon[type] || fileTypeToIcon.default;
};

interface ClipboardItemPreviewProps {
  item: ClipboardItem | null | undefined;
}

/**
 * 可复用的剪贴板/文件搜索预览组件
 */
const ClipboardItemPreview: React.FC<ClipboardItemPreviewProps> = ({ item }) => {
  if (!item) {
    return (
      <div className="flex h-full items-center justify-center text-muted-foreground">
        <span className="text-sm">未选择项目</span>
      </div>
    );
  }

  const details: Array<[string, string]> = [];
  // 针对文本类文件的内容缓存
  const [fileText, setFileText] = useState<string>('');

  // 判断是否为文本类扩展名
  const isTextExt = (ext: string) => {
    const list = [
      'txt','md','log','json','xml','yaml','yml','csv','html','htm','js','ts','jsx','css','scss','less'
    ];
    return list.includes(ext.toLowerCase());
  };

  // 当 item 为 file 且为文本文件时异步读取预览内容
  useEffect(() => {
    if (item && item.type === 'file') {
      const fileItem = item.items[0] as ClipboardFileItem;
      const ext = (fileItem.type || '').replace(/^\./,'');
      if (isTextExt(ext) && fileItem.path) {
        (async () => {
          try {
            const preview = await window.electron.files.readFilePreview(fileItem.path);
            setFileText(preview);
          } catch (e) {
            console.error('读取文件预览失败:', e);
            setFileText('无法读取文件内容');
          }
        })();
      } else {
        setFileText('');
      }
    } else {
      setFileText('');
    }
  }, [item]);

  // 渲染不同类型内容
  const renderContent = () => {
    switch (item.type) {
      case 'text': {
        const textItem = item.items[0] as ClipboardTextItem;
        const content = textItem.content ?? '';

        details.push(['字符', String(textItem.characters ?? content.length)]);
        details.push(['单词', String(textItem.words ?? content.trim().split(/\s+/).filter(Boolean).length)]);
        details.push(['内容类型', item.contentType || textItem.contentType || 'text/plain']);

        // 颜色十六进制
        const isColor = /^#([0-9a-fA-F]{6}|[0-9a-fA-F]{3})$/.test(content.trim());
        if (isColor) {
          return (
            <div className="flex flex-col items-center gap-4 p-4">
              <div
                className="w-24 h-24 rounded-full border"
                style={{ backgroundColor: content.trim() }}
              ></div>
              <div className="flex items-center gap-2">
                <PaletteIcon className="h-5 w-5 text-muted-foreground" />
                <span className="text-base font-mono">{content.trim()}</span>
              </div>
            </div>
          );
        }

        // 链接
        const isLink = /^(https?:\/\/|file:\/\/|ws:\/\/|wss:\/\/)\S+$/i.test(content.trim());
        if (isLink) {
          return (
            <div className="p-4 flex flex-col gap-4">
              <div className="flex items-center gap-2 break-all">
                <LinkIcon className="h-5 w-5 text-muted-foreground" />
                <a
                  href={content.trim()}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-primary underline"
                >
                  {content.trim()}
                </a>
              </div>
              <iframe
                src={content.trim()}
                className="w-full flex-1 border rounded"
                sandbox="allow-same-origin allow-scripts allow-popups"
              />
            </div>
          );
        }

        // 普通文本
        return (
          <pre className="whitespace-pre-wrap break-all text-sm p-4 bg-muted/30 rounded max-h-full overflow-auto">
            {content}
          </pre>
        );
      }
      case 'image': {
        const imageItem = item.items[0] as ClipboardImageItem;

        if (imageItem.width && imageItem.height) {
          details.push(['尺寸', `${imageItem.width}×${imageItem.height}`]);
        }
        if (typeof imageItem.size === 'number') {
          details.push(['大小', `${(imageItem.size / 1024).toFixed(1)} KB`]);
        }
        details.push(['内容类型', item.contentType || imageItem.contentType || 'image']);

        return (
          <UniversalFilePreview mimeType={item.contentType || imageItem.contentType || 'image/*'} src={imageItem.dataURL || ''} />
        );
      }
      case 'file': {
        const fileItem = item.items[0] as ClipboardFileItem;
        details.push(['路径', fileItem.path || fileItem.content]);
        details.push(['大小', `${(fileItem.size / 1024).toFixed(1)} KB`]);
        const mime = item.contentType || fileItem.contentType || 'application/octet-stream';
        details.push(['内容类型', mime]);

        return (
          <UniversalFilePreview 
            mimeType={mime} 
            src={fileItem.path || fileItem.content} 
            textContent={fileText}
          />
        );
      }
      case 'directory': {
        const dirItem = item.items[0] as ClipboardDirectoryItem;
        details.push(['路径', dirItem.path || dirItem.content]);
        details.push(['内容类型', item.contentType || dirItem.contentType || 'directory']);
        return (
          <div className="flex flex-col items-center gap-4 p-4">
            {fileTypeToIcon.directory}
            <span className="text-base font-medium break-all text-center max-w-full truncate" title={dirItem.name}>
              {dirItem.name}
            </span>
          </div>
        );
      }
      case 'multiple': {
        details.push(['文件数量', String(item.count)]);
        details.push(['内容类型', item.contentType || 'multiple files']);
        return (
          <div className="flex flex-col items-center gap-4 p-4">
            <Files className="h-12 w-12" />
            <span className="text-base font-medium">包含 {item.count} 个文件</span>
          </div>
        );
      }
      default:
        return (
          <div className="flex h-full items-center justify-center text-muted-foreground">
            <span className="text-sm">无法预览此类型</span>
          </div>
        );
    }
  };

  // 通用详情
  if (item.source) details.unshift(['来源', item.source]);

  return (
    <div className="w-full h-full max-h-full overflow-hidden bg-background flex flex-col">
      <div className="flex-1 overflow-auto">{renderContent()}</div>
      {details.length > 0 && (
        <div className="border-t p-3 text-xs flex flex-col gap-1 bg-muted/10">
          {details.map(([k, v]) => (
            <div key={k} className="flex justify-between gap-2">
              <span className="text-muted-foreground">{k}</span>
              <span className="truncate max-w-[70%] text-right">{v}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

ClipboardItemPreview.displayName = 'ClipboardItemPreview';

export default ClipboardItemPreview; 