import React, { useState, useEffect, useRef } from 'react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Modal } from 'antd';
import { useAuthStore } from '../stores/authStore';
import { useToast } from './ui/use-toast';
import { Eye, EyeOff, User, Lock, Loader2, KeyRound } from 'lucide-react';
import baseApi from '../api/base';
import userApi from '../api/user';
import wechatApi from '../api/wechat';
import { baseURL } from '../../core/request/index';

// 添加自定义样式来覆盖Dialog的默认宽度限制
const style = document.createElement('style');
style.textContent = `
  .custom-dialog-content {
    width: var(--dialog-width) !important;
    max-width: none !important;
  }
  #wxCode iframe{
    width: 100%;
    height: 100%;
  }
  .dark #wxCode iframe {
    background-color: #ffffff;
  }
  #captcha .yidun_modal {
    transform: translateY(-50%) !important;
  }
  
  /* 暗色模式下的Modal样式 */
  .dark .ant-modal-content {
    background-color: #1f2937 !important;
    color: #f9fafb !important;
  }
  
  .dark .ant-modal-header {
    background-color: #1f2937 !important;
    border-bottom-color: #374151 !important;
  }
  
  .dark .ant-modal-title {
    color: #f9fafb !important;
  }
  
  .dark .ant-modal-close {
    color: #9ca3af !important;
  }
  
  .dark .ant-modal-close:hover {
    color: #f9fafb !important;
  }
  
  .dark .ant-modal-body {
    background-color: #1f2937 !important;
    color: #f9fafb !important;
  }
  
  .dark .ant-modal-footer {
    background-color: #1f2937 !important;
    border-top-color: #374151 !important;
  }
  
  /* 登录对话框Modal的暗色模式样式 */
  .dark .login-dialog-modal .ant-modal-content {
    background-color: #111827 !important;
    border: 1px solid #374151 !important;
  }
  
  .dark .login-dialog-modal .ant-modal-body {
    background-color: #111827 !important;
  }
  
  .dark .login-dialog-modal .ant-modal-close {
    color: #9ca3af !important;
  }
  
  .dark .login-dialog-modal .ant-modal-close:hover {
    color: #f9fafb !important;
    background-color: #374151 !important;
  }
`;
if (!document.head.querySelector('[data-login-dialog-styles]')) {
  style.setAttribute('data-login-dialog-styles', 'true');
  document.head.appendChild(style);
}

type LoginMode = 'login' | 'register' | 'forgot' | 'bindPhone';
type LoginType = 'password' | 'sms';

const LoginDialog: React.FC = () => {
  const { toast } = useToast();
  const [loginMode, setLoginMode] = useState<LoginMode>('login');
  const [loginType, setLoginType] = useState<LoginType>('password');
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  // 忘记密码专用的显示/隐藏密码状态
  const [showForgotPassword, setShowForgotPassword] = useState(false);
  const [showForgotConfirmPassword, setShowForgotConfirmPassword] = useState(false);
  // 注册专用的显示/隐藏密码状态
  const [showRegisterPassword, setShowRegisterPassword] = useState(false);
  const [showRegisterConfirmPassword, setShowRegisterConfirmPassword] = useState(false);

  const {
    loginDialogOpen,
    login,
    closeLoginDialog,
  } = useAuthStore();

  // 登录表单状态
  const [loginForm, setLoginForm] = useState({
    account: '',
    password: '',
  });
  const [loginErrors, setLoginErrors] = useState({
    account: '',
    password: '',
  });

  // 短信验证码相关状态
  const [smsCode, setSmsCode] = useState('');
  const [smsError, setSmsError] = useState('');
  const [smsCountdown, setSmsCountdown] = useState(0);
  const [isSendingSms, setIsSendingSms] = useState(false);

  // 注册表单状态
  const [registerForm, setRegisterForm] = useState({
    account: '',
    password: '',
    confirmPassword: '',
    smsCode: '',
  });
  const [registerErrors, setRegisterErrors] = useState({
    account: '',
    password: '',
    confirmPassword: '',
    smsCode: '',
  });
  const [registerCountdown, setRegisterCountdown] = useState(0);
  const [isRegisterSending, setIsRegisterSending] = useState(false);

  // 忘记密码表单状态
  const [forgotForm, setForgotForm] = useState({
    account: '',
    smsCode: '',
    password: '',
    confirmPassword: ''
  });
  const [forgotErrors, setForgotErrors] = useState({
    account: '',
    smsCode: '',
    password: '',
    confirmPassword: ''
  });
  const [forgotCountdown, setForgotCountdown] = useState(0);
  const [isForgotSending, setIsForgotSending] = useState(false);

  // 绑定手机号表单状态
  const [bindForm, setBindForm] = useState({
    phone: '',
    code: '',
  });
  const [bindErrors, setBindErrors] = useState({
    phone: '',
    code: '',
  });
  const [bindCountdown, setBindCountdown] = useState(0);
  const [isBindSending, setIsBindSending] = useState(false);
  const [bindLoading, setBindLoading] = useState(false);
  const [bindUserData, setBindUserData] = useState<any>({});
  // refs for closure handling
  const loginFormRef = useRef(loginForm);
  const smsCodeRef = useRef(smsCode);
  const loginTypeRef = useRef(loginType);
  const messageHandledRef = useRef<Set<string>>(new Set()); // 记录已处理的消息
  const captchaInsRef = useRef<any>(null)
  const validateCallbackRef = useRef<any>(null)

  useEffect(() => {
    loginFormRef.current = loginForm;
  }, [loginForm]);

  useEffect(() => {
    smsCodeRef.current = smsCode;
  }, [smsCode]);

  useEffect(() => {
    loginTypeRef.current = loginType;
  }, [loginType]);

  // 重置表单状态
  const resetAllForms = () => {
    setLoginForm({ account: '', password: '' });
    setLoginErrors({ account: '', password: '' });
    setSmsCode('');
    setSmsError('');
    setSmsCountdown(0);
    setRegisterForm({ account: '', password: '', confirmPassword: '', smsCode: '' });
    setRegisterErrors({ account: '', password: '', confirmPassword: '', smsCode: '' });
    setRegisterCountdown(0);
    setForgotForm({ account: '', smsCode: '', password: '', confirmPassword: '' });
    setForgotErrors({ account: '', smsCode: '', password: '', confirmPassword: '' });
    setForgotCountdown(0);
    setBindForm({ phone: '', code: '' });
    setBindErrors({ phone: '', code: '' });
    setBindCountdown(0);
    setIsBindSending(false);
    setBindLoading(false);
    setBindUserData({});
    setIsLoading(false);
    setShowPassword(false);
  };

  // 当弹窗关闭时重置表单
  useEffect(() => {
    if (loginDialogOpen) {
      getWXCode();
    } else  {
      resetAllForms();
      setLoginMode('login');
      setLoginType('password');
    }
  }, [loginDialogOpen]);

  useEffect(() => {
    initYiDun()
  }, [])

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      console.log(event, "微信登录事件")
      
      // 只处理来自微信授权页面的消息
      if (!event.data || !event.data.wxCode || !event.data.wxState) {
        return;
      }
      
      // 创建唯一标识符来防重复
      const messageId = `${event.data.wxCode}-${event.data.wxState}`;
      
      // 防止重复处理同一个消息
      if (messageHandledRef.current.has(messageId)) {
        console.log('已处理过微信登录事件，忽略重复事件:', messageId);
        return;
      }
      
      messageHandledRef.current.add(messageId);
      console.log('开始处理微信登录', event.data);
      
      // 直接调用API而不依赖函数引用
      baseApi.unifiedLogin({
        provider: 'wechat',
        code: event.data.wxCode,
        metadata: event.data.wxState
      }).then(async (res: any) => {
        const { action, ...rest } = res.data;
        if (action === 'login') {
          console.log(rest, "restrestrestrest")
          const loginResult = await login({
            user: rest.user,
            token: rest.token,
          });
          if (!loginResult.success) {
            console.error('微信登录设置用户信息失败:', loginResult.message);
            toast({ title: '微信登录失败', description: loginResult.message, variant: 'destructive' });
          }
        } else if (action === 'bind') {
          setBindUserData(rest.tempData);
          setLoginMode('bindPhone');
        }
      }).catch(error => {
        console.error('微信登录失败:', error);
        toast({ title: '微信登录失败', description: '请稍后重试', variant: 'destructive' });
      });
    };

    window.addEventListener('message', handleMessage);
    
    // 清理函数，组件卸载时移除事件监听器
    return () => {
      window.removeEventListener('message', handleMessage);
      // 清理已处理的消息记录
      messageHandledRef.current.clear();
    };
  }, [login, setBindUserData, setLoginMode, toast]);
  // 倒计时效果
  useEffect(() => {
    if (smsCountdown > 0) {
      const timer = setTimeout(() => setSmsCountdown(smsCountdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [smsCountdown]);

  useEffect(() => {
    if (registerCountdown > 0) {
      const timer = setTimeout(() => setRegisterCountdown(registerCountdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [registerCountdown]);

  useEffect(() => {
    if (forgotCountdown > 0) {
      const timer = setTimeout(() => setForgotCountdown(forgotCountdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [forgotCountdown]);

  useEffect(() => {
    if (bindCountdown > 0) {
      const timer = setTimeout(() => setBindCountdown(bindCountdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [bindCountdown]);

  // 初始化微信登录（模拟）
  useEffect(() => {
    if (loginDialogOpen && loginMode === 'login') {
      // 这里可以初始化微信二维码
      console.log('初始化微信登录二维码');
    }
  }, [loginDialogOpen, loginMode]);

  // 手机号验证
  const validatePhone = (phone: string) => {
    return /^1[3-9]\d{9}$/.test(phone);
  };
  const getWXCode = () => {
    // 清空现有的二维码容器
    const wxCodeContainer = document.getElementById('wxCode');
    if (wxCodeContainer) {
      wxCodeContainer.innerHTML = '';
    }

    wechatApi.wechatConfig().then((res: any) => {
      if(res.code === 0) {
        console.log('初始化微信二维码', res.data);
        
        // 确保 WxLogin 已加载
        if (typeof (window as any).WxLogin === 'undefined') {
          console.error('WxLogin 未加载');
          return;
        }

        // 初始化微信二维码
        const wxLoginObj = new (window as any).WxLogin({
          self_redirect: true,
          id: "wxCode", 
          appid: res.data.appId, 
          scope: res.data.scope, 
          redirect_uri: encodeURIComponent(`https://www.mcpcn.cc/redirect`),
          stylelite: res.data.stylelite,
          state: res.data.state,
          style: res.data.style,
          onQRcodeReady() {
            console.log('微信二维码已准备就绪');
          }
        });
        
        console.log('微信二维码初始化完成', wxLoginObj);
      }
    }).catch(error => {
      console.error('获取微信配置失败:', error);
    });
  }
  
  // 登录表单输入处理
  const handleLoginInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setLoginForm(prev => ({ ...prev, [name]: value }));
    // 清除对应字段的错误
    if (loginErrors[name as keyof typeof loginErrors]) {
      setLoginErrors(prev => ({ ...prev, [name]: '' }));
    }
  };
  const initYiDun = () => {
    const initNECaptcha = (window as any).initNECaptcha;
    initNECaptcha({
      captchaId: 'a9efe3dc7e974433841a50b5b747ed70',
      element: '#captcha',
      mode: 'popup',
      width: '320px',
      apiVersion: 2,
      popupStyles: {
        position: 'fixed',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        zIndex: 999999,
        capBarTextAlign: 'center',
        capBarBorderColor: '#fff',
        radius: 8,
        capBarHeight: 50,
        capPaddingTop: 0,
        paddingBottom: 9,
        capBarTextWeight: 500,
        capBarTextColor: '#333'
      },
      onVerify: (err: any, data: any) => {
        if (err) return
        setIsLoading(true)
        if(validateCallbackRef.current){
          validateCallbackRef.current(data.validate)
          validateCallbackRef.current = null
        }
        captchaInsRef.current && captchaInsRef.current.refresh()
      }
    }, (instance: any) => {
      captchaInsRef.current = instance
    }, (err: any) => {
      // 初始化失败
    })
  }
  // 注册表单输入处理
  const handleRegisterInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setRegisterForm(prev => ({ ...prev, [name]: value }));
    // 清除对应字段的错误
    if (registerErrors[name as keyof typeof registerErrors]) {
      setRegisterErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  // 忘记密码表单输入处理
  const handleForgotInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setForgotForm(prev => ({ ...prev, [name]: value }));
    // 清除对应字段的错误
    if (forgotErrors[name as keyof typeof forgotErrors]) {
      setForgotErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  // 绑定表单输入处理
  const handleBindInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setBindForm(prev => ({ ...prev, [name]: value }));
    // 清除对应字段的错误
    if (bindErrors[name as keyof typeof bindErrors]) {
      setBindErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  // 发送登录短信验证码
  const handleSendSmsCode = async () => {
    if (smsCountdown > 0) return;
    if (!validatePhone(loginForm.account)) {
      setLoginErrors(prev => ({ ...prev, account: '请输入正确的手机号' }));
      return;
    }

    setIsSendingSms(true);
    try {
      await baseApi.sendSMSCode({
        smsType: 2,
        phone: loginForm.account
      });
      setSmsCountdown(60);
      setSmsError('');
      toast({ title: '验证码已发送', variant: 'success' });
    } catch (error) {
      setSmsError('验证码发送失败');
      toast({ title: '验证码发送失败', variant: 'destructive' });
    } finally {
      setIsSendingSms(false);
    }
  };

  // 发送注册短信验证码
  const handleRegisterSendSms = async () => {
    if (registerCountdown > 0) return;
    if (!validatePhone(registerForm.account)) {
      setRegisterErrors(prev => ({ ...prev, account: '请输入正确的手机号' }));
      return;
    }

    setIsRegisterSending(true);
    try {
      await baseApi.sendSMSCode({
        smsType: 1,
        phone: registerForm.account
      });
      setRegisterCountdown(60);
      setRegisterErrors(prev => ({ ...prev, smsCode: '' }));
      toast({ title: '验证码已发送', variant: 'success' });
    } catch (error) {
      setRegisterErrors(prev => ({ ...prev, smsCode: '验证码发送失败' }));
      toast({ title: '验证码发送失败', variant: 'destructive' });
    } finally {
      setIsRegisterSending(false);
    }
  };

  // 发送找回密码短信验证码
  const handleForgotSendSms = async () => {
    if (forgotCountdown > 0) return;
    if (!validatePhone(forgotForm.account)) {
      setForgotErrors(prev => ({ ...prev, account: '请输入正确的手机号' }));
      return;
    }

    setIsForgotSending(true);
    try {
      await baseApi.sendSMSCode({
        phone: forgotForm.account
      });
      setForgotCountdown(60);
      setForgotErrors(prev => ({ ...prev, smsCode: '' }));
      toast({ title: '验证码已发送', variant: 'success' });
    } catch (error) {
      setForgotErrors(prev => ({ ...prev, smsCode: '验证码发送失败' }));
      toast({ title: '验证码发送失败', variant: 'destructive' });
    } finally {
      setIsForgotSending(false);
    }
  };

  // 发送绑定手机号短信验证码
  const handleBindSendSms = async () => {
    if (bindCountdown > 0) return;
    if (!validatePhone(bindForm.phone)) {
      setBindErrors(prev => ({ ...prev, phone: '请输入正确的手机号' }));
      return;
    }

    setIsBindSending(true);
    try {
      await baseApi.sendSMSCode({
        phone: bindForm.phone,
        smsType: 3, // 3 代表绑定手机号
      });
      setBindCountdown(60);
      setBindErrors(prev => ({ ...prev, code: '' }));
      toast({ title: '验证码已发送', variant: 'success' });
    } catch (error) {
      setBindErrors(prev => ({ ...prev, code: '验证码发送失败' }));
      toast({ title: '验证码发送失败', variant: 'destructive' });
    } finally {
      setIsBindSending(false);
    }
  };

  // 验证登录表单
  const validateLoginForm = () => {
    const newErrors = { account: '', password: '' };
    let isValid = true;

    if (!loginForm.account) {
      newErrors.account = '请输入手机号';
      isValid = false;
    } else if (!validatePhone(loginForm.account)) {
      newErrors.account = '请输入正确的手机号';
      isValid = false;
    }

    if (loginType === 'password') {
      if (!loginForm.password) {
        newErrors.password = '请输入密码';
        isValid = false;
      }
    } else {
      if (!smsCode) {
        setSmsError('请输入验证码');
        isValid = false;
      } else {
        setSmsError('');
      }
    }

    setLoginErrors(newErrors);
    return isValid;
  };

  // 验证注册表单
  const validateRegisterForm = () => {
    const newErrors = {
      account: '',
      password: '',
      confirmPassword: '',
      smsCode: '',
    };
    let isValid = true;

    if (!registerForm.account) {
      newErrors.account = '请输入手机号';
      isValid = false;
    } else if (!validatePhone(registerForm.account)) {
      newErrors.account = '请输入正确的手机号';
      isValid = false;
    }

    if (!registerForm.password) {
      newErrors.password = '请输入密码';
      isValid = false;
    } else if (registerForm.password.length < 6) {
      newErrors.password = '密码长度不能少于6位';
      isValid = false;
    }

    if (!registerForm.confirmPassword) {
      newErrors.confirmPassword = '请确认密码';
      isValid = false;
    } else if (registerForm.password !== registerForm.confirmPassword) {
      newErrors.confirmPassword = '两次输入的密码不一致';
      isValid = false;
    }

    if (!registerForm.smsCode) {
      newErrors.smsCode = '请输入验证码';
      isValid = false;
    }

    setRegisterErrors(newErrors);
    return isValid;
  };

  // 验证忘记密码表单
  const validateForgotForm = () => {
    const newErrors = {
      account: '',
      smsCode: '',
      password: '',
      confirmPassword: ''
    };
    let isValid = true;

    if (!forgotForm.account) {
      newErrors.account = '请输入手机号';
      isValid = false;
    } else if (!validatePhone(forgotForm.account)) {
      newErrors.account = '请输入正确的手机号';
      isValid = false;
    }

    if (!forgotForm.smsCode) {
      newErrors.smsCode = '请输入验证码';
      isValid = false;
    }

    if (!forgotForm.password) {
      newErrors.password = '请输入新密码';
      isValid = false;
    } else if (forgotForm.password.length < 6) {
      newErrors.password = '密码长度不能少于6位';
      isValid = false;
    }

    if (!forgotForm.confirmPassword) {
      newErrors.confirmPassword = '请确认新密码';
      isValid = false;
    } else if (forgotForm.password !== forgotForm.confirmPassword) {
      newErrors.confirmPassword = '两次输入的密码不一致';
      isValid = false;
    }

    setForgotErrors(newErrors);
    return isValid;
  };

  // 验证绑定表单
  const validateBindForm = () => {
    const newErrors = { phone: '', code: '' };
    let isValid = true;

    if (!bindForm.phone) {
      newErrors.phone = '请输入手机号';
      isValid = false;
    } else if (!validatePhone(bindForm.phone)) {
      newErrors.phone = '请输入正确的手机号';
      isValid = false;
    }

    if (!bindForm.code) {
      newErrors.code = '请输入验证码';
      isValid = false;
    }

    setBindErrors(newErrors);
    return isValid;
  };

  // 处理登录
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateLoginForm()) return;
    validateCallbackRef.current = loginFn;
    captchaInsRef.current && captchaInsRef.current.verify()
  };
  const loginFn = async (validate: string) => {
    setIsLoading(true);
    try {
      let params: any = {};

      if (loginType === 'password') {
        params = {
          provider: 'username',
          username: loginForm.account,
          password: loginForm.password,
          validate
        };
      } else {
        params = {
          provider: 'phone',
          phone: loginForm.account,
          code: smsCode,
          validate
        };
      }

      const result = await login(params);
      if (!result.success) {
        toast({ 
          title: '登录失败', 
          description: result.message || '请检查用户名和密码', 
          variant: 'destructive' 
        });
      }
    } catch (error: any) {
      const errorMessage = error?.message || '登录过程中发生错误，请稍后重试';
      toast({ 
        title: '登录失败', 
        description: errorMessage, 
        variant: 'destructive' 
      });
    } finally {
      setIsLoading(false);
    }
  }
  // 处理注册
  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateRegisterForm()) return;
    validateCallbackRef.current = registerFn;
    captchaInsRef.current && captchaInsRef.current.verify()
  };
  const registerFn = async (validate: string) => {
    setIsLoading(true);
    try {
      const result: any = await baseApi.phoneRegister({
        phone: registerForm.account,
        password: registerForm.password,
        code: registerForm.smsCode,
        validate
      });

      if (result.code === 0) {
        const loginResult = await login({
          user: result.data.user,
          token: result.data.token
        });
        if (loginResult.success) {
          toast({ title: '注册成功', variant: 'success' });
          setLoginMode('login');
        }
      } else {
        toast({ title: '注册失败', description: result.message, variant: 'destructive' });
      }
    } catch (error) {
      toast({ title: '注册失败', description: '注册过程中发生错误，请稍后重试', variant: 'destructive' });
    } finally {
      setIsLoading(false);
    }
  }
  // 处理忘记密码
  const handleForgotPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForgotForm()) return;

    setIsLoading(true);
    try {
      const result: any = await userApi.changePassword({
        phone: forgotForm.account,
        code: forgotForm.smsCode,
        newPassword: forgotForm.password
      });

      if (result.code === 0) {
        toast({ title: '密码重置成功', description: '请使用新密码登录', variant: 'success' });
        setLoginMode('login');
      } else {
        toast({ title: '密码重置失败', description: result.message, variant: 'destructive' });
      }
    } catch (error) {
      toast({ title: '密码重置失败', description: '重置过程中发生错误，请稍后重试', variant: 'destructive' });
    } finally {
      setIsLoading(false);
    }
  };

  // 处理绑定手机号
  const handleBindSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateBindForm()) return;

    setBindLoading(true);
    try {
      const result: any = await baseApi.bindWechatPhone({
        phone: bindForm.phone,
        code: bindForm.code,
        openId: bindUserData.openid,
        unionId: bindUserData.unionid,
        nickName: bindUserData.nickname,
        avatar: bindUserData.headimgurl
      });

      if (result.code === 0) {
        const loginResult = await login({
          user: result.data.user,
          token: result.data.token
        });
        if (loginResult.success) {
          toast({ title: '绑定成功', variant: 'success' });
        }
      } else {
        toast({ title: '绑定失败', description: result.message, variant: 'destructive' });
      }
    } catch (error) {
      toast({ title: '绑定失败', description: '绑定过程中发生错误，请稍后重试', variant: 'destructive' });
    } finally {
      setBindLoading(false);
    }
  };
  // 切换登录方式时清空表单
  const handleLoginTypeChange = (type: LoginType) => {
    setLoginType(type);
    setLoginForm({ account: '', password: '' });
    setLoginErrors({ account: '', password: '' });
    setSmsCode('');
    setSmsError('');
  };

  // 切换模式时清空表单
  const handleModeChange = (mode: LoginMode) => {
    console.log('切换模式到:', mode); // 添加调试日志
    setLoginMode(mode);
    resetAllForms();
  };

  // 直接处理模式切换，避免事件问题
  const handleForgotClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('点击忘记密码按钮');
    setLoginMode('forgot');
    console.log(loginMode, "forgot")
    resetAllForms();
  };

  const handleRegisterClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('点击立即注册按钮');
    setLoginMode('register');
    resetAllForms();
  };

  const handleBackToLoginClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('点击返回登录按钮');
    setLoginMode('login');
    resetAllForms();
  };

  // 处理键盘事件
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !isLoading) {
      if (loginMode === 'login') {
        handleLogin(e as any);
      } else if (loginMode === 'register') {
        handleRegister(e as any);
      } else if (loginMode === 'forgot') {
        handleForgotPassword(e as any);
      } else if (loginMode === 'bindPhone') {
        handleBindSubmit(e as any);
      }
    }
  };

  return (
    <Modal 
      open={loginDialogOpen} 
      width={loginMode === 'login' ? '680px' : '428px'} 
      centered 
      onCancel={() => closeLoginDialog()} 
      footer={null}
      className="login-dialog-modal"
    >
        <div className="flex min-h-[380px]">
          {/* 左侧表单区域 */}
                      <div 
              className="bg-white dark:bg-gray-900 p-6 flex flex-col justify-center"
              style={{ width: loginMode === 'login' ? '380px' : '380px', flexShrink: 0 }}
            >
            {loginMode === 'login' && (
              <>
                {/* 登录方式切换 */}
                <div className="mb-6 flex gap-6 justify-center text-lg font-medium">
                  <span
                    className={`cursor-pointer pb-1 ${
                      loginType === 'password' 
                        ? 'text-blue-600 border-b-2 border-blue-600' 
                        : 'text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300'
                    }`}
                    onClick={() => handleLoginTypeChange('password')}
                  >
                    密码登录
                  </span>
                  <span
                    className={`cursor-pointer pb-1 ${
                      loginType === 'sms' 
                        ? 'text-blue-600 border-b-2 border-blue-600' 
                        : 'text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300'
                    }`}
                    onClick={() => handleLoginTypeChange('sms')}
                  >
                    验证码登录
                  </span>
                </div>

                {loginType === 'password' && (
                  <form onSubmit={handleLogin} className="space-y-4">
                    {/* 手机号输入 */}
                    <div className="space-y-2">
                      <div className="relative">
                        <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 dark:text-gray-500" />
                        <Input
                          id="account"
                          name="account"
                          type="text"
                          placeholder="请输入手机号"
                          value={loginForm.account}
                          onChange={handleLoginInputChange}
                          onKeyPress={handleKeyPress}
                          className={`pl-10 ${loginErrors.account ? 'border-red-500' : ''}`}
                          disabled={isLoading}
                        />
                      </div>
                      {loginErrors.account && (
                        <p className="text-sm text-red-500">{loginErrors.account}</p>
                      )}
                    </div>

                    {/* 密码输入 */}
                    <div className="space-y-2">
                      <div className="relative">
                        <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 dark:text-gray-500" />
                        <Input
                          id="password"
                          name="password"
                          type={showPassword ? 'text' : 'password'}
                          placeholder="请输入密码"
                          value={loginForm.password}
                          onChange={handleLoginInputChange}
                          onKeyPress={handleKeyPress}
                          className={`pl-10 pr-10 ${loginErrors.password ? 'border-red-500' : ''}`}
                          disabled={isLoading}
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300"
                          disabled={isLoading}
                        >
                          {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </button>
                      </div>
                      {loginErrors.password && (
                        <p className="text-sm text-red-500">{loginErrors.password}</p>
                      )}
                    </div>

                    <div className="text-xs text-gray-400 dark:text-gray-500 mb-2">
                      注册/登录即代表已阅读并同意我们的用户协议与隐私政策
                    </div>

                    <Button type="submit" className="w-full" disabled={isLoading}>
                      {isLoading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          登录中...
                        </>
                      ) : (
                        '登录'
                      )}
                    </Button>
                  </form>
                )}

                {loginType === 'sms' && (
                  <form onSubmit={handleLogin} className="space-y-4">
                    {/* 手机号输入 */}
                    <div className="space-y-2">
                      <div className="relative">
                        <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 dark:text-gray-500" />
                        <Input
                          id="sms-account"
                          name="account"
                          type="text"
                          placeholder="请输入手机号"
                          value={loginForm.account}
                          onChange={handleLoginInputChange}
                          onKeyPress={handleKeyPress}
                          className={`pl-10 ${loginErrors.account ? 'border-red-500' : ''}`}
                          disabled={isLoading}
                        />
                      </div>
                      {loginErrors.account && (
                        <p className="text-sm text-red-500">{loginErrors.account}</p>
                      )}
                    </div>

                    {/* 验证码输入 */}
                    <div className="space-y-2">
                      <div className="flex gap-2">
                        <Input
                          id="sms-code"
                          type="text"
                          placeholder="请输入验证码"
                          value={smsCode}
                          onChange={(e) => setSmsCode(e.target.value)}
                          onKeyPress={handleKeyPress}
                          className={`${smsError ? 'border-red-500' : ''}`}
                          disabled={isLoading}
                        />
                        <Button
                          type="button"
                          variant="outline"
                          className="whitespace-nowrap"
                          onClick={handleSendSmsCode}
                          disabled={smsCountdown > 0 || isSendingSms || isLoading}
                        >
                          {smsCountdown > 0 ? `${smsCountdown}秒后重试` : isSendingSms ? '发送中...' : '获取验证码'}
                        </Button>
                      </div>
                      {smsError && (
                        <p className="text-sm text-red-500">{smsError}</p>
                      )}
                    </div>

                    <div className="text-xs text-gray-400 dark:text-gray-500 mb-2">
                      注册/登录即代表已阅读并同意我们的用户协议与隐私政策
                    </div>

                    <Button type="submit" className="w-full" disabled={isLoading}>
                      {isLoading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          登录中...
                        </>
                      ) : (
                        '登录'
                      )}
                    </Button>
                  </form>
                )}

                {/* 底部链接 */}
                <div className="flex justify-between mt-2 text-xs">
                  <Button
                    type="button"
                    variant="link"
                    className="h-auto p-0 text-blue-600 hover:text-blue-800 font-normal text-xs"
                    onClick={handleForgotClick}
                    disabled={isLoading}
                  >
                    忘记密码
                  </Button>
                  <Button
                    type="button"
                    variant="link"
                    className="h-auto p-0 text-blue-600 hover:text-blue-800 font-normal text-xs"
                    onClick={handleRegisterClick}
                    disabled={isLoading}
                  >
                    立即注册
                  </Button>
                </div>
              </>
            )}

            {loginMode === 'register' && (
              <>
                <div className="mb-6 flex gap-6 justify-center text-lg font-medium">
                  <span className="text-blue-600 border-b-2 border-blue-600 pb-1">注册</span>
                </div>
                <form onSubmit={handleRegister} className="space-y-4">
                  {/* 手机号输入 */}
                  <div className="space-y-2">
                    <div className="relative">
                      <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 dark:text-gray-500" />
                      <Input
                        id="register-account"
                        name="account"
                        type="text"
                        placeholder="请输入手机号"
                        value={registerForm.account}
                        onChange={handleRegisterInputChange}
                        onKeyPress={handleKeyPress}
                        className={`pl-10 ${registerErrors.account ? 'border-red-500' : ''}`}
                        disabled={isLoading}
                      />
                    </div>
                    {registerErrors.account && (
                      <p className="text-sm text-red-500">{registerErrors.account}</p>
                    )}
                  </div>

                  {/* 验证码输入 */}
                  <div className="space-y-2">
                    <div className="flex gap-2">
                      <Input
                        id="register-sms-code"
                        name="smsCode"
                        type="text"
                        placeholder="请输入验证码"
                        value={registerForm.smsCode}
                        onChange={handleRegisterInputChange}
                        onKeyPress={handleKeyPress}
                        className={`${registerErrors.smsCode ? 'border-red-500' : ''}`}
                        disabled={isLoading}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        className="whitespace-nowrap"
                        onClick={handleRegisterSendSms}
                        disabled={registerCountdown > 0 || isRegisterSending || isLoading}
                      >
                        {registerCountdown > 0 ? `${registerCountdown}秒后重试` : isRegisterSending ? '发送中...' : '获取验证码'}
                      </Button>
                    </div>
                    {registerErrors.smsCode && (
                      <p className="text-sm text-red-500">{registerErrors.smsCode}</p>
                    )}
                  </div>

                  {/* 密码输入 */}
                  <div className="space-y-2">
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 dark:text-gray-500" />
                      <Input
                        id="register-password"
                        name="password"
                        type={showRegisterPassword ? 'text' : 'password'}
                        placeholder="请设置密码"
                        value={registerForm.password}
                        onChange={handleRegisterInputChange}
                        onKeyPress={handleKeyPress}
                        className={`pl-10 pr-10 ${registerErrors.password ? 'border-red-500' : ''}`}
                        disabled={isLoading}
                      />
                      <button
                        type="button"
                        onClick={() => setShowRegisterPassword((v) => !v)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300"
                        disabled={isLoading}
                      >
                        {showRegisterPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                    </div>
                    {registerErrors.password && (
                      <p className="text-sm text-red-500">{registerErrors.password}</p>
                    )}
                  </div>

                  {/* 确认密码输入 */}
                  <div className="space-y-2">
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 dark:text-gray-500" />
                      <Input
                        id="register-confirm-password"
                        name="confirmPassword"
                        type={showRegisterConfirmPassword ? 'text' : 'password'}
                        placeholder="请确认密码"
                        value={registerForm.confirmPassword}
                        onChange={handleRegisterInputChange}
                        onKeyPress={handleKeyPress}
                        className={`pl-10 pr-10 ${registerErrors.confirmPassword ? 'border-red-500' : ''}`}
                        disabled={isLoading}
                      />
                      <button
                        type="button"
                        onClick={() => setShowRegisterConfirmPassword((v) => !v)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300"
                        disabled={isLoading}
                      >
                        {showRegisterConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                    </div>
                    {registerErrors.confirmPassword && (
                      <p className="text-sm text-red-500">{registerErrors.confirmPassword}</p>
                    )}
                  </div>

                  <div className="text-xs text-gray-500 mb-2">
                    注册即代表已阅读并同意相关服务条款
                  </div>

                  <Button type="submit" className="w-full" disabled={isLoading}>
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        注册中...
                      </>
                    ) : (
                      '注册'
                    )}
                  </Button>
                </form>

                <div className="flex justify-between mt-2 text-xs">
                  <Button
                    type="button"
                    variant="link"
                    className="h-auto p-0 text-blue-600 hover:text-blue-800 font-normal text-xs"
                    onClick={handleBackToLoginClick}
                    disabled={isLoading}
                  >
                    已有账号？去登录
                  </Button>
                </div>
              </>
            )}

            {loginMode === 'forgot' && (
              <>
                <div className="mb-6 flex gap-6 justify-center text-lg font-medium">
                  <span className="text-blue-600 border-b-2 border-blue-600 pb-1">找回密码</span>
                </div>
                <form onSubmit={handleForgotPassword} className="space-y-4">
                  {/* 手机号输入 */}
                  <div className="space-y-2">
                    <div className="relative">
                      <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 dark:text-gray-500" />
                      <Input
                        id="forgot-account"
                        name="account"
                        type="text"
                        placeholder="请输入手机号"
                        value={forgotForm.account}
                        onChange={handleForgotInputChange}
                        onKeyPress={handleKeyPress}
                        className={`pl-10 ${forgotErrors.account ? 'border-red-500' : ''}`}
                        disabled={isLoading}
                      />
                    </div>
                    {forgotErrors.account && (
                      <p className="text-sm text-red-500">{forgotErrors.account}</p>
                    )}
                  </div>

                  {/* 验证码输入 */}
                  <div className="space-y-2">
                    <div className="flex gap-2">
                      <Input
                        id="forgot-sms-code"
                        name="smsCode"
                        type="text"
                        placeholder="请输入验证码"
                        value={forgotForm.smsCode}
                        onChange={handleForgotInputChange}
                        onKeyPress={handleKeyPress}
                        className={`${forgotErrors.smsCode ? 'border-red-500' : ''}`}
                        disabled={isLoading}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        className="whitespace-nowrap"
                        onClick={handleForgotSendSms}
                        disabled={forgotCountdown > 0 || isForgotSending || isLoading}
                      >
                        {forgotCountdown > 0 ? `${forgotCountdown}秒后重试` : isForgotSending ? '发送中...' : '获取验证码'}
                      </Button>
                    </div>
                    {forgotErrors.smsCode && (
                      <p className="text-sm text-red-500">{forgotErrors.smsCode}</p>
                    )}
                  </div>

                  {/* 新密码输入 */}
                  <div className="space-y-2">
                    <div className="relative">
                      <KeyRound className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 dark:text-gray-500" />
                      <Input
                        id="forgot-password"
                        name="password"
                        type={showForgotPassword ? 'text' : 'password'}
                        placeholder="请输入新密码"
                        value={forgotForm.password}
                        onChange={handleForgotInputChange}
                        onKeyPress={handleKeyPress}
                        className={`pl-10 pr-10 ${forgotErrors.password ? 'border-red-500' : ''}`}
                        disabled={isLoading}
                      />
                      <button
                        type="button"
                        onClick={() => setShowForgotPassword((v) => !v)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300"
                        disabled={isLoading}
                      >
                        {showForgotPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                    </div>
                    {forgotErrors.password && (
                      <p className="text-sm text-red-500">{forgotErrors.password}</p>
                    )}
                  </div>

                  {/* 确认新密码输入 */}
                  <div className="space-y-2">
                    <div className="relative">
                      <KeyRound className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 dark:text-gray-500" />
                      <Input
                        id="forgot-confirm-password"
                        name="confirmPassword"
                        type={showForgotConfirmPassword ? 'text' : 'password'}
                        placeholder="请确认新密码"
                        value={forgotForm.confirmPassword}
                        onChange={handleForgotInputChange}
                        onKeyPress={handleKeyPress}
                        className={`pl-10 pr-10 ${forgotErrors.confirmPassword ? 'border-red-500' : ''}`}
                        disabled={isLoading}
                      />
                      <button
                        type="button"
                        onClick={() => setShowForgotConfirmPassword((v) => !v)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300"
                        disabled={isLoading}
                      >
                        {showForgotConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                    </div>
                    {forgotErrors.confirmPassword && (
                      <p className="text-sm text-red-500">{forgotErrors.confirmPassword}</p>
                    )}
                  </div>

                  <Button type="submit" className="w-full" disabled={isLoading}>
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        重置中...
                      </>
                    ) : (
                      '重置密码'
                    )}
                  </Button>
                </form>

                <div className="flex justify-between mt-2 text-xs">
                  <Button
                    type="button"
                    variant="link"
                    className="h-auto p-0 text-blue-600 hover:text-blue-800 font-normal text-xs"
                    onClick={handleBackToLoginClick}
                    disabled={isLoading}
                  >
                    返回登录
                  </Button>
                </div>
              </>
            )}

            {loginMode === 'bindPhone' && (
              <>
                <div className="mb-6 flex gap-6 justify-center text-lg font-medium">
                  <span className="text-blue-600 border-b-2 border-blue-600 pb-1">绑定手机号</span>
                </div>
                <form onSubmit={handleBindSubmit} className="space-y-4">
                  {/* 手机号输入 */}
                  <div className="space-y-2">
                    <div className="relative">
                      <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 dark:text-gray-500" />
                      <Input
                        id="bind-phone"
                        name="phone"
                        type="text"
                        placeholder="请输入手机号"
                        value={bindForm.phone}
                        onChange={handleBindInputChange}
                        onKeyPress={handleKeyPress}
                        className={`pl-10 ${bindErrors.phone ? 'border-red-500' : ''}`}
                        disabled={bindLoading}
                      />
                    </div>
                    {bindErrors.phone && (
                      <p className="text-sm text-red-500">{bindErrors.phone}</p>
                    )}
                  </div>

                  {/* 验证码输入 */}
                  <div className="space-y-2">
                    <div className="flex gap-2">
                      <Input
                        id="bind-code"
                        name="code"
                        type="text"
                        placeholder="请输入验证码"
                        value={bindForm.code}
                        onChange={handleBindInputChange}
                        onKeyPress={handleKeyPress}
                        className={`${bindErrors.code ? 'border-red-500' : ''}`}
                        disabled={bindLoading}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        className="whitespace-nowrap"
                        onClick={handleBindSendSms}
                        disabled={bindCountdown > 0 || isBindSending || bindLoading}
                      >
                        {bindCountdown > 0 ? `${bindCountdown}秒后重试` : isBindSending ? '发送中...' : '获取验证码'}
                      </Button>
                    </div>
                    {bindErrors.code && (
                      <p className="text-sm text-red-500">{bindErrors.code}</p>
                    )}
                  </div>

                  <Button type="submit" className="w-full" disabled={bindLoading}>
                    {bindLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        绑定中...
                      </>
                    ) : (
                      '绑定并登录'
                    )}
                  </Button>
                </form>
              </>
            )}
          </div>

          {/* 右侧二维码区域 */}
          <div className="w-60 flex flex-col items-center justify-center border-l border-gray-200 dark:border-gray-700" style={{ display: loginMode === 'login' ? 'flex' : 'none' }}>
            <div className="bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center" style={{width: '160px', height: '160px'}}>
              {/* 这里可以放置真实的微信二维码 */}
              <div className="text-center" id="wxCode" style={{width: '160px', height: '160px'}}></div>
            </div>
            <div className="flex items-center gap-2 mt-2">
              <svg width="20" height="20" viewBox="0 0 48 48" fill="none"><path fillRule="evenodd" clipRule="evenodd" d="M36.9974 21.7112C36.8434 13.0079 29.7401 6 21 6C12.1634 6 5 13.1634 5 22C5 26.1701 6.59531 29.9676 9.20892 32.8154L8.01043 40.0257L15.125 36.9699C18.2597 38.0122 21.218 38.2728 24 37.7516" fill="#1AAD19"></path><path d="M36.9974 21.7112C36.8434 13.0079 29.7401 6 21 6C12.1634 6 5 13.1634 5 22C5 26.1701 6.59531 29.9676 9.20892 32.8154L8.01043 40.0257L15.125 36.9699C18.2597 38.0122 21.218 38.2728 24 37.7516" stroke="#1AAD19" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round"></path><path d="M15.125 20.4667C16.3676 20.4667 17.375 19.4519 17.375 18.2C17.375 16.9482 16.3676 15.9333 15.125 15.9333C13.8824 15.9333 12.875 16.9482 12.875 18.2C12.875 19.4519 13.8824 20.4667 15.125 20.4667Z" fill="#FFF"></path><path d="M24.125 20.4667C25.3676 20.4667 26.375 19.4519 26.375 18.2C26.375 16.9482 25.3676 15.9333 24.125 15.9333C22.8824 15.9333 21.875 16.9482 21.875 18.2C21.875 19.4519 22.8824 20.4667 24.125 20.4667Z" fill="#FFF"></path><path fillRule="evenodd" clipRule="evenodd" d="M38.7618 39.9293C37.0135 41.2302 34.8467 42 32.5 42C26.701 42 22 37.299 22 31.5C22 25.701 26.701 21 32.5 21C38.299 21 43 25.701 43 31.5C43 33.0997 42.6423 34.6159 42.0024 35.9728" fill="#1AAD19"></path><path d="M38.7618 39.9293C37.0135 41.2302 34.8467 42 32.5 42C26.701 42 22 37.299 22 31.5C22 25.701 26.701 21 32.5 21C38.299 21 43 25.701 43 31.5C43 33.0997 42.6423 34.6159 42.0024 35.9728" stroke="#1AAD19" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round"></path><path fillRule="evenodd" clipRule="evenodd" d="M42.0024 35.9728L43 42L38.7618 39.9293" fill="#1AAD19"></path><path d="M42.0024 35.9728L43 42L38.7618 39.9293" stroke="#1AAD19" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round"></path><path d="M35.6875 30.7999C34.7555 30.7999 34 30.0388 34 29.0999C34 28.161 34.7555 27.3999 35.6875 27.3999C36.6195 27.3999 37.375 28.161 37.375 29.0999C37.375 30.0388 36.6195 30.7999 35.6875 30.7999Z" fill="#FFF"></path><path d="M28.9375 30.7999C28.0055 30.7999 27.25 30.0388 27.25 29.0999C27.25 28.161 28.0055 27.3999 28.9375 27.3999C29.8695 27.3999 30.625 28.161 30.625 29.0999C30.625 30.0388 29.8695 30.7999 28.9375 30.7999Z" fill="#FFF"></path></svg>
              <span className="text-gray-600 dark:text-gray-300 text-sm">微信扫码登录</span>
            </div>
          </div>
        </div>
    </Modal>
  );
};

export default LoginDialog; 