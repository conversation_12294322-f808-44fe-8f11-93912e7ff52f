import React from 'react';
import { cn } from '../../utils';

interface SwitchProps {
  checked?: boolean;
  onChange?: (checked: boolean) => void;
  disabled?: boolean;
  size?: 'small' | 'medium' | 'large';
  className?: string;
}

export const Switch: React.FC<SwitchProps> = ({ 
  checked = false, 
  onChange, 
  disabled = false,
  size = 'medium',
  className,
  ...props 
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!disabled && onChange) {
      onChange(e.target.checked);
    }
  };

  const sizeClasses = {
    small: 'w-9 h-5',
    medium: 'w-11 h-6', 
    large: 'w-14 h-7'
  };

  const thumbSizeClasses = {
    small: 'w-4 h-4',
    medium: 'w-5 h-5',
    large: 'w-6 h-6' 
  };

  const thumbPositionClasses = {
    small: checked ? 'translate-x-4' : 'translate-x-0',
    medium: checked ? 'translate-x-5' : 'translate-x-0',
    large: checked ? 'translate-x-7' : 'translate-x-0'
  };

  return (
    <label className={cn(
      "relative inline-flex items-center cursor-pointer",
      disabled && "cursor-not-allowed opacity-60",
      className
    )}>
      <input
        type="checkbox"
        checked={checked}
        onChange={handleChange}
        disabled={disabled}
        className="sr-only"
        {...props}
      />
      <div className={cn(
        "relative inline-flex items-center rounded-full transition-colors duration-200",
        sizeClasses[size],
        checked 
          ? "bg-blue-600 hover:bg-blue-700" 
          : "bg-gray-300 hover:bg-gray-400 dark:bg-gray-600 dark:hover:bg-gray-500",
        disabled && "hover:bg-gray-300 dark:hover:bg-gray-600"
      )}>
        <span className={cn(
          "inline-block rounded-full bg-white shadow-lg transform transition-transform duration-200",
          thumbSizeClasses[size],
          thumbPositionClasses[size],
          "ml-0.5"
        )} />
      </div>
    </label>
  );
}; 