import * as React from "react"
import { cn } from "../../utils"
import RcInput from "rc-input"
import type { InputProps as RcInputProps, InputRef } from "rc-input"
import "rc-input/assets/index.css"

export type InputProps = Omit<RcInputProps, "prefixCls" | "className"> & {
  className?: string
}

const Input = React.forwardRef<InputRef, InputProps>(
  ({ className, ...props }, ref) => {
    return (
      <RcInput
        prefixCls="rc-input"
        className={cn(
          "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
Input.displayName = "Input"

export { Input } 