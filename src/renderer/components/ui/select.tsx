import React from 'react';
import { cn } from '../../utils';

interface SelectOption {
  value: string;
  label: string;
}

interface SelectProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'onChange'> {
  options: SelectOption[];
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  className?: string;
  minWidth?: string;
}

export const Select: React.FC<SelectProps> = ({
  options,
  value,
  onChange,
  placeholder,
  className,
  minWidth = "min-w-[120px]",
  disabled,
  ...props
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onChange?.(e.target.value);
  };

  return (
    <select
      value={value}
      onChange={handleChange}
      disabled={disabled}
      className={cn(
        "px-3 py-1.5 border border-gray-300 rounded-md bg-white text-sm",
        "focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none",
        "dark:bg-gray-800 dark:border-gray-700 dark:text-gray-300",
        "disabled:opacity-50 disabled:cursor-not-allowed",
        minWidth,
        className
      )}
      {...props}
    >
      {placeholder && (
        <option value="" disabled>
          {placeholder}
        </option>
      )}
      {options.map((option) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  );
}; 