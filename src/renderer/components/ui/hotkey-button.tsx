import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useToast } from './use-toast';
import { systemShortcutChecker } from '../../../shared/system-shortcuts';

interface HotkeyButtonProps {
  /** 快捷键唯一标识符 */
  shortcutId: string;
  /** 当前快捷键值 */
  value: string;
  /** 快捷键变化回调 */
  onChange: (newShortcut: string) => void;
  /** 自定义样式类名 */
  simpleMode?: boolean;
  /** 是否禁用 */
  disabled?: boolean;
  /** 冲突检查函数 */
  checkConflict?: (shortcut: string) => boolean;
}

// 修饰键图标组件
const ModifierKeyIcon: React.FC<{ 
  keyName: string; 
  isPressed: boolean; 
  status: 'normal' | 'success' | 'error' 
}> = ({ keyName, isPressed, status }) => {
  const isMac = navigator.platform.indexOf('Mac') > -1;
  
  const getKeyLabel = () => {
    if (isMac) {
      switch (keyName) {
        case 'Cmd': return '⌘';
        case 'Option': return '⌥';
        case 'Shift': return '⇧';
        case 'Ctrl': return '⌃';
        default: return keyName;
      }
    } else {
      switch (keyName) {
        case 'Ctrl': return 'Ctrl';
        case 'Alt': return 'Alt';
        case 'Shift': return 'Shift';
        case 'Win': return '⊞';
        default: return keyName;
      }
    }
  };

  const getKeyStyles = () => {
    const baseStyles = 'inline-flex items-center justify-center w-6 h-6 rounded border text-xs font-medium transition-all duration-150';
    
    if (status === 'success') {
      return `${baseStyles} bg-green-100 text-green-700 border-green-300 dark:bg-green-900/30 dark:text-green-400 dark:border-green-600`;
    }
    
    if (status === 'error') {
      return `${baseStyles} bg-red-100 text-red-700 border-red-300 dark:bg-red-900/30 dark:text-red-400 dark:border-red-600`;
    }
    
    if (isPressed) {
      return `${baseStyles} bg-blue-100 text-blue-700 border-blue-300 dark:bg-blue-900/30 dark:text-blue-400 dark:border-blue-600`;
    }
    
    return `${baseStyles} bg-gray-100 text-gray-600 border-gray-300 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600`;
  };

  return (
    <span className={getKeyStyles()}>
      {getKeyLabel()}
    </span>
  );
};

// 主键显示组件
const MainKeyDisplay: React.FC<{ 
  keys: string[]; 
  status: 'normal' | 'success' | 'error' 
}> = ({ keys, status }) => {
  const mainKeys = keys.filter(key => 
    !['Cmd', 'Option', 'Shift', 'Ctrl', 'Alt', 'Win'].includes(key)
  );
  
  if (mainKeys.length === 0) return null;

  const getTextStyles = () => {
    const baseStyles = 'ml-1 px-2 py-1 rounded text-xs font-medium';
    
    if (status === 'success') {
      return `${baseStyles} bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400`;
    }
    
    if (status === 'error') {
      return `${baseStyles} bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400`;
    }
    
    return `${baseStyles} bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400`;
  };

  return (
    <span className={getTextStyles()}>
      {mainKeys.join('+')}
    </span>
  );
};

/**
 * 快捷键设置按钮组件
 * 可复用的快捷键录制和显示组件
 */
export const HotkeyButton: React.FC<HotkeyButtonProps> = ({
  shortcutId,
  value,
  onChange,
  simpleMode = false,
  disabled = false,
  checkConflict
}) => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const [hotkey, setHotkey] = useState(value);
  const [status, setStatus] = useState<'normal' | 'success' | 'error'>('normal');
  const [isCapturing, setIsCapturing] = useState(false);
  const [capturedKeys, setCapturedKeys] = useState<string[]>([]);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  
  const isMac = navigator.platform.indexOf('Mac') > -1;

  // 同步外部value变化
  useEffect(() => {
    setHotkey(value);
  }, [value]);

  // 检查是否为系统级快捷键
  const isSystemShortcut = (shortcut: string): boolean => {
    const isSystem = systemShortcutChecker.isSystemShortcut(shortcut);
    console.log('检查系统快捷键:', shortcut, '结果:', isSystem, '平台:', systemShortcutChecker.getPlatform());
    return isSystem;
  };

  // 开始捕获快捷键
  const startCapturing = async () => {
    try {
      if (window.electron?.shortcut) {
        console.log('正在禁用所有快捷键...');
        const success = await window.electron.shortcut.disableAllShortcuts();
        if (!success) {
          console.error('禁用快捷键失败');
          toast({
            title: '无法禁用快捷键，请重试',
            variant: 'destructive'
          });
          return;
        }

        // 等待一段时间确保禁用操作完成
        await new Promise(resolve => setTimeout(resolve, 200));
        console.log('快捷键禁用完成');
      }
    } catch (error) {
      console.error('禁用快捷键失败:', error);
      toast({
        title: '禁用快捷键失败：' + (error as Error).message,
        variant: 'destructive'
      });
      return;
    }

    setIsCapturing(true);
    setCapturedKeys([]);
    setErrorMessage(null);
    setStatus('normal');
  };

  // 取消捕获
  const cancelCapturing = async () => {
    try {
      if (window.electron?.shortcut) {
        console.log('正在恢复所有快捷键...');
        const success = await window.electron.shortcut.restoreAllShortcuts();
        if (!success) {
          console.error('恢复快捷键失败');
        } else {
          console.log('快捷键恢复完成');
        }
      }
    } catch (error) {
      console.error('恢复快捷键失败:', error);
    }

    setIsCapturing(false);
    setCapturedKeys([]);
    setErrorMessage(null);
    setStatus('normal');
  };

  // 注册快捷键
  const registerShortcut = async (keys: string[]) => {
    if (keys.length === 0) return;

    const shortcutString = keys.join('+');
    
    try {
      // 检查快捷键API是否可用
      if (!window.electron?.shortcut) {
        setErrorMessage('API不可用');
        setStatus('error');
        toast({
          title: '无法设置快捷键，请检查应用权限',
          variant: 'destructive'
        });
        return;
      }

      // 检查是否与其他应用冲突
      if (checkConflict && checkConflict(shortcutString)) {
        setErrorMessage('应用冲突');
        setStatus('error');
        toast({
          title: '快捷键冲突',
          variant: 'destructive'
        });
        return;
      }

      // 检查快捷键是否可用
      const isAvailable = await window.electron.shortcut.checkShortcutAvailable(shortcutString);
      
      if (!isAvailable) {
        setErrorMessage('已被占用');
        setStatus('error');
        toast({
          title: '此快捷键已被占用，请尝试其他快捷键组合',
          variant: 'destructive'
        });
        return;
      }

      // 成功设置
      setHotkey(shortcutString);
      onChange(shortcutString);
      setStatus('success');

      // 结束快捷键设置模式并恢复快捷键
      console.log('正在恢复所有快捷键...');
      const restoreSuccess = await window.electron.shortcut.restoreAllShortcuts();
      if (!restoreSuccess) {
        console.error('恢复快捷键失败');
      } else {
        console.log('快捷键恢复完成');
      }

      setIsCapturing(false);
      setCapturedKeys([]);
      setErrorMessage(null);

      // 2秒后恢复正常状态
      setTimeout(() => {
        setStatus('normal');
      }, 2000);
      
    } catch (error) {
      setErrorMessage('设置失败');
      setStatus('error');
      toast({
        title: '快捷键设置失败：' + (error as Error).message,
        variant: 'destructive'
      });
      console.error('快捷键设置失败:', error);
    }
  };

  // 快捷键监听效果
  useEffect(() => {
    if (!isCapturing) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      e.preventDefault();
      e.stopPropagation();

      // ESC键取消设置
      if (e.key === 'Escape') {
        cancelCapturing();
        return;
      }

      const keys: string[] = [];
      
      // 检查修饰键
      if (isMac) {
        if (e.metaKey) keys.push('Cmd');
        if (e.altKey) keys.push('Option');
        if (e.shiftKey) keys.push('Shift');
        if (e.ctrlKey) keys.push('Ctrl');
      } else {
        if (e.ctrlKey) keys.push('Ctrl');
        if (e.altKey) keys.push('Alt');
        if (e.shiftKey) keys.push('Shift');
        if (e.metaKey) keys.push('Win');
      }

      // 添加主键
      if (e.key && e.key !== 'Control' && e.key !== 'Alt' && e.key !== 'Shift' && e.key !== 'Meta' && e.key !== 'OS') {
        let keyName = e.key;
        
        // 特殊键映射
        const specialKeyMap: { [key: string]: string } = {
          'Space': 'Space',
          'Enter': 'Enter',
          'Escape': 'Escape',
          'Tab': 'Tab',
          'Backspace': 'Backspace',
          'Delete': 'Delete',
          'Insert': 'Insert',
          'Home': 'Home',
          'End': 'End',
          'PageUp': 'PageUp',
          'PageDown': 'PageDown',
          'ArrowUp': 'Up',
          'ArrowDown': 'Down',
          'ArrowLeft': 'Left',
          'ArrowRight': 'Right',
          'F1': 'F1', 'F2': 'F2', 'F3': 'F3', 'F4': 'F4',
          'F5': 'F5', 'F6': 'F6', 'F7': 'F7', 'F8': 'F8',
          'F9': 'F9', 'F10': 'F10', 'F11': 'F11', 'F12': 'F12'
        };
        
        if (e.code) {
          if (e.code.startsWith('Key')) {
            keyName = e.code.substring(3);
          } else if (e.code.startsWith('Digit')) {
            keyName = e.code.substring(5);
          } else if (e.code.startsWith('Numpad')) {
            keyName = e.code.substring(6);
          } else if (specialKeyMap[e.code]) {
            keyName = specialKeyMap[e.code];
          } else {
            keyName = e.key === ' ' ? 'Space' : e.key;
          }
        } else {
          keyName = e.key === ' ' ? 'Space' : e.key;
        }
        
        keys.push(keyName.toUpperCase());
      }

      // 更新捕获的按键（无论是否有主键都要更新显示状态）
      setCapturedKeys(keys);
      setErrorMessage(null);
      setStatus('normal');

      // 检查是否有主键（字母、数字、空格等）
      const hasMainKey = keys.some(key => 
        !['Cmd', 'Option', 'Shift', 'Ctrl', 'Alt', 'Win'].includes(key)
      );

      // 只有在有主键时才进行验证和注册
      if (hasMainKey) {
        // 验证是否为有效的组合键
        const hasModifier = keys.includes('Ctrl') || keys.includes('Alt') || keys.includes('Option') || keys.includes('Cmd') || keys.includes('Win');
        
        if (!hasModifier) {
          setErrorMessage('需要修饰键');
          setStatus('error');
          return;
        }

        // 检查是否为系统级快捷键
        const shortcutString = keys.join('+');
        if (isSystemShortcut(shortcutString)) {
          setErrorMessage('系统快捷键');
          setStatus('error');
          toast({
            title: `${shortcutString} 是系统保留快捷键，请尝试其他快捷键组合`,
            variant: 'destructive'
          });
          return;
        }

        // 延迟处理快捷键注册
        setTimeout(() => {
          registerShortcut(keys);
        }, 300);
      }
    };

    document.addEventListener('keydown', handleKeyDown, true);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown, true);
    };
  }, [isCapturing, isMac]);

  const handleClick = () => {
    if (disabled) return;
    
    if (isCapturing) {
      cancelCapturing();
    } else {
      startCapturing();
    }
  };

  const renderContent = () => {
    // 设置状态
    if (isCapturing) {
      const modifierKeys = isMac ? ['Cmd', 'Option', 'Shift', 'Ctrl'] : ['Ctrl', 'Alt', 'Shift', 'Win'];
      const currentKeys = capturedKeys;
      
      // 检查是否有主键（字母、数字、空格等）
      const hasMainKey = currentKeys.some(key => 
        !['Cmd', 'Option', 'Shift', 'Ctrl', 'Alt', 'Win'].includes(key)
      );
      
      // 简单模式：仅显示文本
      if (simpleMode) {
        return (
          <div className="text-xs">
            {currentKeys.length > 0 ? (
              <span className={`${
                status === 'error' ? 'text-red-600 dark:text-red-400' :
                status === 'success' ? 'text-green-600 dark:text-green-400' :
                'text-blue-600 dark:text-blue-400'
              }`}>
                {currentKeys.join('+')}
              </span>
            ) : (
              <span className="text-gray-500 dark:text-gray-400">
                {t('settings.hotkeys.pressKey', '按下快捷键...')}
              </span>
            )}
          </div>
        );
      }
      
      // 完整模式：显示图标
      return (
        <div className="flex items-center gap-1">
          {modifierKeys.map(key => (
            <ModifierKeyIcon
              key={key}
              keyName={key}
              isPressed={currentKeys.includes(key)}
              status={status}
            />
          ))}
          <MainKeyDisplay keys={currentKeys} status={status} />
          {/* 只有在没有主键时才显示"请按键..." */}
          {!hasMainKey && (
            <span className="text-gray-500 dark:text-gray-400 text-xs ml-2">
              {t('settings.hotkeys.pressKey', '按下快捷键...')}
            </span>
          )}
        </div>
      );
    }
    
    // 正常显示状态
    if (!hotkey || hotkey.trim() === '') {
      return (
        <span className="text-gray-500 dark:text-gray-400 text-xs">
          快捷键
        </span>
      );
    }
    
    // 简单模式：仅显示文本
    if (simpleMode) {
      return (
        <span className={`text-xs ${
          status === 'success' 
            ? 'text-green-600 dark:text-green-400'
            : status === 'error'
            ? 'text-red-600 dark:text-red-400'
            : 'text-gray-700 dark:text-gray-300'
        }`}>
          {hotkey}
        </span>
      );
    }
    
    // 完整模式：解析并显示现有快捷键
    const keys = hotkey.split('+');
    const modifierKeys = keys.filter(key => 
      ['Cmd', 'Option', 'Shift', 'Ctrl', 'Alt', 'Win'].includes(key)
    );
    const mainKeys = keys.filter(key => 
      !['Cmd', 'Option', 'Shift', 'Ctrl', 'Alt', 'Win'].includes(key)
    );
    
    return (
      <div className="flex items-center gap-1">
        {modifierKeys.map((key, index) => (
          <ModifierKeyIcon
            key={`${key}-${index}`}
            keyName={key}
            isPressed={false}
            status={status}
          />
        ))}
        {mainKeys.length > 0 && (
          <span className={`px-2 py-1 rounded text-xs font-medium ${
            status === 'success' 
              ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'
              : status === 'error'
              ? 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400'
              : 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300'
          }`}>
            {mainKeys.join('+')}
          </span>
        )}
      </div>
    );
  };

  const getButtonStyles = () => {
    // 简单模式：无边框样式
    if (simpleMode) {
      return 'inline-flex items-center cursor-pointer select-none transition-all duration-150 min-h-[20px]';
    }
    
    // 完整模式：带边框和背景
    const baseStyles = 'inline-flex items-center px-3 py-2 border rounded-lg cursor-pointer select-none transition-all duration-150 min-h-[36px]';
    
    if (disabled) {
      return `${baseStyles} bg-gray-50 border-gray-200 cursor-not-allowed dark:bg-gray-900 dark:border-gray-700`;
    }
    
    if (isCapturing) {
      if (status === 'error') {
        return `${baseStyles} bg-red-50 border-red-300 hover:bg-red-100 dark:bg-red-900/20 dark:border-red-600 dark:hover:bg-red-900/30`;
      } else if (status === 'success') {
        return `${baseStyles} bg-green-50 border-green-300 dark:bg-green-900/20 dark:border-green-600`;
      } else {
        return `${baseStyles} bg-blue-50 border-blue-300 hover:bg-blue-100 dark:bg-blue-900/20 dark:border-blue-600 dark:hover:bg-blue-900/30`;
      }
    }
    
    return `${baseStyles} bg-white border-gray-300 hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:hover:bg-gray-700`;
  };

  return (
    <div
      className={getButtonStyles()}
      onClick={handleClick}
    >
      {renderContent()}
    </div>
  );
}; 