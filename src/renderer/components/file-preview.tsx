import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { FileText, Image, FileCode, Music, Video, File } from 'lucide-react';
import { Button } from './ui/button';

interface FilePreviewProps {
  filePath: string | null;
  fileName: string | null;
  fileType: string | null;
  fileSize: number | null;
  onOpen: () => void;
  onOpenLocation: () => void;
}

const formatFileSize = (size: number): string => {
  if (size < 1024) {
    return `${size} B`;
  } else if (size < 1024 * 1024) {
    return `${(size / 1024).toFixed(2)} KB`;
  } else if (size < 1024 * 1024 * 1024) {
    return `${(size / (1024 * 1024)).toFixed(2)} MB`;
  } else {
    return `${(size / (1024 * 1024 * 1024)).toFixed(2)} GB`;
  }
};

const FilePreview: React.FC<FilePreviewProps> = ({
  filePath,
  fileName,
  fileType,
  fileSize,
  onOpen,
  onOpenLocation
}) => {
  const { t } = useTranslation();
  const [previewContent, setPreviewContent] = useState<string | null>(null);
  const [previewType, setPreviewType] = useState<'text' | 'image' | 'none'>('none');
  const [isLoading, setIsLoading] = useState(false);

  // 根据文件扩展名确定预览类型
  useEffect(() => {
    if (!filePath || !fileType) {
      setPreviewType('none');
      setPreviewContent(null);
      return;
    }

    setIsLoading(true);

    const extension = fileType.toLowerCase();

    // 图片类型
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'];
    // 文本类型
    const textExtensions = ['.txt', '.md', '.json', '.js', '.ts', '.html', '.css', '.csv', '.xml', '.log'];

    if (imageExtensions.some(ext => extension.endsWith(ext))) {
      setPreviewType('image');
      setPreviewContent(filePath);
      setIsLoading(false);
    } else if (textExtensions.some(ext => extension.endsWith(ext))) {
      setPreviewType('text');
      // 使用fileManagerClient读取文件内容
      import('../services/api/file-manager').then(({ fileManagerClient }) => {
        fileManagerClient.readFilePreview(filePath)
          .then((content: string) => {
            setPreviewContent(content || '无法预览文件内容');
            setIsLoading(false);
          })
          .catch(err => {
            console.error('读取文件预览出错:', err);
            setPreviewContent('读取文件内容出错');
            setIsLoading(false);
          });
      });
    } else {
      setPreviewType('none');
      setPreviewContent(null);
      setIsLoading(false);
    }
  }, [filePath, fileType]);

  const renderFileIcon = () => {
    if (!fileType) return <File size={64} className="text-gray-400" />;

    const extension = fileType.toLowerCase();
    
    if (extension.endsWith('.jpg') || extension.endsWith('.jpeg') || 
        extension.endsWith('.png') || extension.endsWith('.gif') || 
        extension.endsWith('.bmp') || extension.endsWith('.webp') || 
        extension.endsWith('.svg')) {
      return <Image size={64} className="text-blue-500" />;
    }
    
    if (extension.endsWith('.js') || extension.endsWith('.ts') || 
        extension.endsWith('.html') || extension.endsWith('.css') || 
        extension.endsWith('.json') || extension.endsWith('.xml')) {
      return <FileCode size={64} className="text-green-500" />;
    }
    
    if (extension.endsWith('.mp3') || extension.endsWith('.wav') || 
        extension.endsWith('.aac') || extension.endsWith('.ogg')) {
      return <Music size={64} className="text-yellow-500" />;
    }
    
    if (extension.endsWith('.mp4') || extension.endsWith('.mov') || 
        extension.endsWith('.avi') || extension.endsWith('.mkv')) {
      return <Video size={64} className="text-red-500" />;
    }
    
    return <FileText size={64} className="text-gray-500" />;
  };

  return (
    <div className="h-full flex flex-col">
      {!filePath ? (
        <div className="flex-1 flex flex-col items-center justify-center text-muted-foreground">
          <File size={64} className="mb-4 text-gray-300" />
          <p>{t('filePreview.noFileSelected')}</p>
        </div>
      ) : isLoading ? (
        <div className="flex-1 flex items-center justify-center text-muted-foreground">
          <p>{t('filePreview.loading')}</p>
        </div>
      ) : (
        <>
          <div className="flex-1 overflow-auto">
            {previewType === 'image' ? (
              <div className="flex items-center justify-center h-full p-4">
                <img 
                  src={previewContent || ''} 
                  alt={fileName || '图片预览'} 
                  className="max-w-full max-h-full object-contain"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none';
                    setPreviewType('none');
                  }}
                />
              </div>
            ) : previewType === 'text' ? (
              <pre className="p-4 text-sm whitespace-pre-wrap overflow-auto h-full">
                {previewContent}
              </pre>
            ) : (
              <div className="flex flex-col items-center justify-center h-full">
                {renderFileIcon()}
                <p className="mt-4 text-lg font-medium">{fileName}</p>
                {fileSize !== null && (
                  <p className="text-sm text-muted-foreground mt-2">
                    {formatFileSize(fileSize)}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  {fileType}
                </p>
              </div>
            )}
          </div>

          <div className="p-4 border-t flex justify-between">
            <Button variant="secondary" size="sm" onClick={onOpen}>
              {t('filePreview.open')}
            </Button>
            <Button variant="outline" size="sm" onClick={onOpenLocation}>
              {t('filePreview.openLocation')}
            </Button>
          </div>
        </>
      )}
    </div>
  );
};

export default FilePreview; 