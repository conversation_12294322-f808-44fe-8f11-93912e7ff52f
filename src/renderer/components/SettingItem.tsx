import React from 'react';

interface SettingItemProps {
  label: string; 
  children: React.ReactNode;
  noBorder?: boolean;
  className?: string;
}
const SettingItem: React.FC<SettingItemProps> = ({
  label, children, noBorder = false, className = ""
}) => {
  // 完整版本显示更多信息
  return (
    <div className={`flex items-center justify-between py-4 ${!noBorder ? 'border-b border-gray-100 dark:border-gray-800' : ''} ${className}`}>
      <div className="text-sm font-medium text-gray-900 dark:text-gray-100 min-w-[100px] flex-shrink-0">{label}</div>
      <div className="flex-1 flex justify-end">{children}</div>
    </div>
  );
};

export default SettingItem; 