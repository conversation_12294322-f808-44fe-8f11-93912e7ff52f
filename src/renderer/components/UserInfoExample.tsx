import React from 'react';
import { Button } from './ui/button';
import UserProfile from './UserProfile';
import { useAuth } from '../hooks/useAuth';
import { Shield, ShieldCheck, AlertTriangle } from 'lucide-react';

/**
 * 用户信息使用示例组件
 * 展示如何在页面中使用用户认证状态和信息
 */
const UserInfoExample: React.FC = () => {
  const { 
    isLoggedIn, 
    user, 
    username, 
    nickname, 
    email,
    requireAuth,
    getAuthHeaders,
    openLoginDialog 
  } = useAuth();

  // 模拟需要登录才能执行的操作
  const handleProtectedAction = () => {
    if (requireAuth()) {
      alert(`欢迎 ${nickname || username}! 执行受保护的操作成功！`);
    }
  };

  // 模拟API调用
  const handleApiCall = () => {
    if (!requireAuth()) return;

    const headers = getAuthHeaders();
    console.log('API调用头部:', headers);
    alert('检查控制台，查看API调用头部信息');
  };

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      <h1 className="text-2xl font-bold text-gray-900">用户认证示例</h1>
      
      {/* 认证状态显示 */}
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <h2 className="text-lg font-semibold mb-3 flex items-center gap-2">
          {isLoggedIn ? (
            <>
              <ShieldCheck className="h-5 w-5 text-green-500" />
              已登录状态
            </>
          ) : (
            <>
              <Shield className="h-5 w-5 text-gray-500" />
              未登录状态
            </>
          )}
        </h2>
        
        {isLoggedIn ? (
          <div className="space-y-2 text-sm">
            <p><span className="font-medium">用户ID:</span> {user?.id}</p>
            <p><span className="font-medium">用户名:</span> {username}</p>
            <p><span className="font-medium">昵称:</span> {nickname}</p>
            <p><span className="font-medium">邮箱:</span> {email || '未设置'}</p>
            <p><span className="font-medium">注册时间:</span> {user?.createTime ? new Date(user.createTime).toLocaleString() : '未知'}</p>
          </div>
        ) : (
          <p className="text-gray-600">请先登录以查看用户信息</p>
        )}
      </div>

      {/* 用户资料组件展示 */}
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <h2 className="text-lg font-semibold mb-3">用户资料组件</h2>
        <div className="space-y-4">
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-2">紧凑模式:</h3>
            <UserProfile variant="compact" className="inline-flex" />
          </div>
          
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-2">完整模式:</h3>
            <UserProfile variant="full" className="max-w-sm" />
          </div>
        </div>
      </div>

      {/* 认证保护的操作 */}
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <h2 className="text-lg font-semibold mb-3 flex items-center gap-2">
          <AlertTriangle className="h-5 w-5 text-yellow-500" />
          受保护的操作
        </h2>
        <p className="text-gray-600 mb-4">
          以下操作需要登录后才能执行，如果未登录会自动弹出登录弹窗：
        </p>
        
        <div className="space-y-3">
          <Button
            onClick={handleProtectedAction}
            className="w-full sm:w-auto"
          >
            执行受保护操作
          </Button>
          
          <Button
            onClick={handleApiCall}
            variant="outline"
            className="w-full sm:w-auto"
          >
            模拟API调用
          </Button>
          
          {!isLoggedIn && (
            <Button
              onClick={openLoginDialog}
              variant="secondary"
              className="w-full sm:w-auto"
            >
              手动打开登录弹窗
            </Button>
          )}
        </div>
      </div>

      {/* 使用说明 */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h2 className="text-lg font-semibold mb-3 text-blue-900">使用说明</h2>
        <div className="text-sm text-blue-800 space-y-2">
          <p><strong>1. 用户认证状态管理:</strong> 使用 useAuth hook 获取用户状态和操作方法</p>
          <p><strong>2. 自动持久化:</strong> 用户登录状态会自动保存到本地存储</p>
          <p><strong>3. 认证保护:</strong> 使用 requireAuth() 方法保护需要登录的操作</p>
          <p><strong>4. API调用:</strong> 使用 getAuthHeaders() 获取认证头部信息</p>
          <p><strong>5. 组件复用:</strong> UserProfile 组件支持不同显示模式</p>
        </div>
      </div>
    </div>
  );
};

export default UserInfoExample; 