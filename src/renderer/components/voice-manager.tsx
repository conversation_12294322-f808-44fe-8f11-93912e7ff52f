import React, { useRef, useCallback } from 'react';
import { OpusDecoder } from 'opus-decoder';

// 生成 WAV 头部的工具函数
function createWavHeader(sampleCount: number, sampleRate: number, numChannels = 1, bitsPerSample = 16) {
  const blockAlign = numChannels * bitsPerSample / 8;
  const byteRate = sampleRate * blockAlign;
  const dataSize = sampleCount * blockAlign;
  const buffer = new ArrayBuffer(44);
  const view = new DataView(buffer);

  // "RIFF"
  view.setUint32(0, 0x52494646, false);
  view.setUint32(4, 36 + dataSize, true);
  view.setUint32(8, 0x57415645, false);
  // "fmt "
  view.setUint32(12, 0x666d7420, false);
  view.setUint32(16, 16, true); // PCM
  view.setUint16(20, 1, true); // PCM
  view.setUint16(22, numChannels, true);
  view.setUint32(24, sampleRate, true);
  view.setUint32(28, byteRate, true);
  view.setUint16(32, blockAlign, true);
  view.setUint16(34, bitsPerSample, true);
  // "data"
  view.setUint32(36, 0x64617461, false);
  view.setUint32(40, dataSize, true);

  return buffer;
}

// 音频队列管理器类
export class AudioQueueManager {
  private decoder: any;
  private decoderReady: Promise<void>;
  private sampleRate: number = 24000;
  private minOpusSize: number = 32; // 最小Opus数据包大小（字节）

  // Web Audio API流式播放相关
  private audioContext: AudioContext | null = null;
  private nextPlayTime: number = 0;
  private isAudioContextInitialized: boolean = false;

  // 优化：PCM合并缓冲区
  private pcmMergeBuffer: Int16Array[] = [];
  private pcmMergeSamples: number = 0;
  private readonly PCM_MERGE_MS = 100; // 合并100ms的PCM再播放
  private lastSampleRate: number = 36000;
  private isFirstChunk: boolean = true;
  private isStreamEnded: boolean = false;

  // 新增：Opus数据包缓存（用于生成完整WAV）
  private opusDataList: ArrayBuffer[] = [];
  private completeWavData: ArrayBuffer | null = null;

  constructor() {
    this.decoder = new OpusDecoder();
    this.decoderReady = this.decoder.ready;
    this.sampleRate = 24000;
    this.audioContext = null;
    this.nextPlayTime = 0;
    this.isAudioContextInitialized = false;
    this.pcmMergeBuffer = [];
    this.pcmMergeSamples = 0;
    this.lastSampleRate = 36000;
    this.isFirstChunk = true;
    this.isStreamEnded = false;
    this.opusDataList = [];
    this.completeWavData = null;
  }

  private ensureAudioContext(sampleRate: number) {
    if (!this.audioContext) {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({ sampleRate });
      this.nextPlayTime = this.audioContext.currentTime;
      this.isAudioContextInitialized = true;
    }
  }

  public async addAudio(opusData: ArrayBuffer): Promise<void> {
    if (opusData.byteLength < this.minOpusSize) {
      console.log(`🎵 跳过过小的Opus数据包: ${opusData.byteLength} bytes`);
      return;
    }
    // 缓存Opus数据包（用于生成完整WAV）
    this.opusDataList.push(opusData);
    await this.decodeAndBufferPcm(opusData);
  }

  private async decodeAndBufferPcm(opusData: ArrayBuffer): Promise<void> {
    await this.decoderReady;
    try {
      const result = this.decoder.decodeFrame(new Uint8Array(opusData));
      this.sampleRate = result.sampleRate;
      this.lastSampleRate = result.sampleRate;
      let pcm = new Int16Array(result.channelData[0].length);
      for (let i = 0; i < result.channelData[0].length; i++) {
        const sample = result.channelData[0][i];
        pcm[i] = Math.max(-32768, Math.min(32767, Math.round(sample * 32767)));
      }
      // PCM合并（用于流式播放）
      this.pcmMergeBuffer.push(pcm);
      this.pcmMergeSamples += pcm.length;
      
      // 计算100ms对应的采样点数
      const mergeSamplesThreshold = Math.floor(this.sampleRate * this.PCM_MERGE_MS / 1000);
      while (this.pcmMergeSamples >= mergeSamplesThreshold) {
        const segment = this.extractPcmSegment(mergeSamplesThreshold);
        // 只在第一个包做fade in
        let processed = segment;
        if (this.isFirstChunk) {
          processed = this.applyFadeInOut(segment, true, false);
          this.isFirstChunk = false;
        }
        this.playPcmChunk(processed, this.sampleRate);
      }
    } catch (error) {
      console.error('🎵 单个Opus数据包解码失败:', error);
    }
  }

  // 提取指定采样点数的PCM分段
  private extractPcmSegment(samples: number): Int16Array {
    let result = new Int16Array(samples);
    let offset = 0;
    while (samples > 0 && this.pcmMergeBuffer.length > 0) {
      const chunk = this.pcmMergeBuffer[0];
      if (chunk.length <= samples) {
        result.set(chunk, offset);
        offset += chunk.length;
        samples -= chunk.length;
        this.pcmMergeBuffer.shift();
      } else {
        result.set(chunk.subarray(0, samples), offset);
        this.pcmMergeBuffer[0] = chunk.subarray(samples);
        offset += samples;
        samples = 0;
      }
    }
    this.pcmMergeSamples -= result.length;
    return result;
  }

  // PCM流式播放
  private playPcmChunk(pcm: Int16Array, sampleRate: number) {
    this.ensureAudioContext(sampleRate);
    if (!this.audioContext) return;
    // 转为Float32
    const float32 = new Float32Array(pcm.length);
    for (let i = 0; i < pcm.length; i++) {
      float32[i] = pcm[i] / 32768;
    }
    // 创建AudioBuffer
    const buffer = this.audioContext.createBuffer(1, float32.length, sampleRate);
    buffer.getChannelData(0).set(float32);
    // 创建Source
    const source = this.audioContext.createBufferSource();
    source.buffer = buffer;
    source.connect(this.audioContext.destination);
    // 计算播放时间
    const now = this.audioContext.currentTime;
    if (this.nextPlayTime < now) this.nextPlayTime = now;
    source.start(this.nextPlayTime);
    this.nextPlayTime += buffer.duration;
  }

  public async endStream(): Promise<void> {
    this.isStreamEnded = true;
    
    // 播放剩余PCM（流式播放）
    if (this.pcmMergeSamples > 0) {
      const segment = this.extractPcmSegment(this.pcmMergeSamples);
      const processed = this.applyFadeInOut(segment, false, true);
      this.playPcmChunk(processed, this.lastSampleRate);
    }
    
    // 统一解码所有Opus为PCM并生成完整WAV
    if (this.opusDataList.length > 0) {
      this.completeWavData = await this.generateCompleteWavFromOpus();
    }
  }

  // 从Opus数据包生成完整WAV文件
  private async generateCompleteWavFromOpus(): Promise<ArrayBuffer> {
    await this.decoderReady;
    
    let totalPcmSamples = 0;
    let pcmChunks: Int16Array[] = [];
    
    // 统一解码所有Opus数据包
    for (const opusData of this.opusDataList) {
      try {
        const result = this.decoder.decodeFrame(new Uint8Array(opusData));
        const pcm = new Int16Array(result.channelData[0].length);
        for (let i = 0; i < result.channelData[0].length; i++) {
          const sample = result.channelData[0][i];
          pcm[i] = Math.max(-32768, Math.min(32767, Math.round(sample * 32767)));
        }
        pcmChunks.push(pcm);
        totalPcmSamples += pcm.length;
      } catch (error) {
        console.error('🎵 生成完整WAV时Opus解码失败:', error);
      }
    }
    
    if (totalPcmSamples === 0) {
      return new ArrayBuffer(0);
    }
    
    // 拼接所有PCM数据
    const wavHeader = createWavHeader(totalPcmSamples, this.lastSampleRate);
    const pcmData = new Int16Array(totalPcmSamples);
    let offset = 0;
    
    for (const chunk of pcmChunks) {
      pcmData.set(chunk, offset);
      offset += chunk.length;
    }
    
    // 对整个完整音频做淡入淡出
    const processedPcmData = this.applyFadeInOut(pcmData, true, true);
    
    const wavBuffer = new Uint8Array(wavHeader.byteLength + processedPcmData.byteLength);
    wavBuffer.set(new Uint8Array(wavHeader), 0);
    wavBuffer.set(new Uint8Array(processedPcmData.buffer), wavHeader.byteLength);
    return wavBuffer.buffer;
  }

  public stop(): void {
    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
      this.isAudioContextInitialized = false;
    }
    this.nextPlayTime = 0;
    this.pcmMergeBuffer = [];
    this.pcmMergeSamples = 0;
    this.isFirstChunk = true;
    this.isStreamEnded = false;
    this.opusDataList = [];
    this.completeWavData = null;
  }

  public getAllAudioData(): ArrayBuffer[] {
    return this.completeWavData ? [this.completeWavData] : [];
  }

  // 只在头/尾做淡入淡出
  private applyFadeInOut(pcmData: Int16Array, fadeIn: boolean, fadeOut: boolean): Int16Array {
    const fadeLength = Math.min(200, Math.floor(pcmData.length * 0.15));
    const processedData = new Int16Array(pcmData.length);
    for (let i = 0; i < pcmData.length; i++) {
      let sample = pcmData[i];
      if (fadeIn && i < fadeLength) {
        const fadeInFactor = 0.5 * (1 - Math.cos((i / fadeLength) * Math.PI));
        sample = Math.round(sample * fadeInFactor);
      }
      if (fadeOut && i >= pcmData.length - fadeLength) {
        const fadeOutFactor = 0.5 * (1 - Math.cos(((pcmData.length - i) / fadeLength) * Math.PI));
        sample = Math.round(sample * fadeOutFactor);
      }
      processedData[i] = sample;
    }
    return processedData;
  }
}

// 合并多个WAV音频ArrayBuffer为一个
export const mergeAudioBuffers = (audioBuffers: ArrayBuffer[]): ArrayBuffer => {
  if (audioBuffers.length === 0) {
    return new ArrayBuffer(0);
  }
  
  if (audioBuffers.length === 1) {
    return audioBuffers[0];
  }
  
  console.log('🔄 开始合并', audioBuffers.length, '个音频片段');
  
  // 调试第一个音频片段的头部信息
  const firstBuffer = audioBuffers[0];
  if (firstBuffer.byteLength >= 12) {
    const firstView = new DataView(firstBuffer);
    const riffMarker = String.fromCharCode(...new Uint8Array(firstBuffer.slice(0, 4)));
    const waveMarker = String.fromCharCode(...new Uint8Array(firstBuffer.slice(8, 12)));
    console.log('🔍 第一个音频片段头部检查:', {
      size: firstBuffer.byteLength,
      riff: riffMarker,
      wave: waveMarker,
      isValid: riffMarker === 'RIFF' && waveMarker === 'WAVE'
    });
  } else {
    console.log('🔍 第一个音频片段太小，无法检查头部:', firstBuffer.byteLength, 'bytes');
  }
  
  try {
    // 解析第一个WAV文件的头部信息
    const firstView = new DataView(firstBuffer);
    
    // 检查是否为有效的WAV文件
    const riffMarker = String.fromCharCode(...new Uint8Array(firstBuffer.slice(0, 4)));
    const waveMarker = String.fromCharCode(...new Uint8Array(firstBuffer.slice(8, 12)));
    
    if (riffMarker !== 'RIFF' || waveMarker !== 'WAVE') {
      console.warn('⚠️ 不是有效的WAV格式，使用简单合并');
      return simpleAudioMerge(audioBuffers);
    }
    
    // 读取音频格式信息
    const fmtChunkOffset = findChunk(firstView, 'fmt ');
    if (fmtChunkOffset === -1) {
      console.warn('⚠️ 找不到fmt chunk，使用简单合并');
      return simpleAudioMerge(audioBuffers);
    }
    
    const audioFormat = firstView.getUint16(fmtChunkOffset + 8, true);
    const numChannels = firstView.getUint16(fmtChunkOffset + 10, true);
    const sampleRate = firstView.getUint32(fmtChunkOffset + 12, true);
    const byteRate = firstView.getUint32(fmtChunkOffset + 16, true);
    const blockAlign = firstView.getUint16(fmtChunkOffset + 20, true);
    const bitsPerSample = firstView.getUint16(fmtChunkOffset + 22, true);
    
    console.log('📊 音频格式信息:', {
      audioFormat,
      numChannels,
      sampleRate,
      byteRate,
      blockAlign,
      bitsPerSample
    });
    
    // 提取所有音频数据
    const audioDataChunks: Uint8Array[] = [];
    let totalAudioDataSize = 0;
    
    for (let i = 0; i < audioBuffers.length; i++) {
      const buffer = audioBuffers[i];
      const view = new DataView(buffer);
      const dataChunkOffset = findChunk(view, 'data');
      
      if (dataChunkOffset === -1) {
        console.warn(`⚠️ 音频片段 ${i + 1} 找不到data chunk，跳过`);
        continue;
      }
      
      const dataSize = view.getUint32(dataChunkOffset + 4, true);
      const audioData = new Uint8Array(buffer.slice(dataChunkOffset + 8, dataChunkOffset + 8 + dataSize));
      
      audioDataChunks.push(audioData);
      totalAudioDataSize += audioData.length;
      
      console.log(`🎵 音频片段 ${i + 1}: ${audioData.length} bytes`);
    }
    
    if (audioDataChunks.length === 0) {
      console.warn('⚠️ 没有有效的音频数据，使用简单合并');
      return simpleAudioMerge(audioBuffers);
    }
    
    // 创建新的WAV文件
    const headerSize = 44; // 标准WAV头部大小
    const totalFileSize = headerSize + totalAudioDataSize - 8;
    const mergedBuffer = new ArrayBuffer(headerSize + totalAudioDataSize);
    const mergedView = new DataView(mergedBuffer);
    const mergedArray = new Uint8Array(mergedBuffer);
    
    // 写入WAV头部
    // RIFF chunk
    mergedArray.set([0x52, 0x49, 0x46, 0x46], 0); // 'RIFF'
    mergedView.setUint32(4, totalFileSize, true); // file size - 8
    mergedArray.set([0x57, 0x41, 0x56, 0x45], 8); // 'WAVE'
    
    // fmt chunk
    mergedArray.set([0x66, 0x6D, 0x74, 0x20], 12); // 'fmt '
    mergedView.setUint32(16, 16, true); // fmt chunk size
    mergedView.setUint16(20, audioFormat, true); // audio format
    mergedView.setUint16(22, numChannels, true); // num channels
    mergedView.setUint32(24, sampleRate, true); // sample rate
    mergedView.setUint32(28, byteRate, true); // byte rate
    mergedView.setUint16(32, blockAlign, true); // block align
    mergedView.setUint16(34, bitsPerSample, true); // bits per sample
    
    // data chunk
    mergedArray.set([0x64, 0x61, 0x74, 0x61], 36); // 'data'
    mergedView.setUint32(40, totalAudioDataSize, true); // data size
    
    // 合并音频数据
    let offset = headerSize;
    for (const audioData of audioDataChunks) {
      mergedArray.set(audioData, offset);
      offset += audioData.length;
    }
    
    console.log('✅ WAV合并完成:', {
      原始片段数: audioBuffers.length,
      合并后大小: mergedBuffer.byteLength,
      音频数据大小: totalAudioDataSize,
      估计时长: (totalAudioDataSize / byteRate).toFixed(2) + '秒'
    });
    
    return mergedBuffer;
    
  } catch (error) {
    console.error('❌ WAV合并失败，使用简单合并:', error);
    return simpleAudioMerge(audioBuffers);
  }
  
  // 辅助函数：查找WAV chunk
  function findChunk(view: DataView, chunkId: string): number {
    const chunkIdBytes = new TextEncoder().encode(chunkId);
    for (let i = 12; i < view.byteLength - 4; i++) {
      let match = true;
      for (let j = 0; j < 4; j++) {
        if (view.getUint8(i + j) !== chunkIdBytes[j]) {
          match = false;
          break;
        }
      }
      if (match) {
        return i;
      }
    }
    return -1;
  }
  
  // 简单合并函数（后备方案）
  function simpleAudioMerge(buffers: ArrayBuffer[]): ArrayBuffer {
    const totalLength = buffers.reduce((sum, buffer) => sum + buffer.byteLength, 0);
    const mergedBuffer = new ArrayBuffer(totalLength);
    const mergedView = new Uint8Array(mergedBuffer);
    
    let offset = 0;
    for (const buffer of buffers) {
      const bufferView = new Uint8Array(buffer);
      mergedView.set(bufferView, offset);
      offset += buffer.byteLength;
    }
    
    return mergedBuffer;
  }
};

// 语音管理器Hook
export const useVoiceManager = () => {
  const audioQueueManager = useRef<AudioQueueManager>(new AudioQueueManager());

  const addAudio = useCallback(async (opusData: ArrayBuffer) => {
    await audioQueueManager.current.addAudio(opusData);
  }, []);

  const endStream = useCallback(async () => {
    await audioQueueManager.current.endStream();
  }, []);

  const stop = useCallback(() => {
    audioQueueManager.current.stop();
  }, []);

  const getAllAudioData = useCallback(() => {
    return audioQueueManager.current.getAllAudioData();
  }, []);

  return {
    addAudio,
    endStream,
    stop,
    getAllAudioData,
    audioQueueManager: audioQueueManager.current
  };
}; 