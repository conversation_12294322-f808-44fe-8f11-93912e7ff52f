import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Play, Pause } from 'lucide-react';

interface AudioPlayerProps {
  audioData: ArrayBuffer;
  className?: string;
}

const AudioPlayer: React.FC<AudioPlayerProps> = ({ audioData, className = '' }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [audioUrl, setAudioUrl] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [duration, setDuration] = useState<number>(0); // 添加时长状态
  const audioRef = useRef<HTMLAudioElement>(null);
  const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 格式化时长显示
  const formatDuration = useCallback((seconds: number): string => {
    // 如果正在加载，显示计算中
    if (isLoading) {
      return '计算中...';
    }
    
    // 如果时长有效且大于0，显示具体时长
    if (seconds && isFinite(seconds) && seconds > 0) {
      const totalSeconds = Math.round(seconds);
      
      if (totalSeconds < 60) {
        return `${totalSeconds}秒`;
      } else {
        const minutes = Math.floor(totalSeconds / 60);
        const remainingSeconds = totalSeconds % 60;
        if (remainingSeconds === 0) {
          return `${minutes}分钟`;
        } else {
          return `${minutes}分${remainingSeconds}秒`;
        }
      }
    }
    
    // 如果不在加载状态但没有有效时长，显示语音
    return '语音';
  }, [isLoading]);

  useEffect(() => {
    setIsLoading(true);
    setError(null);
    setIsPlaying(false); // 重置播放状态
    setDuration(0); // 重置时长状态
    
    // 检查音频数据是否为空
    if (!audioData || audioData.byteLength === 0) {
      console.warn('AudioPlayer: 音频数据为空或无效');
      setError('音频数据为空');
      setIsLoading(false);
      return;
    }
    
    // 将ArrayBuffer转换为Blob URL
    const blob = new Blob([audioData], { type: 'audio/wav' });
    const url = URL.createObjectURL(blob);
    setAudioUrl(url);

    // 设置加载超时，3秒后强制完成加载
    loadingTimeoutRef.current = setTimeout(() => {
      console.warn('AudioPlayer: 加载超时，强制完成加载状态');
      setIsLoading(false);
    }, 3000);

    return () => {
      // 清理URL对象和超时器
      URL.revokeObjectURL(url);
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
    };
  }, [audioData]);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio || !audioUrl) return;


    const handleCanPlay = () => {
      setIsLoading(false);
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
      // 在canplay事件中也尝试获取时长
      if (audio.duration && isFinite(audio.duration) && audio.duration > 0) {
        setDuration(audio.duration);
        console.log('AudioPlayer: 在canplay中获取到音频时长:', audio.duration, '秒');
      } else {
        console.log('AudioPlayer: canplay时未获取到有效时长，duration:', audio.duration);
      }
    };

    const handleLoadedData = () => {
      setIsLoading(false);
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
      // 在loadeddata事件中也尝试获取时长
      if (audio.duration && isFinite(audio.duration) && audio.duration > 0) {
        setDuration(audio.duration);
        console.log('AudioPlayer: 在loadeddata中获取到音频时长:', audio.duration, '秒');
             } else {
         console.log('AudioPlayer: loadeddata时未获取到有效时长，duration:', audio.duration);
         // 如果仍然无法获取时长，尝试估算
         tryEstimateDuration();
         // 确保加载状态结束
         setIsLoading(false);
       }
    };

    const handleLoadStart = () => {
      console.log('AudioPlayer: 开始加载音频');
    };

    const handleLoadedMetadata = () => {
      console.log('AudioPlayer: 音频元数据加载完成');
      // 获取音频时长
      if (audio.duration && isFinite(audio.duration) && audio.duration > 0) {
        setDuration(audio.duration);
        console.log('AudioPlayer: 获取到音频时长:', audio.duration, '秒');
      } else {
        console.log('AudioPlayer: 元数据加载完成但无法获取时长，duration:', audio.duration);
        // 延迟再次尝试获取时长
        setTimeout(() => {
          if (audio.duration && isFinite(audio.duration) && audio.duration > 0) {
            setDuration(audio.duration);
            console.log('AudioPlayer: 延迟获取到音频时长:', audio.duration, '秒');
                     } else {
             console.log('AudioPlayer: 延迟后仍无法获取时长，尝试估算');
             tryEstimateDuration();
             // 确保加载状态结束
             setIsLoading(false);
           }
        }, 500);
      }
    };

    const handlePlay = () => {
      console.log('AudioPlayer: 音频开始播放');
      setIsPlaying(true);
      // 播放开始时再次尝试获取时长
      if (audio.duration && isFinite(audio.duration) && audio.duration > 0) {
        setDuration(audio.duration);
        console.log('AudioPlayer: 播放时获取到音频时长:', audio.duration, '秒');
      }
    };

    const handlePause = () => {
      console.log('AudioPlayer: 音频暂停');
      setIsPlaying(false);
    };

    const handleEnded = () => {
      console.log('AudioPlayer: 音频播放结束');
      setIsPlaying(false);
      // 强制确保音频处于暂停状态
      if (audio.currentTime >= audio.duration && audio.duration > 0) {
        console.log('AudioPlayer: 检测到音频播放完毕，强制重置到开头');
        audio.currentTime = 0;
      }
    };

    // 添加时间更新监听器，监控播放进度
    const handleTimeUpdate = () => {
      const currentTime = audio.currentTime;
      const duration = audio.duration;
      
      // 只在明确播放到结尾时才处理，避免误触发
      if (duration > 0 && currentTime >= duration - 0.05) {
        console.log('AudioPlayer: 通过timeupdate检测到播放完毕，准备重置状态');
        // 延迟一点再重置，让ended事件有机会先触发
        setTimeout(() => {
          if (audio.currentTime >= duration - 0.05) {
            console.log('AudioPlayer: 确认播放完毕，重置状态');
            setIsPlaying(false);
            audio.currentTime = 0;
          }
        }, 50);
      }
    };

    const handleError = (e: Event) => {
      console.error('AudioPlayer: 音频加载失败:', e, audio.error);
      setError('音频格式不支持或文件损坏');
      setIsLoading(false);
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
    };

    // 添加估算时长的函数
    const tryEstimateDuration = () => {
      try {
        // 基于文件大小估算时长（这是一个粗略的估算）
        // 检查是否为WAV文件头
        const dataView = new DataView(audioData);
        const isWav = audioData.byteLength >= 44 && 
                     String.fromCharCode(dataView.getUint8(0), dataView.getUint8(1), dataView.getUint8(2), dataView.getUint8(3)) === 'RIFF' &&
                     String.fromCharCode(dataView.getUint8(8), dataView.getUint8(9), dataView.getUint8(10), dataView.getUint8(11)) === 'WAVE';
        
        if (isWav) {
          // 对于WAV文件，尝试从头部读取采样率和数据大小
          try {
            const sampleRate = dataView.getUint32(24, true); // 采样率位置
            const bitsPerSample = dataView.getUint16(34, true); // 位深度
            const channels = dataView.getUint16(22, true); // 声道数
            const dataSize = audioData.byteLength - 44; // 减去头部大小
            
            const bytesPerSecond = sampleRate * channels * (bitsPerSample / 8);
            const estimatedDuration = dataSize / bytesPerSecond;
            
            console.log('AudioPlayer: WAV文件信息:', { sampleRate, bitsPerSample, channels, dataSize, bytesPerSecond });
            
            if (estimatedDuration > 0 && estimatedDuration < 3600) {
              setDuration(estimatedDuration);
              console.log('AudioPlayer: 从WAV头部估算音频时长:', estimatedDuration, '秒');
              return;
            }
          } catch (headerError) {
            console.warn('AudioPlayer: 无法从WAV头部获取信息:', headerError);
          }
        }
        
        // 如果不是WAV或无法从头部获取，使用通用估算
        // 假设为常见的44.1kHz 16位立体声格式
        const estimatedDuration = audioData.byteLength / (44100 * 2 * 2);
        if (estimatedDuration > 0 && estimatedDuration < 3600) {
          setDuration(estimatedDuration);
          console.log('AudioPlayer: 通用估算音频时长:', estimatedDuration, '秒 (基于文件大小', audioData.byteLength, 'bytes)');
        } else {
          console.log('AudioPlayer: 估算时长不合理:', estimatedDuration, '秒');
        }
      } catch (error) {
        console.error('AudioPlayer: 估算时长失败:', error);
      }
      
      // 无论估算是否成功，都确保加载状态结束
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
      setIsLoading(false);
    };

    // 添加所有相关的事件监听器
    audio.addEventListener('loadstart', handleLoadStart);
    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('canplay', handleCanPlay);
    audio.addEventListener('loadeddata', handleLoadedData);
    audio.addEventListener('play', handlePlay);
    audio.addEventListener('pause', handlePause);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('error', handleError);
    audio.addEventListener('timeupdate', handleTimeUpdate); // 添加时间更新监听器

    // 手动触发加载
    try {
      audio.load();
      console.log('AudioPlayer: 手动触发音频加载');
    } catch (err) {
      console.error('AudioPlayer: 手动加载失败:', err);
    }

    return () => {
      audio.removeEventListener('loadstart', handleLoadStart);
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('canplay', handleCanPlay);
      audio.removeEventListener('loadeddata', handleLoadedData);
      audio.removeEventListener('play', handlePlay);
      audio.removeEventListener('pause', handlePause);
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('error', handleError);
      audio.removeEventListener('timeupdate', handleTimeUpdate); // 移除时间更新监听器
    };
  }, [audioUrl, audioData]); // 移除 isPlaying 依赖，避免频繁重新绑定

  // 添加定期状态同步的 useEffect
  useEffect(() => {
    if (!audioRef.current) return;
    
    const syncInterval = setInterval(() => {
      const audio = audioRef.current;
      if (!audio) return;
      
      // 检查音频的实际播放状态
      // 更宽松的播放状态检查，避免误判
      const actuallyPlaying = !audio.paused && !audio.ended;
      
      // 只在明确检测到播放结束时才同步状态
      if (audio.ended && isPlaying) {
        console.log('AudioPlayer: 通过定期检查检测到音频播放结束');
        setIsPlaying(false);
        audio.currentTime = 0;
      }
      // 如果音频暂停且UI显示播放中，但不是因为加载中导致的，才同步
      else if (audio.paused && isPlaying && audio.readyState >= 2 && !isLoading) {
        // 额外检查：只有在音频时长大于0且当前时间接近结尾时才认为是播放完毕
        if (audio.duration > 0 && audio.currentTime >= audio.duration - 0.1) {
          console.log('AudioPlayer: 通过定期检查检测到播放到达结尾');
          setIsPlaying(false);
          audio.currentTime = 0;
        }
      }
    }, 500); // 降低检查频率到500ms，减少误触发
    
    return () => {
      clearInterval(syncInterval);
    };
  }, [isPlaying, isLoading]);

  const togglePlayPause = useCallback(async () => {
    const audio = audioRef.current;
    if (!audio) {
      console.log('AudioPlayer: 音频元素不存在');
      return;
    }

    console.log('AudioPlayer: 切换播放状态，当前状态:', isPlaying ? '播放中' : '已暂停', '加载状态:', isLoading);
    console.log('AudioPlayer: 音频实际状态 - paused:', audio.paused, 'ended:', audio.ended, 'currentTime:', audio.currentTime, 'duration:', audio.duration);

    try {
      if (isPlaying) {
        console.log('AudioPlayer: 执行暂停操作');
        audio.pause();
        // 手动设置状态，确保UI立即更新
        setIsPlaying(false);
      } else {
        console.log('AudioPlayer: 执行播放操作');
        
        // 如果音频已结束，重置到开头
        if (audio.ended || (audio.duration > 0 && audio.currentTime >= audio.duration)) {
          console.log('AudioPlayer: 音频已结束，重置到开头再播放');
          audio.currentTime = 0;
        }
        
        // 即使在加载状态也尝试播放
        await audio.play();
        // 手动设置状态，确保UI立即更新
        setIsPlaying(true);
      }
    } catch (error) {
      console.error('AudioPlayer: 播放控制失败:', error);
      setError('播放失败');
      setIsPlaying(false);
    }
  }, [isPlaying, isLoading]);

  // 声波动画组件
  const SoundWaveAnimation = () => (
    <div className="flex items-center gap-1 h-3">
      {[...Array(5)].map((_, i) => (
        <div
          key={i}
          className={`w-0.5 bg-blue-500 rounded-full transition-all duration-200 ${
            isPlaying 
              ? (i % 2 === 0 ? 'animate-soundwave' : 'animate-soundwave-alt')
              : ''
          }`}
          style={{
            height: isPlaying ? 'auto' : '5px',
            animationDelay: `${i * 150}ms`,
            minHeight: '4px'
          }}
        />
      ))}
    </div>
  );

  if (error) {
    return (<></>);
  }

  if (isLoading) {
    return (
      <div className={`absolute bottom-1 right-1 z-10 inline-flex items-center gap-3 px-1 py-1  bg-blue-50 dark:bg-blue-900/20 rounded-full border border-blue-200 dark:border-blue-800 ${className}`}>
        <div className="animate-spin rounded-full h-4 w-4 border-2 border-gray-300 border-t-blue-500"></div>
      </div>
    );
  }

  return (
    <div 
      className={`absolute bottom-1 right-1 z-10 inline-flex items-center gap-3 px-1 py-1 bg-blue-50 dark:bg-blue-900/20 rounded-full border border-blue-200 dark:border-blue-800 ${className} cursor-pointer`}
      onClick={togglePlayPause}>
      <audio ref={audioRef} src={audioUrl} preload="metadata" />
      
      

      {/* 声波动画替换文字 */}
      {
        isPlaying? 
        <div className="flex items-center gap-2">
          <SoundWaveAnimation />
          {/* <span className="text-xs text-blue-600 dark:text-blue-400">
            {formatDuration(duration)}
          </span> */}
        </div>
        :
        <button
          className="flex items-center justify-center w-5 h-5 rounded-full bg-blue-500 hover:bg-blue-600 text-white transition-all duration-200 hover:scale-105"
          type="button"
        >
          {isPlaying ? (
            <Pause className="h-3 w-3" />
          ) : (
            <Play className="h-3 w-3 ml-0.5" />
          )}
        </button>
      }
    </div>
  );
};

export default AudioPlayer; 