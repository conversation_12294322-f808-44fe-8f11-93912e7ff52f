import React, { useState, useEffect } from 'react';
import { 
  File, 
  FileText, 
  Image as ImageIcon, 
  Files, 
  FileCode,
  FileArchive,
  FileSpreadsheet,
  Music,
  Video,
  Box,
  Folder
} from 'lucide-react';
import type {
  ClipboardItem,
  ClipboardTextItem,
  ClipboardImageItem,
  ClipboardFileItem,
  ClipboardDirectoryItem
} from '../../shared/types/clipboard';

// 文件类型映射到图标
const fileTypeToIcon: Record<string, React.ReactNode> = {
  default: <File className="h-8 w-8" />,
  directory: <Folder className="h-8 w-8" />,
  text: <FileText className="h-8 w-8" />,
  image: <ImageIcon className="h-8 w-8" />,
  code: <FileCode className="h-8 w-8" />,
  zip: <FileArchive className="h-8 w-8" />,
  rar: <FileArchive className="h-8 w-8" />,
  '7z': <FileArchive className="h-8 w-8" />,
  xls: <FileSpreadsheet className="h-8 w-8" />,
  xlsx: <FileSpreadsheet className="h-8 w-8" />,
  doc: <FileText className="h-8 w-8" />,
  docx: <FileText className="h-8 w-8" />,
  pdf: <FileText className="h-8 w-8" />,
  mp3: <Music className="h-8 w-8" />,
  wav: <Music className="h-8 w-8" />,
  mp4: <Video className="h-8 w-8" />,
  avi: <Video className="h-8 w-8" />,
  mov: <Video className="h-8 w-8" />,
  obj: <Box className="h-8 w-8" />,
  stl: <Box className="h-8 w-8" />,
};

// 获取文件图标
const getFileIcon = (fileType: string): React.ReactNode => {
  const type = fileType.toLowerCase();
  
  // 编程语言文件
  if (['js', 'ts', 'jsx', 'tsx', 'py', 'java', 'c', 'cpp', 'cs', 'go', 'rb', 'php', 'html', 'css'].includes(type)) {
    return fileTypeToIcon.code;
  }
  
  // 图片文件
  if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(type)) {
    return fileTypeToIcon.image;
  }

  return fileTypeToIcon[type] || fileTypeToIcon.default;
};

/**
 * 剪贴板预览组件
 * 只显示当前剪贴板内容，不再显示历史记录
 */
const ClipboardPreview: React.FC = () => {
  const [currentItem, setCurrentItem] = useState<ClipboardItem | null>(null);

  // 获取当前剪贴板内容
  useEffect(() => {
    // 确保应用就绪后再访问剪贴板
    const initClipboard = async () => {
      try {
        // 获取当前剪贴板项目
        const current = await window.electron.clipboard.getCurrent();
        if (current) {
          setCurrentItem(current);
        }
      } catch (error) {
        console.error('获取剪贴板内容失败:', error);
      }
    };

    initClipboard();

    // 监听剪贴板变化
    const unsubscribe = window.electron.clipboard.onChange((item: ClipboardItem[]) => {
      setCurrentItem(item[0]);
    });

    return () => {
      unsubscribe();
    };
  }, []);

  // 渲染剪贴板项的内容
  const renderClipboardItem = (item: ClipboardItem) => {
    if (item.type === 'text' && item.items.length === 1) {
      const textItem = item.items[0] as ClipboardTextItem;
      return (
        <div className="p-2 text-sm bg-muted/30 rounded max-h-20 overflow-auto">
          <span className="line-clamp-3">
            {textItem.content}
          </span>
        </div>
      );
    } 
    
    if (item.type === 'image' && item.items.length === 1) {
      const imageItem = item.items[0] as ClipboardImageItem;
      return (
        <div className="flex items-center gap-2 p-2 bg-muted/30 rounded">
          {imageItem.dataURL ? (
            <img 
              src={imageItem.dataURL} 
              alt="剪贴板图片" 
              className="max-w-24 max-h-16 object-contain" 
            />
          ) : (
            <ImageIcon className="h-8 w-8" />
          )}
          <span className="text-sm">
            {imageItem.name || '图片'}
          </span>
        </div>
      );
    } 
    
    if (item.type === 'directory' && item.items.length === 1) {
      const dirItem = item.items[0] as ClipboardDirectoryItem;
      return (
        <div className="flex items-center gap-2 p-2 bg-muted/30 rounded">
          {fileTypeToIcon.directory}
          <span className="text-sm truncate">
            {dirItem.name}
          </span>
        </div>
      );
    }
    
    if (item.type === 'file' && item.items.length === 1) {
      const fileItem = item.items[0] as ClipboardFileItem;
      return (
        <div className="flex items-center gap-2 p-2 bg-muted/30 rounded">
          {getFileIcon(fileItem.type)}
          <span className="text-sm truncate">
            {fileItem.name}
          </span>
        </div>
      );
    }
    
    if (item.type === 'multiple') {
      const icon = <Files className="h-8 w-8" />;
      const label = `${item.count} 个文件`;
      
      return (
        <div className="flex items-center gap-2 p-2 bg-muted/30 rounded">
          {icon}
          <span className="text-sm">
            {label}
          </span>
        </div>
      );
    }
    
    return null;
  };

  // 如果没有剪贴板内容，不显示任何内容
  if (!currentItem) {
    return null;
  }

  return (
    <div className="w-full py-2 px-4">
      {renderClipboardItem(currentItem)}
    </div>
  );
};

ClipboardPreview.displayName = 'ClipboardPreview';

export default ClipboardPreview; 