import React, { useState, useEffect } from 'react';
import { File, Folder } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { FileInfo } from '../../shared/types/file';
import { fileManagerClient } from '../services/api/file-manager';

interface FileSearchResultsProps {
  query: string;
  onSelectFile: (file: FileInfo) => void;
  selectedFilePath: string | null;
}

const FileSearchResults: React.FC<FileSearchResultsProps> = ({ 
  query, 
  onSelectFile,
  selectedFilePath
}) => {
  const { t } = useTranslation();
  const [files, setFiles] = useState<FileInfo[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const searchFiles = async () => {
      setLoading(true);
      try {
        // 调用文件管理器客户端搜索文件
        const results = await fileManagerClient.searchFiles(query);
        // Removed type assertion as types should match now
        setFiles(results || []); 
      } catch (error) {
        console.error('搜索文件出错:', error);
        setFiles([]);
      } finally {
        setLoading(false);
      }
    };

    searchFiles();
  }, [query]);

  // 获取文件图标
  const getFileIcon = (file: FileInfo) => {
    // Updated logic to use isDirectory and extension
    if (file.isDirectory) {
      return <Folder className="h-5 w-5 mr-2 text-blue-500" />;
    }
    if (file.extension) {
      const extension = file.extension.toLowerCase();
      // 图片
      if (['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'].includes(extension)) {
        return <img 
          src={`file://${file.path}`} 
          className="h-5 w-5 mr-2 object-cover rounded"
          alt=""
          onError={(e) => {
            (e.currentTarget.nextSibling as HTMLElement).style.display = 'block';
            e.currentTarget.style.display = 'none';
          }}
        />;
      }
    }
    // Default icon for other files or files without extension handled by isDirectory
    return <File className="h-5 w-5 mr-2 text-gray-500" />;
  };

  // 格式化文件时间
  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className="h-full overflow-hidden flex flex-col">
      <div className="text-xs px-4 py-2 text-muted-foreground border-b">
        {loading ? (
          t('filePreview.searching')
        ) : files.length === 0 ? (
          t('filePreview.noResults')
        ) : (
          t('filePreview.resultsCount', { count: files.length })
        )}
      </div>
      
      <div className="flex-1 overflow-auto">
        {files.map((file) => (
          <div 
            key={file.id}
            className={`px-4 py-2 hover:bg-secondary cursor-pointer border-b flex items-center justify-between ${
              selectedFilePath === file.path ? 'bg-secondary' : ''
            }`}
            onClick={() => onSelectFile(file)}
          >
            <div className="flex items-center overflow-hidden">
              {getFileIcon(file)}
              <File className="h-5 w-5 mr-2 text-gray-500" style={{display: 'none'}} />
              <div className="overflow-hidden">
                <div className="truncate">{file.name}</div>
                <div className="text-xs text-muted-foreground truncate">
                  {file.path}
                </div>
              </div>
            </div>
            <div className="text-xs text-muted-foreground whitespace-nowrap ml-2">
              {formatDate(file.mtime)}
            </div>
          </div>
        ))}
        
        {loading && (
          <div className="flex justify-center items-center p-4">
            <div className="animate-spin h-5 w-5 border-2 border-gray-300 rounded-full border-t-gray-600"></div>
          </div>
        )}
      </div>
    </div>
  );
};

export default FileSearchResults; 