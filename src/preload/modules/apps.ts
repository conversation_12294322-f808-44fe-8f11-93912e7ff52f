import { ipc<PERSON><PERSON><PERSON> } from 'electron';
import { APP, AppsAPI } from '../../shared/ipc';

export const appsAPI: AppsAPI = {
  // 启动应用
  launch: async (appPath: string): Promise<boolean> => {
    return await ipcRenderer.invoke(APP.LAUNCH, appPath);
  },

  // 获取应用列表
  getAppList: async () => {
    return await ipcRenderer.invoke(APP.GET_APP_LIST);
  },
  
  // 强制刷新应用列表
  refreshAppList: async () => {
    return await ipcRenderer.invoke(APP.REFRESH_APP_LIST);
  },
  
  // 监听应用列表更新事件
  onListUpdated: (callback: (data: { apps: any[]; count: number; timestamp: number }) => void) => {
    const handler = (_: any, data: any) => callback(data);
    ipcRenderer.on(APP.LIST_UPDATED, handler);
    return () => ipcRenderer.removeListener(APP.LIST_UPDATED, handler);
  }
};