import { ipc<PERSON><PERSON><PERSON> } from 'electron';
import { AUTH } from '../../shared/ipc';
import type { AuthAPI } from '../../shared/ipc';

/**
 * 认证相关API
 */
export const authAPI: AuthAPI = {
  getToken: async (): Promise<string | null> => {
    return await ipc<PERSON>enderer.invoke(AUTH.GET_TOKEN);
  },

  setToken: async (token: string): Promise<void> => {
    return await ipcRenderer.invoke(AUTH.SET_TOKEN, token);
  },

  clearToken: async (): Promise<void> => {
    return await ipcRenderer.invoke(AUTH.CLEAR_TOKEN);
  },

  onOpenLoginDialog: (callback: () => void) => {
    const listener = () => callback();
    ipcRenderer.on(AUTH.OPEN_LOGIN_DIALOG, listener);
    
    return () => {
      ipcRenderer.removeListener(AUTH.OPEN_LOGIN_DIALOG, listener);
    };
  },
  
  notifyLoginSuccess: () => {
    ipcRenderer.send(AUTH.LOGIN_SUCCESS);
  },

  notifyLogout: () => {
    ipcRenderer.send(AUTH.LOGOUT);
  },

  broadcastUserStatusChange: async (user: any): Promise<boolean> => {
    return await ipcRenderer.invoke(AUTH.BROADCAST_USER_STATUS_CHANGE, user);
  },

  onClearAllTokens: (callback: () => void) => {
    const listener = () => callback();
    ipcRenderer.on(AUTH.CLEAR_ALL_TOKENS, listener);
    
    return () => {
      ipcRenderer.removeListener(AUTH.CLEAR_ALL_TOKENS, listener);
    };
  },
};