import { ipc<PERSON><PERSON><PERSON> } from 'electron';
// Import the specific API interface type
import { FILE,FilesAPI } from '../../shared/ipc';

/**
 * 文件操作相关API
 */
// Ensure the exported object conforms to the FilesAPI type
export const filesAPI: FilesAPI = {
  // 获取最近文件
  getRecent: async () => {
    // TODO: Define a specific File type in shared types and cast the result
    return await ipcRenderer.invoke(FILE.GET_RECENT);
  },
  
  // 搜索文件
  search: async (query: string) => {
    // TODO: Define a specific File type in shared types and cast the result
    return await ipcRenderer.invoke(FILE.SEARCH, query);
  },
  
  // 打开文件
  open: async (path: string): Promise<boolean> => {
    return await ipcRenderer.invoke(FILE.OPEN, path);
  },
  
  // 打开文件所在位置
  openLocation: async (path: string): Promise<boolean> => {
    return await ipcRenderer.invoke(FILE.OPEN_LOCATION, path);
  },

  readFilePreview: async (path: string): Promise<string> => {
    return await ipcRenderer.invoke(FILE.READ_PREVIEW, path);
  },

  checkExists: async (path: string): Promise<boolean> => {
    return await ipcRenderer.invoke(FILE.CHECK_EXISTS, path);
  },

  selectFolder: async (): Promise<string | null> => {
    return await ipcRenderer.invoke(FILE.SELECT_FOLDER);
  }
};
