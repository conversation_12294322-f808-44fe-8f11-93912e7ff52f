import { ipc<PERSON><PERSON><PERSON> } from 'electron';
import { SETTINGS } from '../../shared/ipc';
import type { SettingsAPI } from '../../shared/ipc';

/**
 * 设置相关的预加载API
 */
export const settingsAPI: SettingsAPI = {
  /**
   * 获取所有设置
   */
  get: async () => {
    return await ipcRenderer.invoke(SETTINGS.GET);
  },

  /**
   * 更新设置
   */
  update: async (newConfig: any) => {
    return await ipcRenderer.invoke(SETTINGS.UPDATE, newConfig);
  },
}; 