import { ipc<PERSON><PERSON><PERSON> } from 'electron';
import type { MCPAPI } from '../../shared/ipc';
import { MCP } from '../../shared/ipc';

/**
 * MCP (Multi-Cloud Platform) related APIs
 */
export const mcpAPI: MCPAPI = {
  listTools: () => ipcRenderer.invoke(MCP.LIST_TOOLS),
  getAllTools: () => ipcRenderer.invoke(MCP.GET_ALL_TOOLS),
  callTool: (tool) => ipcRenderer.invoke(MCP.CALL_TOOL, tool),
  getConfig: () => ipcRenderer.invoke(MCP.GET_CONFIG),
  putConfig: (config) => ipcRenderer.invoke(MCP.PUT_CONFIG, config),
  activate: (args) => ipcRenderer.invoke(MCP.ACTIVATE, args),
  deactivated: (key) => ipcRenderer.invoke(MCP.DEACTIVATED, key),
  addServer: (server) => ipcRenderer.invoke(MCP.ADD_SERVER, server),
  updateServer: (server) => ipcRenderer.invoke(MCP.UPDATE_SERVER, server),
  deleteServer: (key) => ipcRenderer.invoke(MCP.DELETE_SERVER, key),
  getMcpToolById: (id) => ipcRenderer.invoke(MCP.GET_MCP_TOOL_BY_ID, id), // 添加根据ID获取MCP工具的方法
  
  onToolsUpdated: (callback: () => void) => {
    const listener = () => callback();
    ipcRenderer.on(MCP.TOOLS_UPDATED, listener);

    return () => {
      ipcRenderer.removeListener(MCP.TOOLS_UPDATED, listener);
    };
  },

  onToolDownloadRequest: (callback: (data: any) => void) => {
    const listener = (_: any, data: any) => callback(data);
    ipcRenderer.on(MCP.TOOL_DOWNLOAD_REQUEST, listener);

    return () => {
      ipcRenderer.removeListener(MCP.TOOL_DOWNLOAD_REQUEST, listener);
    };
  },
};