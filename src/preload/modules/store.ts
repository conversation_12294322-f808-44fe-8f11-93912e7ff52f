import { ipc<PERSON><PERSON><PERSON> } from 'electron';
import type { StoreAPI } from '../../shared/ipc';
import { STORE } from '../../shared/ipc';

/**
 * Store related APIs
 * Note: Using synchronous IPC for get is generally discouraged.
 * Consider using electron-store in the main process and async methods if possible.
 */
export const storeAPI: StoreAPI = {
  get: (key, defaultValue) => {
    // 首先尝试从localStorage读取（用于调试）
    try {
      const localData = localStorage.getItem(`electron-store-${key}`);
      if (localData) {
        const parsedData = JSON.parse(localData);
        return parsedData;
      }
    } catch (error) {
      console.warn('读取localStorage失败:', error);
    }
    
    // 从electron-store读取
    const result = ipcRenderer.sendSync(STORE.GET, { key, defaultValue });
    
    // 同时保存到localStorage（用于调试）
    if (result !== undefined) {
      try {
        localStorage.setItem(`electron-store-${key}`, JSON.stringify(result));
      } catch (error) {
        console.warn('保存到localStorage失败:', error);
      }
    }
    
    return result;
  },
  
  set: (key, val) => {
    // 保存到electron-store
    ipcRenderer.send(STORE.SET, { key, val });
    
    // 同时保存到localStorage（用于调试）
    try {
      localStorage.setItem(`electron-store-${key}`, JSON.stringify(val));
    } catch (error) {
      console.warn('保存到localStorage失败:', error);
    }
  }
}; 