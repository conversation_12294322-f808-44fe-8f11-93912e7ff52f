import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';
// Import specific interfaces from the shared file
import type { WindowAPI, HotkeyInfo } from '../../shared/ipc'; 
// Import the specific API interface type
import { WINDOW } from '../../shared/ipc';

/**
 * 窗口管理相关API
 */
// Ensure the exported object conforms to the WindowAPI type
export const windowAPI: WindowAPI = {
  // 窗口控制方法
  hideWindow: () => {
    ipcRenderer.send(WINDOW.HIDE);
  },
  
  showWindow: () => {
    ipcRenderer.send(WINDOW.SHOW);
  },
  // 移动窗口
  moveWindow: (mouseData: { mouseX: number, mouseY: number, width: number, height: number }) => {
    ipcRenderer.send(WINDOW.MOVE_WINDOW, mouseData);
  },
  toggleWindow: () => {
    ipcRenderer.send(WINDOW.TOGGLE);
  },
  
  // 开发者工具
  openDevTools: () => {
    ipcRenderer.send(WINDOW.DEV_TOOLS);
  },
  
  // 窗口显示事件监听
  onWindowShow: (callback: () => void) => {
    const listener = () => callback();
    ipcRenderer.on(WINDOW.SHOW_EVENT, listener);
    
    return () => {
      ipcRenderer.removeListener(WINDOW.SHOW_EVENT, listener);
    };
  },
  onWindowHide: (callback: () => void) => {
    const listener = () => callback();
    ipcRenderer.on(WINDOW.HIDE_EVENT, listener);
    
    return () => {
      ipcRenderer.removeListener(WINDOW.HIDE_EVENT, listener);
    };
  },
  // 剪贴板历史显示事件监听
  onShowClipboardHistory: (callback: () => void) => {
    const listener = () => callback();
    ipcRenderer.on(WINDOW.SHOW_CLIPBOARD_HISTORY, listener);
    
    return () => {
      ipcRenderer.removeListener(WINDOW.SHOW_CLIPBOARD_HISTORY, listener);
    };
  },

  // 设置窗口是否可调整大小
  setResizable: async (resizable: boolean) => {
    await ipcRenderer.send(WINDOW.SET_RESIZABLE, resizable);
  },
  
  // 获取窗口是否可调整大小
  isResizable: async (): Promise<boolean> => {
    return await ipcRenderer.invoke(WINDOW.IS_RESIZABLE);
  },
  
  // 设置窗口是否忽略鼠标事件
  setIgnoreMouseEvents: async (ignore: boolean) => {
    await ipcRenderer.send(WINDOW.SET_IGNORE_MOUSE, ignore);
  },
  
  // 获取配置信息
  getConfig: async (): Promise<any> => {
    return await ipcRenderer.invoke(WINDOW.GET_CONFIG);
  },
  
  // 设置钉住状态
  setPinned: async (pinned: boolean) => {
    await ipcRenderer.send(WINDOW.SET_PIN, pinned);
  },
  
  // 获取钉住状态
  isPinned: async (): Promise<boolean> => {
    return await ipcRenderer.invoke(WINDOW.IS_PINNED);
  },

  // 设置窗口相关
  showSettingsWindow: () => {
    ipcRenderer.send(WINDOW.SHOW_SETTINGS);
  },

  hideSettingsWindow: () => {
    ipcRenderer.send(WINDOW.HIDE_SETTINGS);
  },

  toggleSettingsWindow: () => {
    ipcRenderer.send(WINDOW.TOGGLE_SETTINGS);
  },

  // 设置紧凑模式
  setCompactMode: async (compact: boolean) => {
    await ipcRenderer.send(WINDOW.SET_COMPACT_MODE, compact);
  },

  // 内置功能快捷键触发事件监听
  onSwitchToTranslator: (callback: () => void) => {
    const listener = () => callback();
    ipcRenderer.on(WINDOW.SWITCH_TO_TRANSLATOR, listener);

    return () => {
      ipcRenderer.removeListener(WINDOW.SWITCH_TO_TRANSLATOR, listener);
    };
  },

  onSwitchToAiChat: (callback: () => void) => {
    const listener = () => callback();
    ipcRenderer.on(WINDOW.SWITCH_TO_AI_CHAT, listener);

    return () => {
      ipcRenderer.removeListener(WINDOW.SWITCH_TO_AI_CHAT, listener);
    };
  },

  onSwitchToFileSearch: (callback: () => void) => {
    const listener = () => callback();
    ipcRenderer.on(WINDOW.SWITCH_TO_FILE_SEARCH, listener);

    return () => {
      ipcRenderer.removeListener(WINDOW.SWITCH_TO_FILE_SEARCH, listener);
    };
  },

  onTriggerScreenshot: (callback: () => void) => {
    const listener = () => callback();
    ipcRenderer.on(WINDOW.TRIGGER_SCREENSHOT, listener);

    return () => {
      ipcRenderer.removeListener(WINDOW.TRIGGER_SCREENSHOT, listener);
    };
  }

};