import { ipc<PERSON><PERSON><PERSON> } from 'electron';
import { DB,DBAPI } from '../../shared/ipc';

/**
 * 数据库操作相关API
 */
export const dbAPI: DBAPI = {
    all<T>(sql: string, params?: any): Promise<T[]> {
        return ipcRenderer.invoke(DB.ALL, { sql, params });
    },
    get<T>(sql: string, id: any): Promise<T> {
        return ipcRenderer.invoke(DB.GET, { sql, id });
    },
    run(sql: string, params: any): Promise<boolean> {
        return ipcRenderer.invoke(DB.RUN, { sql, params });
    },
    transaction(tasks: { sql: string; params: any[] }[]): Promise<boolean> {
        return ipcRenderer.invoke(DB.TRANSACTION, tasks);
    },
}