import { ipc<PERSON><PERSON><PERSON> } from 'electron';
import { SHORTCUT, ShortcutAPI } from '../../shared/ipc';

/**
 * 快捷键操作相关API
 */
export const shortcutAPI: ShortcutAPI = {
  /**
   * 检查快捷键是否可用
   */
  checkShortcutAvailable: (shortcut: string): Promise<boolean> => {
    return ipcRenderer.invoke(SHORTCUT.CHECK_AVAILABLE, shortcut);
  },

  /**
   * 注册应用快捷键
   */
  registerAppShortcut: (id: string, shortcut: string, path: string, name: string): Promise<boolean> => {
    return ipcRenderer.invoke(SHORTCUT.REGISTER_APP, { id, shortcut, path, name });
  },

  /**
   * 注销应用快捷键
   */
  unregisterAppShortcut: (id: string): Promise<boolean> => {
    return ipcRenderer.invoke(SHORTCUT.UNREGISTER_APP, id);
  },

  /**
   * 获取所有应用快捷键
   */
  getAppShortcuts: (): Promise<Array<{id: string, shortcut: string, path: string, name: string}>> => {
    return ipcRenderer.invoke(SHORTCUT.GET_APP_SHORTCUTS);
  },

  disableAllShortcuts: (): Promise<boolean> => {
    return ipcRenderer.invoke(SHORTCUT.DISABLE_ALL);
  },

  restoreAllShortcuts: (): Promise<boolean> => {
    return ipcRenderer.invoke(SHORTCUT.RESTORE_ALL);
  },

  /**
   * 注册内置功能快捷键
   */
  registerBuiltinShortcut: (id: string, shortcut: string, name: string): Promise<boolean> => {
    return ipcRenderer.invoke(SHORTCUT.REGISTER_BUILTIN, { id, shortcut, name });
  },

  /**
   * 注销内置功能快捷键
   */
  unregisterBuiltinShortcut: (id: string): Promise<boolean> => {
    return ipcRenderer.invoke(SHORTCUT.UNREGISTER_BUILTIN, id);
  },

  /**
   * 获取所有内置功能快捷键
   */
  getBuiltinShortcuts: (): Promise<Array<{id: string, shortcut: string, name: string}>> => {
    return ipcRenderer.invoke(SHORTCUT.GET_BUILTIN_SHORTCUTS);
  },
};