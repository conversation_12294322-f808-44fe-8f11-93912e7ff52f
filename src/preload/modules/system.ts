import { ipc<PERSON><PERSON><PERSON> } from 'electron';
import { SYSTEM, THEME, LANGUAGE, CLIPBOARD_SETTINGS, USER_STATUS } from '../../shared/ipc';
import type { SystemInfo, SystemAPI } from '../../shared/ipc';

/**
 * 系统信息相关的预加载API
 */
export const systemAPI: SystemAPI = {
  /**
   * 获取完整的系统信息
   */
  getInfo: (): Promise<SystemInfo> => {
    return ipcRenderer.invoke(SYSTEM.GET_INFO);
  },

  /**
   * 获取电脑唯一标识符
   */
  getComputerId: (): Promise<string> => {
    return ipcRenderer.invoke(SYSTEM.GET_COMPUTER_ID);
  },

  /**
   * 获取用户主目录路径
   */
  getHomePath: (): Promise<string> => {
    return ipcRenderer.invoke(SYSTEM.GET_HOME_PATH);
  },

  /**
   * 监听主题变化事件
   */
  onThemeChanged: (callback: (theme: string) => void) => {
    const listener = (_event: any, theme: string) => callback(theme);
    ipcRenderer.on(THEME.CHANGED, listener);
    
    return () => {
      ipcRenderer.removeListener(THEME.CHANGED, listener);
    };
  },

  /**
   * 监听语言变化事件
   */
  onLanguageChanged: (callback: (language: string) => void) => {
    const listener = (_event: any, language: string) => callback(language);
    ipcRenderer.on(LANGUAGE.CHANGED, listener);

    return () => {
      ipcRenderer.removeListener(LANGUAGE.CHANGED, listener);
    };
  },

  /**
   * 监听剪贴板设置变化事件
   */
  onClipboardSettingsChanged: (callback: (settings: any) => void) => {
    const listener = (_event: any, settings: any) => callback(settings);
    ipcRenderer.on(CLIPBOARD_SETTINGS.CHANGED, listener);

    return () => {
      ipcRenderer.removeListener(CLIPBOARD_SETTINGS.CHANGED, listener);
    };
  },

  /**
   * 监听用户状态变化事件
   */
  onUserStatusChanged: (callback: (user: any) => void) => {
    const listener = (_event: any, user: any) => callback(user);
    ipcRenderer.on(USER_STATUS.CHANGED, listener);

    return () => {
      ipcRenderer.removeListener(USER_STATUS.CHANGED, listener);
    };
  },
}; 