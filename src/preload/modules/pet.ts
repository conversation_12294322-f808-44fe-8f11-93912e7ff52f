import { ipc<PERSON><PERSON><PERSON> } from 'electron';
import { PET, PetAPI } from '../../shared/ipc';

/**
 * 模型管理API
 */
export const petAPI: PetAPI = {
  // 获取模型文件
  getModels: async (fileOrPath: string | File): Promise<string[]> => {
    // 将File对象转换为具有path属性的普通对象
    const param = fileOrPath instanceof File 
      ? { path: (fileOrPath as any).path } 
      : fileOrPath;
      
    return await ipcRenderer.invoke(PET.GET_MODELS, param);
  },

   // 监听Tips显示事件
   onShowTip: (callback: (message: string, timeout?: number, priority?: number) => void) => {
    const handler = (_event: any, data: { message: string; timeout?: number; priority?: number }) => {
      callback(data.message, data.timeout, data.priority);
    };
    
    ipcRenderer.on(PET.SHOW_TIP, handler);
    // 返回取消监听的函数
    return () => {
      ipcRenderer.removeListener(PET.SHOW_TIP, handler);
    };
  }
}; 