import { ipc<PERSON>enderer } from 'electron';
import { 
  CROSS_WINDOW, 
  WindowType, 
  CrossWindowAPI, 
  CrossWindowEventData, 
  CrossWindowEventListener 
} from '../../shared/ipc';

/**
 * 跨窗口通信 API 实现
 * 为渲染进程提供简洁的跨窗口通信接口
 */
class CrossWindowEventBus implements CrossWindowAPI {
  private eventListeners: Map<string, Set<CrossWindowEventListener>> = new Map();
  private currentWindowType: WindowType | null = null;

  constructor() {
    this.setupEventForwarding();
    this.initCurrentWindowType();
  }

  /**
   * 设置事件转发
   */
  private setupEventForwarding(): void {
    // 监听来自主进程转发的跨窗口事件
    ipcRenderer.on(CROSS_WINDOW.FORWARD, (_, eventData: CrossWindowEventData) => {
      this.handleForwardedEvent(eventData);
    });
  }

  /**
   * 初始化当前窗口类型
   */
  private async initCurrentWindowType(): Promise<void> {
    try {
      this.currentWindowType = await ipcRenderer.invoke('cross-window-get-current-type');
    } catch (error) {
      console.error('获取当前窗口类型失败:', error);
      this.currentWindowType = WindowType.MAIN; // 默认值
    }
  }

  /**
   * 处理转发的事件
   */
  private handleForwardedEvent(eventData: CrossWindowEventData): void {
    const { eventName, data, sourceWindow, id: eventId } = eventData;
    
    // 获取该事件的所有监听器
    const listeners = this.eventListeners.get(eventName);
    if (!listeners || listeners.size === 0) {
      return;
    }

    // 调用所有监听器
    listeners.forEach(listener => {
      try {
        listener(data, sourceWindow, eventId);
      } catch (error) {
        console.error(`跨窗口事件监听器执行失败 [${eventName}]:`, error);
      }
    });
  }

  /**
   * 发送事件到指定窗口或广播到所有窗口
   */
  public async emit<T = any>(
    eventName: string, 
    data: T, 
    targetWindow?: WindowType | WindowType[]
  ): Promise<boolean> {
    try {
      return await ipcRenderer.invoke(CROSS_WINDOW.EMIT, eventName, data, targetWindow);
    } catch (error) {
      console.error('发送跨窗口事件失败:', error);
      return false;
    }
  }

  /**
   * 监听跨窗口事件
   */
  public on<T = any>(eventName: string, listener: CrossWindowEventListener<T>): () => void {
    // 添加监听器到本地集合
    if (!this.eventListeners.has(eventName)) {
      this.eventListeners.set(eventName, new Set());
    }
    
    const listeners = this.eventListeners.get(eventName)!;
    listeners.add(listener as CrossWindowEventListener);

    // 通知主进程注册监听器
    ipcRenderer.send(CROSS_WINDOW.ON, eventName);

    // 返回取消监听的函数
    return () => {
      this.off(eventName, listener);
    };
  }

  /**
   * 移除事件监听器
   */
  public off(eventName: string, listener?: CrossWindowEventListener): void {
    const listeners = this.eventListeners.get(eventName);
    if (!listeners) {
      return;
    }

    if (listener) {
      // 移除特定监听器
      listeners.delete(listener);
      
      // 如果没有监听器了，清理事件
      if (listeners.size === 0) {
        this.eventListeners.delete(eventName);
        ipcRenderer.send(CROSS_WINDOW.OFF, eventName);
      }
    } else {
      // 移除所有监听器
      this.eventListeners.delete(eventName);
      ipcRenderer.send(CROSS_WINDOW.OFF, eventName);
    }
  }

  /**
   * 广播事件到所有窗口（除了当前窗口）
   */
  public async broadcast<T = any>(eventName: string, data: T): Promise<boolean> {
    try {
      return await ipcRenderer.invoke(CROSS_WINDOW.BROADCAST, eventName, data);
    } catch (error) {
      console.error('广播跨窗口事件失败:', error);
      return false;
    }
  }

  /**
   * 获取当前窗口类型
   */
  public getCurrentWindowType(): WindowType {
    return this.currentWindowType || WindowType.MAIN;
  }

  /**
   * 获取所有活跃窗口类型
   */
  public async getActiveWindows(): Promise<WindowType[]> {
    try {
      return await ipcRenderer.invoke('cross-window-get-active-windows');
    } catch (error) {
      console.error('获取活跃窗口列表失败:', error);
      return [];
    }
  }

  /**
   * 清理所有事件监听器
   */
  public cleanup(): void {
    // 移除所有本地监听器
    for (const eventName of this.eventListeners.keys()) {
      ipcRenderer.send(CROSS_WINDOW.OFF, eventName);
    }
    this.eventListeners.clear();

    // 移除 IPC 监听器
    ipcRenderer.removeAllListeners(CROSS_WINDOW.FORWARD);
  }
}

// 创建单例实例
const crossWindowEventBus = new CrossWindowEventBus();

// 导出 API
export const crossWindowAPI: CrossWindowAPI = {
  emit: crossWindowEventBus.emit.bind(crossWindowEventBus),
  on: crossWindowEventBus.on.bind(crossWindowEventBus),
  off: crossWindowEventBus.off.bind(crossWindowEventBus),
  broadcast: crossWindowEventBus.broadcast.bind(crossWindowEventBus),
  getCurrentWindowType: crossWindowEventBus.getCurrentWindowType.bind(crossWindowEventBus),
  getActiveWindows: crossWindowEventBus.getActiveWindows.bind(crossWindowEventBus),
};

// 在窗口卸载时清理资源
window.addEventListener('beforeunload', () => {
  crossWindowEventBus.cleanup();
});

// 导出事件总线实例（用于调试和高级用法）
export { crossWindowEventBus };
