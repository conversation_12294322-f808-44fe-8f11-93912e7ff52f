import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';

import type { ProxyConfig } from '../../shared/ipc';

/**
 * 代理相关的API
 */
export const proxyApi = {
  /**
   * 获取当前代理配置
   */
  getConfig: (): Promise<ProxyConfig | null> => {
    return ipcRenderer.invoke('proxy:get-config');
  },

  /**
   * 设置代理配置
   */
  setConfig: (config: ProxyConfig): Promise<{ success: boolean }> => {
    return ipcRenderer.invoke('proxy:set-config', config);
  },

  /**
   * 测试代理连接
   */
  testConnection: (testUrl?: string): Promise<boolean> => {
    return ipcRenderer.invoke('proxy:test-connection', testUrl);
  },

  /**
   * 重置为系统代理
   */
  resetToSystem: (): Promise<{ success: boolean }> => {
    return ipcRenderer.invoke('proxy:reset-to-system');
  },

  /**
   * 设置直连模式
   */
  setDirect: (): Promise<{ success: boolean }> => {
    return ipcRenderer.invoke('proxy:set-direct');
  },

  /**
   * 设置手动代理
   */
  setManual: (proxyRules: string, bypassRules?: string): Promise<{ success: boolean }> => {
    return ipcRenderer.invoke('proxy:set-manual', proxyRules, bypassRules);
  },

  /**
   * 设置PAC脚本代理
   */
  setPac: (pacScript: string): Promise<{ success: boolean }> => {
    return ipcRenderer.invoke('proxy:set-pac', pacScript);
  }
};