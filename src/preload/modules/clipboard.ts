import { ipc<PERSON><PERSON><PERSON> } from 'electron';
// Import the specific API interface type
import { CLIPBOARD, CLIPBOARD_HISTORY, ClipboardAPI, ClipboardHistoryAPI } from '../../shared/ipc';

/**
 * 剪贴板操作相关API
 */
// 基础剪贴板API
export const clipboardAPI: ClipboardAPI = {
    getHistory: async () => {
        return await ipcRenderer.invoke(CLIPBOARD.GET_HISTORY);
    },
    clearHistory: async () => {
        return await ipcRenderer.invoke(CLIPBOARD.CLEAR_HISTORY);
    },
    onChange: (callback: (history: any[]) => void) => {
        const wrappedCallback = (_event: any, history: any[]) => {
            callback(history);
        };
        ipcRenderer.on(CLIPBOARD.ON_CHANGE, wrappedCallback);
        return () => {
            ipcRenderer.removeListener(CLIPBOARD.ON_CHANGE, wrappedCallback);
        };
    },
    write: (clipboardItem: any) => {
        ipcRenderer.send(CLIPBOARD.WRITE, clipboardItem);
    },
    getCurrent: async () => {
        return await ipcRenderer.invoke(CLIPBOARD.GET_CURRENT);
    }
};

/**
 * 剪贴板历史窗口控制API
 */
export const clipboardHistoryAPI: ClipboardHistoryAPI = {
    show: async (): Promise<boolean> => {
        return await ipcRenderer.invoke(CLIPBOARD_HISTORY.SHOW);
    },

    hide: async (): Promise<boolean> => {
        return await ipcRenderer.invoke(CLIPBOARD_HISTORY.HIDE);
    },

    toggle: async (): Promise<boolean> => {
        return await ipcRenderer.invoke(CLIPBOARD_HISTORY.TOGGLE);
    },

    getHistory: async (): Promise<any[]> => {
        return await ipcRenderer.invoke(CLIPBOARD_HISTORY.GET_HISTORY);
    },

    clearHistory: async (): Promise<boolean> => {
        return await ipcRenderer.invoke(CLIPBOARD_HISTORY.CLEAR_HISTORY);
    },

    selectItem: async (item: any): Promise<boolean> => {
        return await ipcRenderer.invoke(CLIPBOARD_HISTORY.SELECT_ITEM, item);
    },

    updateStyle: async (style: string): Promise<boolean> => {
        return await ipcRenderer.invoke(CLIPBOARD_HISTORY.UPDATE_STYLE, style);
    },

    getCurrentStyle: async (): Promise<string> => {
        return await ipcRenderer.invoke(CLIPBOARD_HISTORY.GET_CURRENT_STYLE);
    },

    onReset: (callback: () => void) => {
        const wrappedCallback = () => {
            callback();
        };
        ipcRenderer.on(CLIPBOARD_HISTORY.RESET, wrappedCallback);
        return () => {
            ipcRenderer.removeListener(CLIPBOARD_HISTORY.RESET, wrappedCallback);
        };
    },

    // 新增的分页和数据库功能
    getHistoryPaginated: async (options?: {
        page?: number;
        limit?: number;
        type?: string;
        favorite?: boolean;
        search?: string;
    }): Promise<{
        items: any[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    }> => {
        return await ipcRenderer.invoke(CLIPBOARD_HISTORY.GET_HISTORY_PAGINATED, options);
    },

    deleteItem: async (id: string): Promise<boolean> => {
        return await ipcRenderer.invoke(CLIPBOARD_HISTORY.DELETE_ITEM, id);
    },

    deleteItems: async (ids: string[]): Promise<number> => {
        return await ipcRenderer.invoke(CLIPBOARD_HISTORY.DELETE_ITEMS, ids);
    },

    setFavorite: async (id: string, favorite: boolean): Promise<boolean> => {
        return await ipcRenderer.invoke(CLIPBOARD_HISTORY.SET_FAVORITE, id, favorite);
    },

    updateMemo: async (id: string, memo: string): Promise<boolean> => {
        return await ipcRenderer.invoke(CLIPBOARD_HISTORY.UPDATE_MEMO, id, memo);
    },

    updateTags: async (id: string, tags: string[]): Promise<boolean> => {
        return await ipcRenderer.invoke(CLIPBOARD_HISTORY.UPDATE_TAGS, id, tags);
    },

    purgeDeleted: async (olderThanDays?: number): Promise<number> => {
        return await ipcRenderer.invoke(CLIPBOARD_HISTORY.PURGE_DELETED, olderThanDays);
    }
};