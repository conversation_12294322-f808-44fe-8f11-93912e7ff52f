import { contextBridge } from 'electron';
import { windowAPI } from './modules/window';
import { floatingBallAPI } from './modules/floatingBall';
import { appsAPI } from './modules/apps';
import { filesAPI } from './modules/files';
import { dbAPI } from './modules/db';
import { mcpAPI } from './modules/mcp';
import { cryptoAPI } from './modules/crypto';
import { storeAPI } from './modules/store';
import { clipboardAPI, clipboardHistoryAPI } from './modules/clipboard';
import { petAPI } from './modules/pet';
import { proxyApi } from './modules/proxy';
import { shortcutAPI } from './modules/shortcut';
import { systemAPI } from './modules/system';
import { authAPI } from './modules/auth';
import { settingsAPI } from './modules/settings';
import { crossWindowAPI } from './modules/cross-window';
// Import the main API interface
import type { ElectronAPI } from '../shared/ipc';

/**
 * 将API暴露给渲染进程
 * 集中管理所有暴露的API
 */

// Construct the final API object conforming to ElectronAPI type
const electron: ElectronAPI = {
  // 应用信息
  appInfo: {
    name: 'Aido',
    version: '1.0.0' // TODO: Get version dynamically
  },

  // Import API modules
  window: windowAPI,
  floatingBall: floatingBallAPI,
  apps: appsAPI,
  files: filesAPI,
  db: dbAPI,
  mcp: mcpAPI,
  crypto: cryptoAPI,
  store: storeAPI,
  auth: authAPI,
  clipboard: clipboardAPI,
  clipboardHistory: clipboardHistoryAPI,
  pet: petAPI,
  proxy: proxyApi,
  shortcut: shortcutAPI,
  system: systemAPI,
  settings: settingsAPI,
  crossWindow: crossWindowAPI,
} as ElectronAPI;

contextBridge.exposeInMainWorld('electron', electron);