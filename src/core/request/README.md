# Request (Axios-based)

这是一个现代化、基于 [Axios](https://axios-http.com/) 构建的 HTTP 请求库，使用 TypeScript 编写。它在 Axios 的基础上添加了自动 token 管理、主线程/渲染线程兼容性等功能，专为 Electron 应用设计。

## 特性

- 🚀 基于成熟稳定的 Axios 库
- 🤝 Promise-based 异步 API
- ✋ 请求和响应拦截器
- 🔄 自动的 JSON 数据转换
- ⏰ 请求超时处理
- ✨ 创建实例以复用配置
- 🔒 使用 TypeScript 编写，提供强类型支持
- 🔐 自动 token 管理和刷新
- ⚡ 主线程和渲染线程兼容

## 文件结构

```text
request/
├── adapters.ts       # 环境适配器，处理主线程和渲染线程的不同逻辑
├── index.ts          # 统一入口文件，包含所有类型定义、token管理和axios实例
└── README.md         # (本文档)
```

## 架构设计

### 环境适配器模式

使用适配器模式来处理主线程和渲染线程的差异：

- **RendererAdapter**: 处理渲染线程的逻辑，从 localStorage 和 store 获取数据
- **MainAdapter**: 处理主线程的逻辑，通过 IPC 获取数据
- **EnvironmentAdapter**: 统一接口，自动选择合适的适配器

## 安装与引入

将 `request` 目录复制到您的项目中，然后可以按需引入。

```typescript
import request from './path/to/request';
```

## 快速上手

你可以像使用其他流行的 HTTP 库一样发起请求。

### GET 请求

```typescript
import request from './request';

request.get('https://api.example.com/data', {
  params: {
    id: 123
  }
})
.then(response => {
  console.log(response.data);
})
.catch(error => {
  console.error(error);
});
```

### POST 请求

`POST`、`PUT`、`PATCH` 方法的第二个参数是请求体 `data`。

```typescript
import request from './request';

request.post('https://api.example.com/users', {
  firstName: 'Gemini',
  lastName: 'Pro'
})
.then(response => {
  console.log(response.data);
});
```

## 核心功能

### 拦截器

拦截器是该库最强大的功能之一。您可以在请求被发送或响应被处理之前拦截它们。

```typescript
import request from './request';

// 添加请求拦截器
request.interceptors.request.use(config => {
  // 在发送请求之前做些什么
  // 例如，为每个请求添加认证头
  const token = localStorage.getItem('token');
  if (token) {
    config.headers['Authorization'] = `Bearer ${token}`;
  }
  return config;
}, error => {
  // 对请求错误做些什么
  return Promise.reject(error);
});

// 添加响应拦截器
request.interceptors.response.use(response => {
  // 对响应数据做点什么
  // 例如，只返回 response.data
  return response.data;
}, error => {
  // 对响应错误做点什么
  // 例如，统一处理 401 未授权错误
  if (error.response && error.response.status === 401) {
    // 跳转到登录页
    console.log('Unauthorized, redirecting to login.');
  }
  return Promise.reject(error);
});
```

### 创建实例

`request.create()` 方法允许您创建一个拥有独立配置、拦截器和默认值的请求实例。这在需要与多个不同配置的 API 交互时非常有用。

```typescript
import request from './request';

const service = request.create({
  baseURL: 'https://api.anotherservice.com/v1',
  timeout: 5000,
});

// 这个实例拥有独立的 baseURL 和拦截器
service.get('/todos/1').then(response => {
  console.log(response.data);
});

// 原来的 request 实例不受影响
```

## API

### `request(config)`
- **`request(url[, config])`**: 发起一个请求。

### 实例方法

- **`request.get(url[, config])`**
- **`request.delete(url[, config])`**
- **`request.head(url[, config])`**
- **`request.options(url[, config])`**
- **`request.post(url[, data[, config]])`**
- **`request.put(url[, data[, config]])`**
- **`request.patch(url[, data[, config]])`**

### 请求配置 (`RequestConfig`)

基于 Axios 的 `AxiosRequestConfig`，并添加了以下自定义属性：

| 属性 | 描述 | 类型 |
| --- | --- | --- |
| `getToken` | 获取 token 的函数 | `() => string \| null` |
| `_retry` | 内部重试标记 | `boolean` |

所有 Axios 的标准配置选项都可以使用，包括：

- `url`, `method`, `baseURL`, `headers`, `params`, `data`, `timeout` 等

### 响应结构 (`Response`)

直接使用 Axios 的 `AxiosResponse` 类型：

| 属性 | 描述 | 类型 |
| --- | --- | --- |
| `data` | 服务器返回的数据 | `any` |
| `status` | HTTP 状态码 | `number` |
| `statusText` | HTTP 状态信息 | `string` |
| `headers` | 响应头 | `AxiosHeaders` |
| `config` | 该响应对应的请求配置 | `RequestConfig` |

## 特殊功能

### 自动 Token 管理

- 自动从 localStorage 或主进程获取 token
- 自动在请求头中添加 `X-Token` 和 `Device-Id`
- 401 错误时自动刷新 token 并重试请求

### 主线程/渲染线程兼容

- 自动检测运行环境
- 在主线程中通过 IPC 获取 token 和 computerId
- 在渲染线程中从 localStorage 和 store 获取数据

### 导出的工具函数

```typescript
import { getToken, getTokenAsync, refreshTokenApi } from './path/to/request'

// 同步获取 token（仅渲染线程）
const token = getToken()

// 异步获取 token（主线程和渲染线程）
const token = await getTokenAsync()

// 手动刷新 token
await refreshTokenApi()
```