/**
 * 环境适配器 - 处理主线程和渲染线程的不同逻辑
 */

// ==================== 环境检测 ====================
export const isMainProcess = (): boolean => {
  return typeof window === 'undefined' && typeof process !== 'undefined' && process.type === 'browser';
};

export const isRendererProcess = (): boolean => {
  return typeof window !== 'undefined' && typeof localStorage !== 'undefined';
};

// ==================== 接口定义 ====================
export interface AuthData {
  token?: string;
  [key: string]: any;
}

export interface EnvironmentAdapter {
  getToken(): string | null;
  getTokenAsync(): Promise<string | null>;
  getComputerId(): Promise<string | null>;
  updateToken(token: string): void;
}

// ==================== 渲染线程适配器 ====================
export class RendererAdapter implements EnvironmentAdapter {
  private getAuthFromStorage(): AuthData | null {
    if (!isRendererProcess()) return null;
    
    try {
      const authData = localStorage.getItem('auth-storage');
      if (authData) {
        const parsed = JSON.parse(authData);
        return parsed.state || parsed;
      }
    } catch (error) {
      console.warn('读取localStorage认证数据失败:', error);
    }
    return null;
  }

  getToken(): string | null {
    const authData = this.getAuthFromStorage();
    const token = authData?.token || null;
    console.log('🔍 [渲染线程] 获取token:', {
      hasAuthData: !!authData,
      hasToken: !!token,
      tokenPreview: token ? `${token.substring(0, 20)}...` : 'null',
      isLoggedIn: authData?.isLoggedIn || false,
      time: new Date().toLocaleTimeString()
    });
    return token;
  }

  async getTokenAsync(): Promise<string | null> {
    return this.getToken();
  }

  async getComputerId(): Promise<string | null> {
    if (!isRendererProcess()) return null;
    try {
      console.log('🔍 [渲染线程] 开始从localStorage获取computerId...');
      // 从localStorage获取computerId
      const computerId = localStorage.getItem('deviceId');
      console.log('🔍 [渲染线程] 从localStorage获取computerId结果:', {
        hasComputerId: !!computerId,
        computerIdPreview: computerId ? `${computerId.substring(0, 8)}...` : 'null',
        time: new Date().toLocaleTimeString()
      });
      return computerId;
    } catch (e) {
      console.error('🔍 [渲染线程] 从localStorage获取computerId失败:', e);
      return null;
    }
  }

  updateToken(token: string): void {
    if (!isRendererProcess()) return;
    
    try {
      const authData = this.getAuthFromStorage();
      if (authData) {
        authData.token = token;
        localStorage.setItem('auth-storage', JSON.stringify({ state: authData }));
        console.log('🔄 Token更新成功');
      }
    } catch (error) {
      console.warn('更新localStorage失败:', error);
    }
  }
}

// ==================== 主线程适配器 ====================
export class MainAdapter implements EnvironmentAdapter {
  getToken(): string | null {
    console.log('主线程同步获取token，返回null（需要使用异步方法）');
    return null;
  }

  async getTokenAsync(): Promise<string | null> {
    try {
      console.log('🔍 [主线程] 开始获取token...');
      const { getMainProcessToken } = await import('../../main/services/rendererFieldService');
      const token = await getMainProcessToken();
      
      console.log('🔍 [主线程] 获取token结果:', {
        hasToken: !!token,
        tokenPreview: token ? `${token.substring(0, 20)}...` : 'null',
        time: new Date().toLocaleTimeString()
      });
      
      return token;
    } catch (error) {
      console.error('🔍 [主线程] 获取token失败:', error);
      return null;
    }
  } 

  async getComputerId(): Promise<string | null> {
    try {
      // 首先尝试从systemService直接获取
      try {
        const { systemService } = await import('../../main/services/systemService');
        const computerId = await systemService.getComputerId();
        console.log('🔍 主进程直接获取computerId:', computerId ? '✅ 成功' : '❌ 失败');
        if (computerId) {
          return computerId;
        }
      } catch (directError) {
        console.log('🔍 主进程直接获取computerId失败，尝试从渲染进程获取:', directError);
      }

      // 如果直接获取失败，再尝试从渲染进程获取
      const { getMainProcessComputerId } = await import('../../main/services/rendererFieldService');
      const rendererComputerId = await getMainProcessComputerId();
      console.log('🔍 从渲染进程获取computerId:', rendererComputerId ? '✅ 成功' : '❌ 失败');
      return rendererComputerId;
    } catch (error) {
      console.error('主进程获取computerId失败:', error);
      return null;
    }
  }

  updateToken(token: string): void {
    // 主线程需要通过IPC更新渲染进程的token
    try {
      // 动态导入避免循环依赖
      import('../../main/services/rendererFieldService').then(({ updateFieldCache }) => {
        updateFieldCache('token', token);
        console.log('🔄 主进程Token缓存更新成功');
      }).catch(error => {
        console.error('主进程更新token缓存失败:', error);
      });

      // 同时通知渲染进程更新localStorage
      import('electron').then(({ BrowserWindow }) => {
        const mainWindow = BrowserWindow.getAllWindows().find(win => !win.isDestroyed());
        if (mainWindow) {
          mainWindow.webContents.executeJavaScript(`
            (() => {
              try {
                const authData = localStorage.getItem('auth-storage');
                if (authData) {
                  const parsed = JSON.parse(authData);
                  const data = parsed.state || parsed;
                  if (data) {
                    data.token = '${token}';
                    localStorage.setItem('auth-storage', JSON.stringify({ state: data }));
                    console.log('🔄 渲染进程Token更新成功');
                  }
                }
              } catch (error) {
                console.error('渲染进程更新token失败:', error);
              }
            })()
          `).catch(error => {
            console.error('执行渲染进程token更新脚本失败:', error);
          });
        }
      }).catch(error => {
        console.error('获取BrowserWindow失败:', error);
      });
    } catch (error) {
      console.error('主进程updateToken操作失败:', error);
    }
  }
}

// ==================== 适配器工厂 ====================
export function createEnvironmentAdapter(): EnvironmentAdapter {
  if (isMainProcess()) {
    return new MainAdapter();
  } else {
    return new RendererAdapter();
  }
}
