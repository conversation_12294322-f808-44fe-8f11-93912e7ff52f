import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError, Method as AxiosMethod } from 'axios'
import { createEnvironmentAdapter, EnvironmentAdapter, isMainProcess } from './adapters'

// ==================== 类型定义 ====================
export type Method = AxiosMethod

// 扩展axios配置，保持向后兼容
export interface RequestConfig extends AxiosRequestConfig {
  getToken?: () => string | null
  _retry?: boolean
  [propName: string]: any
}

// 请求实例接口，直接使用axios实例类型
export interface RequestInstance extends AxiosInstance {
  create: (config: RequestConfig) => RequestInstance
}

// 直接使用axios的错误类型
export interface RequestError extends AxiosError {
  isFetchError?: boolean
}

// ==================== Token 管理 ====================
class TokenManager {
  private adapter: EnvironmentAdapter;

  constructor() {
    this.adapter = createEnvironmentAdapter();
  }

  getToken(): string | null {
    return this.adapter.getToken();
  }

  async getTokenAsync(): Promise<string | null> {
    return await this.adapter.getTokenAsync();
  }

  async getComputerId(): Promise<string | null> {
    return await this.adapter.getComputerId();
  }

  updateToken(token: string): void {
    this.adapter.updateToken(token);
  }
}

// ==================== Token 刷新管理 ====================
class TokenRefreshManager {
  private isRefreshing = false;
  private failedQueue: Array<{
    resolve: (token: string | null) => void;
    reject: (error: any) => void;
  }> = [];

  private processQueue(error: any, token: string | null = null) {
    this.failedQueue.forEach(prom => {
      if (error) {
        prom.reject(error);
      } else {
        prom.resolve(token);
      }
    });
    this.failedQueue = [];
  }

  async refreshToken(): Promise<string | null> {
    console.log('开始刷新Token...');
    if (this.isRefreshing) {
      return new Promise((resolve, reject) => {
        this.failedQueue.push({ resolve, reject });
      });
    }

    this.isRefreshing = true;

    try {
      // 使用当前token请求刷新接口
      const response = await request.post('/api/userSession/refreshToken');
      const { token: newToken } = response.data;

      // 更新token
      tokenManager.updateToken(newToken);

      this.processQueue(null, newToken);
      return newToken;
    } catch (error) {
      this.processQueue(error, null);
      console.error("Token刷新失败:", error);
      throw error;
    } finally {
      this.isRefreshing = false;
    }
  }
}

// ==================== 实例创建 ====================
function createInstance(config: RequestConfig): RequestInstance {
  const axiosInstance = axios.create(config)

  // 添加create方法
  const instance = axiosInstance as any
  instance.create = function (newConfig: RequestConfig): RequestInstance {
    return createInstance({ ...config, ...newConfig })
  }

  return instance as RequestInstance
}

// ==================== 全局实例 ====================
const tokenManager = new TokenManager();
const refreshManager = new TokenRefreshManager();
// const baseURL = "https://www.mcpcn.cc"
const baseURL = "https://www.rapido.chat"
// const baseURL = "http://localhost:3001"

const request = createInstance({
  baseURL: baseURL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  }
});

// ==================== 拦截器配置 ====================
// 请求拦截器
request.interceptors.request.use(
  async config => {
    const startTime = Date.now();
    const token = await tokenManager.getTokenAsync();
    const computerId = await tokenManager.getComputerId();
    const endTime = Date.now();

    // 详细的日志信息
    const processType = isMainProcess() ? '主线程' : '渲染线程';
    const tokenInfo = token ? `✅ 成功 (${token}...)` : '❌ 无token';
    const computerIdInfo = computerId ? `✅ 成功 (${computerId}...)` : '❌ 无computerId';
    
    console.log(`🌐 [${processType}] API请求准备:`, {
      url: config.url,
      method: config.method?.toUpperCase(),
      token: tokenInfo,
      computerId: computerIdInfo,
      耗时: `${endTime - startTime}ms`,
      时间戳: new Date().toLocaleTimeString()
    });

    // 设置请求头
    if (token && config.headers) {
      (config.headers as any)['X-Token'] = token;
      console.log(`🔑 已设置X-Token header: ${token.substring(0, 20)}...`);
    } else if (!token) {
      console.warn('⚠️ 警告: 未获取到token，可能影响需要认证的API调用');
    }
    
    if (computerId && config.headers) {
      (config.headers as any)['Device-Id'] = computerId;
      console.log(`🖥️ 已设置Device-Id header: ${computerId.substring(0, 8)}...`);
    } else if (!computerId) {
      console.warn('⚠️ 警告: 未获取到computerId，可能影响需要设备标识的API调用');
    }

    return config;
  },
  error => Promise.reject(error)
);

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    const data = response.data;
    const url = response.config.url || '';

    // xiaozhi接口特殊处理：如果是xiaozhi/ota/接口或者code === 0，直接返回数据
    if (url.endsWith('/xiaozhi/ota/') || data.code === 0) {
      return data;
    }
    // 其他接口：如果有错误码且不为0，抛出错误
    else if (data.code !== undefined && data.code !== 0) {
      throw new Error(data.msg || '请求失败');
    }

    // 默认返回数据
    return data;
  },
  async (error: RequestError) => {
    const originalRequest = error.config;

    // 401错误且未重试过
    if (error.response?.status === 401 && originalRequest && !(originalRequest as any)._retry) {
      (originalRequest as any)._retry = true;

      const currentToken = await tokenManager.getTokenAsync();
      if (!currentToken) {
        console.error("无token，需要重新登录");
        return Promise.reject(error);
      }

      try {
        const newToken = await refreshManager.refreshToken();
        if (newToken && originalRequest.headers) {
          (originalRequest.headers as any)['X-Token'] = newToken;
        }
        return request(originalRequest);
      } catch (refreshError) {
        console.error("Token刷新失败，需要重新登录:", refreshError);
        // 清除token缓存，强制重新登录
        tokenManager.updateToken('');
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

// ==================== 第三方请求实例 ====================
// 创建第三方请求实例，不包含项目特定的拦截器
export const createThirdPartyRequest = (config: RequestConfig = {}): RequestInstance => {
  const thirdPartyInstance = createInstance({
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    ...config
  });

  // 第三方请求只添加基础的错误处理，不添加token相关逻辑
  thirdPartyInstance.interceptors.response.use(
    (response) => {
      return response;
    },
    (error: RequestError) => {
      console.error('第三方请求错误:', error);
      return Promise.reject(error);
    }
  );

  return thirdPartyInstance;
};

// ==================== 导出 ====================
request.create = function create(config: RequestConfig) {
  return createInstance(config)
}

// 刷新token的API方法
export const refreshTokenApi = () => refreshManager.refreshToken();

// Token相关导出
export const getToken = () => tokenManager.getToken();
export const getTokenAsync = () => tokenManager.getTokenAsync();

export default request;
export { request, baseURL };
