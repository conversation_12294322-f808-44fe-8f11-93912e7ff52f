// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import getMacApps from './get-mac-app';
import fs from 'fs';
import path from 'path';

import os from 'os';

const icondir = path.join(os.tmpdir(), 'ProcessIcon');

const exists = fs.existsSync(icondir);
if (!exists) {
  fs.mkdirSync(icondir);
}

const isZhRegex = /[\u4e00-\u9fa5]/;

// 为了避免应用名称中包含空格、特殊字符或中文导致读写不一致，
// 统一对文件名进行 encodeURIComponent 处理。
async function getAppIcon(appPath: string, nativeImage: any, name: string) {
  try {
    const safeName = encodeURIComponent(name);
    const iconpath = path.join(icondir, `${safeName}.png`);
    const iconnone = path.join(icondir, `${safeName}.none`);
    const exists = fs.existsSync(iconpath);
    const existsnone = fs.existsSync(iconnone);
    if (exists) return true;
    if (existsnone) return false;
    // const appName: string = appPath.split('/').pop() || '';
    // const extname: string = path.extname(appName);
    // const appSubStr: string = appName.split(extname)[0];
    // const path1 = path.join(appPath, `/Contents/Resources/App.icns`);
    // const path2 = path.join(appPath, `/Contents/Resources/AppIcon.icns`);
    // const path3 = path.join(appPath, `/Contents/Resources/${appSubStr}.icns`);
    // const path4 = path.join(
    //   appPath,
    //   `/Contents/Resources/${appSubStr.replace(' ', '')}.icns`
    // );
    // let iconPath: string = path1;
    // if (fs.existsSync(path1)) {
    //   iconPath = path1;
    // } else if (fs.existsSync(path2)) {
    //   iconPath = path2;
    // } else if (fs.existsSync(path3)) {
    //   iconPath = path3;
    // } else if (fs.existsSync(path4)) {
    //   iconPath = path4;
    // } else {
    //   // 性能最低的方式
    //   const resourceList = fs.readdirSync(
    //     path.join(appPath, `/Contents/Resources`)
    //   );
    //   const iconName = resourceList.filter(
    //     (file) => path.extname(file) === '.icns'
    //   )[0];
    //   if (!iconName) {
    //     fs.writeFileSync(iconnone, '');
    //     return false;
    //   }
    //   iconPath = path.join(appPath, `/Contents/Resources/${iconName}`);
    // }
    await getMacApps.app2png(appPath, iconpath);
    
    // 检查转换后的文件是否存在
    if (fs.existsSync(iconpath)) {
      return true;
    } else {
      return false;
    }
  } catch (e) {
    // 创建无图标标记，避免重复尝试
    const safeName = encodeURIComponent(name);
    const iconnone = path.join(icondir, `${safeName}.none`);
    try {
      fs.writeFileSync(iconnone, '');
    } catch (writeError) {
      // 忽略写入错误
    }
    return false;
  }
}

export default async (nativeImage: any) => {
  let apps: any = await getMacApps.getApps();

  apps = apps.filter((app: any) => {
    const extname = path.extname(app.path);
    if (extname !== '.app' && extname !== '.prefPane') return false;
    
    // 过滤系统辅助功能代理和其他系统服务
    const appName = (app._name || '').toLowerCase();
    const systemAgents = [
      'axvisualsupportagent',
      'accessibilityvisualsagent', 
      'accessibility',
      'voiceover',
      'switchcontrol',
      'assistivecontrol',
      'universalaccess',
      'systemuiserver',
      'dock',
      'finder', // 可选：Finder 通常不需要手动启动
      'loginwindow',
      'windowserver',
      'coreaudiod',
    ];
    
    // 过滤系统路径
    const systemPaths = [
      '/application support/',
      '/applications/utilities/adobe',
      '/library/apple/',
      '/system/library/',
      '/library/image capture/',
      
    ];
    
    const appPath = app.path.toLowerCase();

    // 过滤用户目录
    if(appPath.startsWith('/users/')) {
      return false;
    }

    if (systemPaths.some(sysPath => appPath.includes(sysPath))) {
      return false;
    }
    
    // 过滤系统代理应用
    if (systemAgents.some(agent => appName.includes(agent))) {
      return false;
    }
    
    return true;
  });
  for (const app of apps) {
    if (await getAppIcon(app.path, nativeImage, app._name)) {
      // 转换为 base64 数据 URL，这样渲染进程可以正常显示
      const iconPath = path.join(
        os.tmpdir(),
        'ProcessIcon',
        `${encodeURIComponent(app._name)}.png`
      );
      
      try {
        const iconBuffer = fs.readFileSync(iconPath);
        const base64Icon = iconBuffer.toString('base64');
        app.icon = `data:image/png;base64,${base64Icon}`;
      } catch (readError) {
        app.icon = null;
      }
    } else {
      app.icon = null;
    }
    // todo getApp size
  }
  
  // 不再过滤掉没有图标的应用，让前端显示占位符
  // apps = apps.filter((app: any) => !!app.icon);

  apps = apps.map((app: any) => {
    const appName: any = app.path.split('/').pop();
    const extname = path.extname(appName);
    const appSubStr = appName.split(extname)[0];
    let fileOptions = {
      ...app,
      value: 'plugin',
      desc: app.path,
      pluginType: 'app',
      action: `open ${app.path.replace(/ /g, '\\ ') as string}`,
      keyWords: [appSubStr],
    };

    if (app._name && isZhRegex.test(app._name)) {
      // const [, pinyinArr] = translate(app._name);
      // const firstLatter = pinyinArr.map((py) => py[0]);
      // // 拼音
      // fileOptions.keyWords.push(pinyinArr.join(''));
      // // 缩写
      // fileOptions.keyWords.push(firstLatter.join(''));
      // 中文
      fileOptions.keyWords.push(app._name);
    }

    fileOptions = {
      ...fileOptions,
      name: app._name,
      names: JSON.parse(JSON.stringify(fileOptions.keyWords)),
    };
    return fileOptions;
  });

  return apps;
};
