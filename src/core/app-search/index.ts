import { isLinux, isMac, isWin } from '../../main/common'

// 定义应用搜索函数的类型
type AppSearchFunction = (nativeImage: any) => Promise<any[]>;

let appSearch: AppSearchFunction;
if (isMac) {
  appSearch = async (nativeImage) => (await import('./darwin')).default(nativeImage);
} else if (isWin) {
  appSearch = async () => {
      return (await import('./win')).default();
  };
} else if (isLinux) {
  appSearch = async () => (await import('./linux')).default();
}

export default appSearch;
  