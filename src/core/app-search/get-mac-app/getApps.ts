import { spawn } from 'child_process';
import plist from 'plist';

interface AppItem {
  _name: string;
  [key: string]: any;
}

interface PlistData {
  _items: AppItem[];
}

export default function getApps(
  resolve: (value: AppItem[] | boolean) => void, 
  reject: (reason?: any) => void, 
  filterByAppName: string | false = false
): void {
  let resultBuffer = Buffer.from([]);

  const profileInstalledApps = spawn('/usr/sbin/system_profiler', [
    '-xml',
    '-detailLevel',
    'mini',
    'SPApplicationsDataType',
  ]);

  profileInstalledApps.stdout.on('data', (chunckBuffer: Buffer) => {
    resultBuffer = Buffer.concat([resultBuffer, chunckBuffer]);
  });

  profileInstalledApps.on('exit', (exitCode: number | null) => {
    if (exitCode !== 0) {
      reject([]);
      return;
    }

    try {
      const [installedApps] = plist.parse(resultBuffer.toString()) as unknown as [PlistData];
      if (!filterByAppName) return resolve(installedApps._items);
      return resolve(
        installedApps._items.filter((apps) => apps._name === filterByAppName)
          .length !== 0
      );
    } catch (err) {
      console.warn('Error parsing installed apps:', err);
      reject(err);
    }
  });

  profileInstalledApps.on('error', (err: Error) => {
    console.warn('Error getting installed apps:', err);
    reject(err);
  });
}
