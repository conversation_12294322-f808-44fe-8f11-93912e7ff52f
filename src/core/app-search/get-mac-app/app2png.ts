import path from 'path';
import fs from 'fs';
import { exec } from 'child_process';

// eslint-disable-next-line @typescript-eslint/no-var-requires
const plist = require('simple-plist');

interface PlistData {
  CFBundleIconFile?: string;
}

const DEFAULT_ICON_PATH = '/System/Library/CoreServices/CoreTypes.bundle/Contents/Resources/GenericApplicationIcon.icns';

const getIconFile = (appFileInput: string): Promise<string> => {
  return new Promise((resolve) => {
    const plistPath = path.join(appFileInput, 'Contents', 'Info.plist');
    
    plist.readFile(plistPath, (err: any, data: PlistData) => {
      if (err || !data?.CFBundleIconFile) {
        return resolve(DEFAULT_ICON_PATH);
      }
      
      // 处理图标文件名，移除可能的扩展名
      let iconFileName = data.CFBundleIconFile;
      if (iconFileName.endsWith('.icns')) {
        iconFileName = iconFileName.slice(0, -5);
      }
      
      const iconFile = path.join(
        appFileInput,
        'Contents',
        'Resources',
        iconFileName
      );
      
      const iconFiles = [
        iconFile,
        iconFile + '.icns',
        iconFile + '.tiff',
        iconFile + '.png'
      ];
      
      const existedIcon = iconFiles.find((file) => {
        try {
          return fs.existsSync(file);
        } catch (error) {
          console.warn(`Error checking icon file ${file}:`, error);
          return false;
        }
      });
      
      resolve(existedIcon || DEFAULT_ICON_PATH);
    });
  });
};

// 安全转义shell参数
const escapeShellArg = (arg: string): string => {
  // 使用双引号包围，并转义内部的双引号、反斜杠和美元符号
  return '"' + arg.replace(/[\\$"`]/g, '\\$&') + '"';
};

// 创建一个简单的空白PNG文件作为最终fallback
const createEmptyPng = (pngFileOutput: string): void => {
  try {
    // 创建一个64x64的透明PNG，Base64编码的数据
    const emptyPngData = Buffer.from(
      'iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAdSURBVHhe7cEBAQAAAIKg/q92BAAAAAAAAAAA4A0AAAEAAUgCBQAAAABJRU5ErkJggg==',
      'base64'
    );
    
    fs.writeFileSync(pngFileOutput, emptyPngData);
    console.warn(`Created empty PNG fallback: ${pngFileOutput}`);
  } catch (error) {
    console.error(`Failed to create empty PNG fallback:`, error);
  }
};

const createDefaultPng = (pngFileOutput: string): Promise<void> => {
  return new Promise((resolve) => {
    const command = `sips -s format png ${escapeShellArg(DEFAULT_ICON_PATH)} --out ${escapeShellArg(pngFileOutput)} --resampleHeightWidth 64 64`;
    
    exec(command, (error) => {
      if (error) {
        console.warn(`Failed to create default icon:`, error.message);
        // 如果默认图标也失败了，创建一个空白PNG
        createEmptyPng(pngFileOutput);
      }
      // 无论成功失败都resolve，确保链条继续
      resolve();
    });
  });
};

const tiffToPng = (iconFile: string, pngFileOutput: string): Promise<void> => {
  return new Promise((resolve) => {
    // 检查源文件是否存在
    if (!fs.existsSync(iconFile)) {
      console.warn(`Icon file not found: ${iconFile}, using default`);
      return createDefaultPng(pngFileOutput).then(resolve);
    }

    const command = `sips -s format png ${escapeShellArg(iconFile)} --out ${escapeShellArg(pngFileOutput)} --resampleHeightWidth 64 64`;
    
    exec(command, (error) => {
      if (error) {
        console.warn(`Failed to convert icon ${iconFile} to PNG, using default:`, error.message);
        // 如果转换失败，尝试使用默认图标
        createDefaultPng(pngFileOutput).then(resolve);
      } else {
        resolve();
      }
    });
  });
};

const app2png = (appFileInput: string, pngFileOutput: string): Promise<void> => {
  return getIconFile(appFileInput)
    .then((iconFile) => {
    return tiffToPng(iconFile, pngFileOutput);
    })
    .catch((error) => {
      console.warn(`Failed to convert app icon for ${appFileInput}, using default:`, error.message);
      // 最后的fallback：直接使用默认图标或空白PNG
      return createDefaultPng(pngFileOutput);
    })
    .then(() => {
      // 确保文件存在，如果不存在则创建空白PNG
      if (!fs.existsSync(pngFileOutput)) {
        console.warn(`PNG file not created, creating empty fallback: ${pngFileOutput}`);
        createEmptyPng(pngFileOutput);
      }
  });
};

export default app2png;
