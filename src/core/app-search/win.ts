import fs from 'fs';
import path from 'path';
import os from 'os';
import { ipcMain, app } from 'electron';
import { APP } from '../../shared/ipc';

const { shell } = require('electron');

const fileLists: any[] = [];
const seenTargets = new Set<string>();
const isZhRegex = /[\u4e00-\u9fa5]/;

// 创建图标缓存目录
const iconCacheDir = path.join(os.tmpdir(), 'aido-app-icons');
if (!fs.existsSync(iconCacheDir)) {
  fs.mkdirSync(iconCacheDir, { recursive: true });
}

// 提取并缓存应用图标
async function extractAndCacheIcon(appName: string, targetPath: string): Promise<string | null> {
  try {
    // 检查目标文件是否存在
    if (!fs.existsSync(targetPath)) {
      return null;
    }

    // 使用应用名称和路径哈希作为缓存文件名，避免重复
    const pathHash = require('crypto').createHash('md5').update(targetPath).digest('hex').substring(0, 8);
    const iconFileName = `${encodeURIComponent(appName)}_${pathHash}.ico`;
    const iconCachePath = path.join(iconCacheDir, iconFileName);
    
    // 如果图标已经缓存，直接返回路径
    if (fs.existsSync(iconCachePath)) {
      return iconCachePath;
    }

    // 尝试提取图标
    try {
      const fileIcon = require('extract-file-icon');
      const iconBuffer = fileIcon(targetPath, 32);
      
      if (iconBuffer && iconBuffer.length > 0) {
        await fs.promises.writeFile(iconCachePath, iconBuffer);
        return iconCachePath;
      }
    } catch (iconError) {
      console.warn(`提取图标失败 ${appName} (${targetPath}):`, iconError.message);
      
      // 如果提取失败，尝试使用默认图标
      const defaultIconPath = path.join(process.cwd(), 'build', 'icon.png');
      if (fs.existsSync(defaultIconPath)) {
        const defaultIconBuffer = await fs.promises.readFile(defaultIconPath);
        await fs.promises.writeFile(iconCachePath, defaultIconBuffer);
        return iconCachePath;
      }
    }
  } catch (error) {
    console.warn(`处理图标失败 ${appName} (${targetPath}):`, error.message);
  }
  
  return null;
}

// 扫描开始菜单获取已安装程序
function scanStartMenu() {
  const startMenuPaths = [
    'C:\\ProgramData\\Microsoft\\Windows\\Start Menu\\Programs',
    path.join(os.homedir(), 'AppData\\Roaming\\Microsoft\\Windows\\Start Menu\\Programs'),
  ];

  startMenuPaths.forEach(menuPath => {
    if (fs.existsSync(menuPath)) {
      walkDirSync(menuPath);
    }
  });
}

function walkDirSync(dirPath: string): void {
  if (!fs.existsSync(dirPath)) return;

  const entries = fs.readdirSync(dirPath);
  for (const entry of entries) {
    const fullPath = path.join(dirPath, entry);
    let stats: fs.Stats;
    try {
      stats = fs.statSync(fullPath);
    } catch (err) {
      continue;
    }

    if (stats.isFile() && path.extname(entry).toLowerCase() === '.lnk') {
      try {
        const detail = shell.readShortcutLink(fullPath);
        const targetPath = detail.target;
        
        if (targetPath && !targetPath.toLowerCase().includes('unin') && !seenTargets.has(targetPath)) {
          seenTargets.add(targetPath);
          
          const appName = entry.split('.')[0];
          const keyWords = [appName];
          
          if (!isZhRegex.test(appName)) {
            const firstLetters = appName.split(' ').map(n => n[0]).join('');
            keyWords.push(firstLetters);
          }

          const appInfo = {
            id: detail.appUserModelId,
            value: 'plugin',
            desc: detail.description,
            path: targetPath,
            type: 'app',
            icon: null, // 初始为null，稍后异步设置
            pluginType: 'app',
            action: `start "dummyclient" "${targetPath}"`,
            keyWords,
            name: appName,
            _name: appName, // 添加 _name 属性供搜索使用
            names: JSON.parse(JSON.stringify(keyWords)),
          };

          fileLists.push(appInfo);
          
          // 使用默认图标
          appInfo.icon = './build/icon.png';
          
          // 异步提取图标，不阻塞主流程
          setImmediate(async () => {
            const iconPath = await extractAndCacheIcon(appName, targetPath);
            if (iconPath) {
              // 正确处理 Windows 路径格式，确保使用三个斜杠并将反斜杠转换为正斜杠
              const formattedPath = iconPath.replace(/\\/g, '/');
              appInfo.icon = `file:///${formattedPath}`;
              console.log(`设置图标路径: ${appInfo.icon}`);
              
              // 发送图标更新事件到渲染进程
              try {
                // 确保路径格式正确
                const formattedPath = iconPath.replace(/\\/g, '/');
                const iconUrl = `file:///${formattedPath}`;
                
                // 使用正确的 IPC 事件名称
                ipcMain.emit(APP.ICON_UPDATED, null, { 
                  appId: appInfo.id, 
                  iconPath: iconUrl,
                  timestamp: Date.now() // 添加时间戳确保更新能被识别
                });
                
                console.log(`发送图标更新事件: ${appInfo.id} -> ${iconUrl}`);
              } catch (err) {
                console.warn('发送图标更新事件失败:', err);
              }
            }
          });
        }
      } catch (e) {
        // 忽略无法读取的快捷方式
      }
    } else if (stats.isDirectory()) {
      walkDirSync(fullPath);
    }
  }
}

export default async () => {
  fileLists.length = 0;
  seenTargets.clear();
  
  try {
    const time = (new Date()).getTime();
    console.log('---开始扫描开始菜单获取应用程序列表，当前时间:', time);
    
    // 只使用开始菜单扫描
    scanStartMenu();
    
    console.log('---开始菜单扫描完成，用时:', ((new Date()).getTime() - time), 'ms');
    console.log(`成功获取到 ${fileLists.length} 个应用`);
    return fileLists;
  } catch (error) {
    console.error('扫描开始菜单失败:', error);
    return fileLists;
  }
};