{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "调试主进程",
      "type": "node",
      "request": "launch",
      // 使用 npx 执行 electron-forge start 命令
      "runtimeExecutable": "npx",
      "runtimeArgs": [
        "electron-forge",
        "start",
        "--", // 将 --inspect 和 --remote-debugging-port 传递给 Electron 而不是 electron-forge
        "--inspect", // 启用 Node.js 主进程调试器
        "--remote-debugging-port=9223" // 为渲染进程指定调试端口
      ],
      "cwd": "${workspaceFolder}",
      "outputCapture": "std",
      // Electron Forge + Vite 启动可能需要一些时间
      "timeout": 60000, 
      // 移除自动附加子进程，避免重复启动
      // "autoAttachChildProcesses": true,
      "sourceMaps": true, // 确保源码映射被加载
      "env": {
        "NODE_ENV": "development" // 确保是开发环境
      }
    },
    {
      "name": "调试渲染进程",
      // 更改类型为 pwa-chrome 以提高兼容性
      "type": "chrome", 
      "request": "attach",
      // 明确指定我们在主进程启动时设置的端口
      "port": 9223, 
      "webRoot": "${workspaceFolder}/src/renderer",
      // Vite 开发服务器的 URL (如果渲染器直接加载这个URL)
      // 如果 Electron 加载的是 file:// 路径，可能需要调整 webRoot 或添加 pathMapping
      "url": "http://localhost:5173", 
      "timeout": 40000, // 稍微增加超时时间
      "sourceMaps": true,
      "skipFiles": [
        "<node_internals>/**",
        "node_modules/**"
      ]
    },
    {
        "name": "附加到主进程",
        "type": "node",
        "request": "attach",
        // Electron 主进程的默认调试端口
        "port": 9229, 
        "sourceMaps": true,
        "cwd": "${workspaceFolder}",
        "localRoot": "${workspaceFolder}",
        "remoteRoot": "${workspaceFolder}",
        "skipFiles": [
            "<node_internals>/**",
            "node_modules/**"
        ]
    }
  ],
  // 移除 compounds 配置，避免同时启动多个调试器
  "compounds": [
    {
      "name": "调试主进程+渲染进程",
      "configurations": ["调试主进程", "调试渲染进程"],
      "stopAll": true
    }
  ]
} 