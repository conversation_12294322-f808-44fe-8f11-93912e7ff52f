# IPC通信规则
- 主进程和渲染进程共用的数据结构定义，在src\shared\types\目录中，共用的interface或type需要声明在这里。
- IPC通信的所有API及IPC通道名定义在src\shared\ipc.ts中，避免在代码中使用硬编码的方式命名IPC通道名。
- 主进程处理渲染进程发送的IPC事件，需要在src\main\ipc\handlers\目录的对应文件中处理。
- 主进程发送给渲染进程的IPC通讯，需要在src\main\ipc\emitters\目录的对应文件中处理，不要使用window.webContents.send和window.electron.ipcRenderer.on的方式来通信，应该使用emit方式，参考src\main\ipc\emitters\app-emitter.ts。


# 渲染进程规则
- 渲染进程使用了radix-ui，基础组件定义在src\renderer\components\ui\目录中，生成新的UI界面时优先复用基础组件，避免重复定义。
- hooks定义在src\renderer\hooks\目录中。
- 渲染进程使用了zustand进行状态管理，store定义在src\renderer\stores\目录中。