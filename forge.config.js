const { MakerSquirrel } = require('@electron-forge/maker-squirrel');
const { MakerZIP } = require('@electron-forge/maker-zip');
const { MakerDeb } = require('@electron-forge/maker-deb');
const { MakerRpm } = require('@electron-forge/maker-rpm');
const { VitePlugin } = require('@electron-forge/plugin-vite');
const { FusesPlugin } = require('@electron-forge/plugin-fuses');
const { FuseVersion, FuseV1Options } = require('@electron/fuses');
const { MakerDMG } = require('@electron-forge/maker-dmg');
const { AutoUnpackNativesPlugin } = require('@electron-forge/plugin-auto-unpack-natives');
const path = require('path');
const ForgeExternalsPlugin = require('@timfish/forge-externals-plugin');

// 获取平台特定图标和资源
function getPlatformIcons() {
  const platform = process.platform;

  // 额外资源配置
  const extraResources = [
    // 添加配置文件目录
    'config'
  ];

  // 平台特定的bun工具和uv/uvx工具
  if (platform === 'win32') {
    extraResources.push('build/runtime/bun-windows-x64.zip');
    extraResources.push('build/runtime/uv-windows-x64.zip');
  } else if (platform === 'darwin') {
    extraResources.push(process.arch === 'arm64' ? 'build/runtime/bun-darwin-aarch64.zip' : 'build/runtime/bun-darwin-x64.zip');
    extraResources.push(process.arch === 'arm64' ? 'build/runtime/uv-darwin-aarch64.zip' : 'build/runtime/uv-darwin-x64.zip');
  } else if (platform === 'linux') {
    extraResources.push('build/runtime/bun-linux-x64.zip');
    extraResources.push('build/runtime/uv-linux-x64.zip');
  }

  return { extraResources };
}

const { extraResources } = getPlatformIcons();

module.exports = {
  packagerConfig: {
    asar: true,
    prune: true,
    // 提供一个默认 ignore 函数，避免 @timfish/forge-externals-plugin 期望的 existingIgnoreFn 为空时报错
    ignore: (filePath) => {
      if (filePath === '' || filePath === '/') {
        return false;
      }

      const ignorePatterns = [
        /^(\/\w+)*\/\.DS_Store$/,
        /^\/src($|\/)/,
        /^\/build($|\/)/,
        /^\/\.vscode($|\/)/,
        /\.map$/,
        /^\/forge\.config\.js$/,
        /^\/vite\..*\.ts$/,
        /^\/tsconfig\.json$/,
        /^\/postcss\.config\.js$/,
        /^\/tailwind\.config\.js$/,
        /^\/README\.md$/,
        /^\/PRD\.md$/,
        /^\/TASKS\.md$/,
        /^\/components\.json$/,
      ];

      return ignorePatterns.some(pattern => pattern.test(filePath));
    },
    extraResource: extraResources,
    // 平台特定配置
    ...((process.platform === 'darwin') ? {
      appBundleId: 'site.aido.app',
      darwinDarkModeSupport: true,
      // macOS图标额外配置
      icon: path.resolve(__dirname, 'build/icon.icns'),
      // 确保应用元数据正确
      appCategoryType: 'public.app-category.developer-tools',
      // 开发阶段的代码签名配置（自签名）
      osxSign: {
        identity: 'Developer ID Application',
        'hardened-runtime': true,
        'gatekeeper-assess': false,
        entitlements: 'build/entitlements.plist',
        'entitlements-inherit': 'build/entitlements.plist'
      },
      extendInfo: {
        CFBundleDisplayName: 'Aido',
        CFBundleExecutable: 'Aido',
        NSSupportsSecureRestorableState: true,
        // 添加辅助功能权限说明
        NSAppleEventsUsageDescription: 'Aido 需要辅助功能权限来让AI帮您自动执行某些系统操作。',
        NSSystemAdministrationUsageDescription: 'Aido 需要系统管理权限来让AI帮您执行某些系统级操作。',
      }
    } : {}),
    ...((process.platform === 'win32') ? {
      win32metadata: {
        CompanyName: 'SilliconGeek',
        FileDescription: 'SilliconGeek',
        ProductName: 'Aido',
      }
    } : {})
  },
  rebuildConfig: {},
  makers: [
    new MakerSquirrel({
      iconUrl: path.resolve(__dirname, 'build/icon.ico'),
      setupIcon: path.resolve(__dirname, 'build/icon.ico'),
    }), 
    new MakerZIP({}, ['darwin']), 
    new MakerRpm({
      options: {
        icon: path.resolve(__dirname, 'build/icon.png')
      }
    }), 
    new MakerDeb({
      options: {
        icon: path.resolve(__dirname, 'build/icon.png')
      }
    }),
    new MakerDMG({
      icon: path.resolve(__dirname, 'build/icon.icns')
    })
  ],
  plugins: [
    new AutoUnpackNativesPlugin(),
    new ForgeExternalsPlugin({
      externals: ['better-sqlite3'],
      includeDeps: true,
    }),
    new VitePlugin({
      build: [
        {
          entry: 'src/main/main.ts',
          config: 'vite.main.config.ts',
        },
        {
          entry: 'src/preload/index.ts',
          config: 'vite.preload.config.ts',
        },
      ],
      renderer: [
        {
          name: 'main_window',
          config: 'vite.renderer.config.ts',
        },
      ]
    }),
    new FusesPlugin({
      version: FuseVersion.V1,
      [FuseV1Options.RunAsNode]: false,
      [FuseV1Options.EnableCookieEncryption]: true,
      [FuseV1Options.EnableNodeOptionsEnvironmentVariable]: false,
      [FuseV1Options.EnableNodeCliInspectArguments]: false,
      [FuseV1Options.EnableEmbeddedAsarIntegrityValidation]: true,
      [FuseV1Options.OnlyLoadAppFromAsar]: true,
    }),
  ],
};