{"env": {"browser": true, "es6": true, "node": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended", "plugin:import/recommended", "plugin:import/electron", "plugin:import/typescript"], "parser": "@typescript-eslint/parser", "settings": {"import/resolver": {"node": {}}}, "rules": {"import/no-unresolved": "off", "@typescript-eslint/no-var-requires": "off", "@typescript-eslint/no-inferrable-types": "off", "@typescript-eslint/no-empty-function": "off"}}