import { defineConfig } from 'vite';
import path from 'path';

// https://vitejs.dev/config
export default defineConfig(({ mode }) => ({
  build: {
    outDir: '.vite/build',
    lib: {
      entry: 'src/main/main.ts',
      formats: ['cjs'],
      fileName: () => 'main.js',
    },
    rollupOptions: {
      external: [
        'electron', 'better-sqlite3', 'canvas', 'jsdom', 'original-fs',
        'util', 'fs', 'path', 'os', 'url', 'http', 'https', 'net', 'tls',
        'zlib', 'stream', 'assert', 'crypto', 'child_process', 'constants',
        'domain', 'process', 'buffer', 'fs/promises', 'node:fs', 'node:path', 'node:os', 'node:url', 'node:http', 'node:https', 'node:net', 'node:tls', 'node:zlib', 'node:stream', 'node:assert', 'node:crypto', 'node:child_process', 'node:constants', 'node:domain', 'node:process', 'node:buffer', 'node:util',
        'node-machine-id',
      ],
    },
    sourcemap: mode === 'development',
    minify: mode === 'production',
  },
  resolve: {
    alias: {
      '@main': path.resolve(__dirname, 'src/main'),
      '@src': path.resolve(__dirname, 'src')
    }
  },
  plugins: [
    // 暂时禁用自动重启插件，测试是否导致重复执行
    // {
    //   name: 'electron-forge-restart',
    //   closeBundle() {
    //     // 当 Vite 完成主进程构建后，发送 'rs' 命令来重启 Electron Forge
    //     // 这模拟了在控制台中手动输入 'rs' 的行为
    //     if (process.env.NODE_ENV !== 'production') {
    //       process.stdin.emit('data', 'rs');
    //     }
    //   },
    // },
  ],
}));
