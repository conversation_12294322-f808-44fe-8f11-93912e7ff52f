{"name": "<PERSON><PERSON>", "productName": "<PERSON><PERSON>", "version": "1.0.0", "description": "My Electron application description", "main": ".vite/build/main.js", "scripts": {"start": "cross-env NODE_ENV=development FORCE_COLOR=1 electron-forge start", "start:win": "chcp 65001 && cross-env NODE_ENV=development FORCE_COLOR=1 electron-forge start", "dev": "npm run start", "package": "cross-env NODE_ENV=production electron-forge package", "make": "cross-env NODE_ENV=production electron-forge make", "publish": "electron-forge publish", "lint": "eslint --ext .ts,.tsx .", "shadcn": "npx shadcn-ui@latest"}, "keywords": [], "author": {"name": "马亮", "email": "<EMAIL>"}, "license": "MIT", "engines": {"node": ">=18.0.0 <19.0.0"}, "overrides": {"eventsource-parser": "^3.0.2"}, "devDependencies": {"@electron-forge/cli": "^7.8.0", "@electron-forge/maker-deb": "^7.8.0", "@electron-forge/maker-dmg": "^7.8.0", "@electron-forge/maker-rpm": "^7.8.0", "@electron-forge/maker-squirrel": "^7.8.0", "@electron-forge/maker-zip": "^7.8.0", "@electron-forge/plugin-auto-unpack-natives": "^7.8.0", "@electron-forge/plugin-fuses": "^7.8.0", "@electron-forge/plugin-vite": "^7.8.0", "@electron/fuses": "^1.8.0", "@reduxjs/toolkit": "^2.2.5", "@timfish/forge-externals-plugin": "^0.2.1", "@types/adm-zip": "^0.5.7", "@types/better-sqlite3": "^7.6.11", "@types/debug": "^4.1.12", "@types/diff": "^7", "@types/jsdom": "^21.1.7", "@types/lodash": "^4.17.16", "@types/markdown-it": "^14.1.2", "@types/mime-types": "^3.0.1", "@types/minimatch": "^5.1.2", "@types/node": "^20.11.10", "@types/plist": "^3.0.5", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@types/styled-components": "^5.1.19", "@types/turndown": "^5.0.5", "@types/uuid": "^10.0.0", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "cross-env": "^7.0.3", "debug": "^4.3.4", "diff": "^7.0.0", "electron": "^28.0.0", "eslint": "^8.57.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "express": "^5.1.0", "http-proxy-middleware": "^3.0.5", "jsdom": "^26.0.0", "llama-tokenizer-js": "^1.2.1", "llama3-tokenizer-js": "^1.0.0", "lodash": "^4.17.21", "postcss": "^8.4.33", "sass": "^1.86.3", "simple-plist": "0.2.1", "styled-components": "^5.3.3", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "tiktoken": "^1.0.10", "ts-node": "^10.9.2", "typeid-js": "^1.2.0", "typescript": "^5.8.3", "uuid": "^10.0.0", "vite": "^5.4.18"}, "dependencies": {"@ant-design/x": "^1.2.0", "@google/generative-ai": "^0.24.0", "@mcpcn/mcp-go-off-work": "^1.0.5", "@mcpcn/mcp-open-app": "^1.0.12", "@mcpcn/mcp-open-web": "^1.0.1", "@modelcontextprotocol/sdk": "^1.10.2", "@playwright/mcp": "^0.0.31", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.4", "adm-zip": "^0.5.16", "antd": "^5.24.7", "apple-mcp": "^0.2.7", "async-mutex": "^0.5.0", "axios": "^1.8.4", "better-sqlite3": "11.1.1", "bufferutil": "^4.0.9", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^0.2.1", "electron-log": "^4.4.8", "electron-squirrel-startup": "^1.0.1", "electron-store": "^8.1.0", "electron-window-state": "^5.0.3", "extract-file-icon": "^0.3.2", "file-type": "^20.5.0", "font-awesome": "^4.7.0", "highlight.js": "^11.11.1", "i18next": "^23.10.0", "i18next-browser-languagedetector": "^8.0.4", "js-web-screen-shot": "^1.9.9-rc.26", "lucide-react": "^0.363.0", "markdown-it": "^14.1.0", "mathjs": "^14.5.2", "mermaid": "^11.6.0", "mime-types": "^3.0.1", "minimatch": "^3.1.2", "node-machine-id": "^1.1.12", "openai": "^4.93.0", "opus-decoder": "^0.7.10", "pinyin-match": "^1.2.4", "plist": "^3.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^14.0.5", "react-router-dom": "^6.22.1", "tailwind-merge": "^2.2.1", "turndown": "^7.2.0", "utf-8-validate": "^5.0.10", "ws": "^8.18.2", "zustand": "^4.5.1"}}