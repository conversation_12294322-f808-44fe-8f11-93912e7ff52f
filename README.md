# Aido - 高效AI助手

## 项目介绍

Aido

## 开发环境

- Node.js >= 16
- Yarn (推荐) 或 NPM

## 安装依赖

```bash
yarn install
# 或
npm install
```

## 运行开发环境

```bash
yarn start
# 或
npm run start
```

## VS Code 调试指南

本项目使用Electron Forge构建，VS Code调试配置已针对其特性专门优化：

1. 在VS Code中打开命令面板(`Ctrl+Shift+P`或`Cmd+Shift+P`)
2. 输入`Debug: Select and Start Debugging`或直接按`F5`
3. 选择调试配置：
   - **调试主进程**：仅调试Electron主进程(Node.js部分)
   - **调试渲染进程**：仅调试渲染进程(React前端部分)
   - **调试全部 (Main + Renderer)**：同时调试两个进程

> **注意**: 调试时不需要提前运行`yarn start`，调试配置会直接启动应用。

### 最新调试配置说明

我们已更新调试配置，直接使用Electron启动编译后的`main.js`文件：

1. **修复了主进程调试**:
   - 不再使用electron-forge启动，而是直接使用electron启动编译后文件
   - 添加了源码中的`debugger;`语句确保断点生效
   - 如果需要在特定位置断点，请在代码中添加`debugger;`语句

2. **如何使用**:
   - 选择"调试主进程"，应用会在文件顶部的`debugger;`语句处停下
   - 查看调试控制台的输出日志，了解环境变量和启动参数
   - 程序会在2秒后执行`testBreakpoint()`函数，此处也有`debugger;`语句

### 可能的问题和解决方法

如果调试时遇到问题：

1. **确保先编译项目**：
   - 在调试前先运行`yarn start`然后终止，以确保`.vite/build/main.js`已生成
   - 或运行`yarn build`编译项目

2. **使用明确的调试语句**：
   - 添加`debugger;`语句是确保断点生效的最可靠方法
   - 查看控制台输出以确认应用正在以调试模式运行

3. **检查源映射**：
   - 确保`vite.main.config.ts`中启用了`sourcemap: true`配置
   - VS Code设置中的`outFiles`指向了正确的编译后JavaScript文件

4. **调试渲染进程**：
   - 主进程启动后，渲染进程调试会自动附加
   - 选择"调试全部"可以同时调试两个进程

## 构建应用

```bash
yarn package  # 仅打包
yarn make     # 构建安装包
``` 