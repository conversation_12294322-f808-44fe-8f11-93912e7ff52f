// ========== 代理目标配置 ===========
const API_TARGET = 'https://www.mcpcn.cc';
const XIAOZHI_TARGET = 'http://192.168.100.21:8002';
// ===================================

const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');

const app = express();

// 全局请求日志中间件
app.use((req, res, next) => {
next();
});

// 根路径提示
app.get('/', (req, res) => {
res.send('代理服务器已启动，请使用正确的HTTP方法访问 /api/xxx 或 /xiaozhi/ota/xxx 路径');
});

// 1. API 代理 - 使用函数形式的路径匹配
console.log('创建 API 代理中间件...');
app.use((req, res, next) => {
// 检查是否是 /api 开头的请求
if (req.path.startsWith('/api')) {
  // 创建代理中间件
  const apiProxy = createProxyMiddleware({
    target: API_TARGET,
    changeOrigin: true,
    secure: false,
    logLevel: 'debug',
    onProxyReq: (proxyReq, req, res) => {
    },
    onProxyRes: (proxyRes, req, res) => {
      console.log('=== 代理响应 /api ===');
      console.log('状态码:', proxyRes.statusCode);
      console.log('响应头:', proxyRes.headers);
    },
    onError: (err, req, res) => {
      console.error('=== 代理错误 /api ===');
      console.error('错误类型:', err.code || 'UNKNOWN');
      console.error('错误信息:', err.message);
      console.error('请求URL:', req.originalUrl);
      
      if (!res.headersSent) {
        res.writeHead(500, {
          'Content-Type': 'application/json',
        });
        res.end(JSON.stringify({
          error: '代理请求错误',
          message: err.message,
          code: err.code,
          url: req.originalUrl
        }));
      }
    }
  });
  
  // 执行代理
  return apiProxy(req, res, next);
}

// 不是 /api 路径，继续下一个中间件
next();
});

// 2. xiaozhi 代理
console.log('创建 xiaozhi 代理中间件...');
app.use((req, res, next) => {
// 检查是否是 /xiaozhi/ota 开头的请求
if (req.path.startsWith('/xiaozhi/ota')) {
  // 创建代理中间件
  const xiaozhi = createProxyMiddleware({
    target: XIAOZHI_TARGET,
    changeOrigin: true,
    logLevel: 'debug',
    onProxyReq: (proxyReq, req, res) => {
    },
    onProxyRes: (proxyRes, req, res) => {
    },
    onError: (err, req, res) => {
      if (!res.headersSent) {
        res.writeHead(500, {
          'Content-Type': 'application/json',
        });
        res.end(JSON.stringify({
          error: '代理请求错误',
          message: err.message
        }));
      }
    }
  });
  
  // 执行代理
  return xiaozhi(req, res, next);
}

// 不是 /xiaozhi/ota 路径，继续下一个中间件
next();
});

// 添加一个测试路由
app.get('/test', (req, res) => {
res.json({
  message: '服务器工作正常',
  timestamp: new Date().toISOString(),
  url: req.url,
  method: req.method
});
});

// 捕获所有其他请求
app.use((req, res) => {
res.status(404).json({
  error: '路径未找到',
  method: req.method,
  url: req.url,
  message: '请使用 /api/* 或 /xiaozhi/ota/* 路径',
  availablePaths: ['/api/*', '/xiaozhi/ota/*', '/test']
});
});

// 错误处理中间件
app.use((err, req, res, next) => {
if (!res.headersSent) {
  res.status(500).json({
    error: '服务器内部错误',
    message: err.message
  });
}
});

// 启动服务
const PORT = 3001;
const server = app.listen(PORT, () => {
console.log(`代理服务器已启动，端口: ${PORT}`);
console.log(`访问地址: http://localhost:${PORT}`);
});

// 优雅关闭
process.on('SIGTERM', () => {
console.log('收到 SIGTERM 信号，正在关闭服务器...');
server.close(() => {
  console.log('服务器已关闭');
});
});