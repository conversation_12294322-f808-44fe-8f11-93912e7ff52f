#!/usr/bin/env node

/**
 * 此脚本用于生成macOS应用所需的.icns图标文件
 * 需要安装以下依赖：
 * npm install png2icons --save-dev
 * 
 * 使用方法：
 * node build-mac-icon.js
 */

const path = require('path');
const fs = require('fs');
const { execSync } = require('child_process');

// 检查操作系统
const isMac = process.platform === 'darwin';

// macOS上使用原生工具，其他系统使用npm包
if (isMac) {
  console.log('在macOS上使用系统工具生成.icns文件...');
  
  // 检查源PNG图标是否存在
  const sourcePng = path.join(__dirname, 'build', 'icon', 'icon.png');
  const iconSetDir = path.join(__dirname, 'build', 'icon', 'icon.iconset');
  const outputIcns = path.join(__dirname, 'build', 'icon', 'icon.icns');
  
  if (!fs.existsSync(sourcePng)) {
    console.error('错误: 源PNG图标不存在:', sourcePng);
    process.exit(1);
  }
  
  // 创建iconset目录
  if (!fs.existsSync(iconSetDir)) {
    fs.mkdirSync(iconSetDir, { recursive: true });
  }
  
  // 生成不同尺寸的图标
  const sizes = [16, 32, 64, 128, 256, 512, 1024];
  
  sizes.forEach(size => {
    // 标准尺寸
    console.log(`生成 ${size}x${size} 图标...`);
    execSync(`sips -z ${size} ${size} ${sourcePng} --out "${iconSetDir}/icon_${size}x${size}.png"`);
    
    // 2x尺寸(@2x)
    if (size <= 512) {
      const doubleSize = size * 2;
      console.log(`生成 ${size}x${size}@2x (${doubleSize}x${doubleSize}) 图标...`);
      execSync(`sips -z ${doubleSize} ${doubleSize} ${sourcePng} --out "${iconSetDir}/icon_${size}x${size}@2x.png"`);
    }
  });
  
  // 使用iconutil将iconset转换为icns
  console.log('转换iconset为icns文件...');
  execSync(`iconutil -c icns "${iconSetDir}" -o "${outputIcns}"`);
  
  console.log(`图标已成功生成: ${outputIcns}`);
} else {
  // 非macOS系统
  console.log('非macOS系统，请手动准备.icns文件或在macOS上运行此脚本');
  console.log('或者使用在线工具转换: https://cloudconvert.com/png-to-icns');
} 